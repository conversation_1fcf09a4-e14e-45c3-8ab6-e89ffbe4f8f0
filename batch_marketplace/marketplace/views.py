from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden, HttpResponse
from django.conf import settings
import json
from django.utils import timezone
from django.db.models import Q
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.urls import reverse
from django.contrib.auth.models import User

from .models import (
    UserProfile, Item, Order,
    OrderItem, Payment, ChatMessage,
    Report, OrderStatusHistory,
    Ticket, TicketMessage, BalanceTopUp,
    Notification, Withdrawal, STATUS_CHOICES,
    Announcement
)
from .forms import (
    UserRegistrationForm,
    PaymentForm, ChatMessageForm, ReportForm,
    ReportResolutionForm, PaymentVerificationForm,
    TopUpBalanceForm, TicketForm, SupportTicketForm,
    TicketMessageForm, WithdrawalForm, WithdrawalProcessForm,
    UserRoleForm, ItemForm, ProfileForm, CustomPasswordChangeForm,
    AnnouncementForm
)
from .utils import verify_tron_transaction, create_nowpayments_payment, get_payment_status, verify_nowpayments_ipn_request

# Home page view
def index(request):
    products = Item.objects.filter(is_active=True, is_sold=False).order_by('-created_at')[:6]

    # Get active announcements for all users, sorted by most recently added first
    active_announcements = Announcement.objects.filter(is_active=True).order_by('-created_at')[:3]

    # Use different templates based on authentication status
    if request.user.is_authenticated:
        # For buyer role, get additional stats
        if hasattr(request.user, 'profile') and request.user.profile.role == 'buyer':
            # Get total orders count
            total_orders = Order.objects.filter(buyer=request.user).count()

            # Get total balance paid
            from django.db.models import Sum
            total_paid = Order.objects.filter(buyer=request.user).aggregate(Sum('total_amount'))['total_amount__sum'] or 0

            # Get total reports count
            total_reports = Report.objects.filter(created_by=request.user).count()

            # Get recent activity data
            recent_orders = Order.objects.filter(buyer=request.user).order_by('-created_at')[:5]
            recent_reports = Report.objects.filter(created_by=request.user).order_by('-created_at')[:5]
            recent_tickets = Ticket.objects.filter(user=request.user).order_by('-created_at')[:5]
            recent_notifications = Notification.objects.filter(user=request.user).order_by('-created_at')[:10]

            # Get last top-up - handle case where method field might not exist in the database
            try:
                last_topup = BalanceTopUp.objects.filter(user=request.user).order_by('-created_at').values('id', 'amount', 'verified', 'created_at').first()
            except Exception:
                last_topup = None

            context = {
                'products': products,
                'total_orders': total_orders,
                'total_paid': total_paid,
                'total_reports': total_reports,
                'recent_orders': recent_orders,
                'recent_reports': recent_reports,
                'recent_tickets': recent_tickets,
                'recent_notifications': recent_notifications,
                'last_topup': last_topup,
                'active_announcements': active_announcements
            }
        elif hasattr(request.user, 'profile') and request.user.profile.role == 'seller':
            # Calculate total paid orders
            from django.db.models import Count, Sum
            order_items = OrderItem.objects.filter(product__seller=request.user)
            total_paid_orders = order_items.filter(status='paid').count()

            # Calculate total gained balance (sum of all paid orders)
            total_gained_balance = order_items.filter(status='paid').aggregate(Sum('price'))['price__sum'] or 0

            # Get total reports count
            total_reports = Report.objects.filter(order_item__product__seller=request.user).count()

            # Get open reports count
            open_reports_count = Report.objects.filter(
                order_item__product__seller=request.user,
                resolved=False
            ).count()

            # Get recent activity data
            recent_orders = OrderItem.objects.filter(
                product__seller=request.user,
                status='paid'
            ).order_by('-order__created_at')[:5]

            recent_reports = Report.objects.filter(
                order_item__product__seller=request.user
            ).order_by('-created_at')[:5]

            recent_notifications = Notification.objects.filter(
                user=request.user
            ).order_by('-created_at')[:10]

            context = {
                'products': products,
                'total_paid_orders': total_paid_orders,
                'total_gained_balance': total_gained_balance,
                'total_reports': total_reports,
                'open_reports_count': open_reports_count,
                'recent_orders': recent_orders,
                'recent_reports': recent_reports,
                'recent_notifications': recent_notifications,
                'active_announcements': active_announcements
            }
        elif hasattr(request.user, 'profile') and request.user.profile.role == 'support':
            # Get all open tickets
            open_tickets = Ticket.objects.filter(
                status__in=['open', 'in_progress', 'waiting']
            ).order_by('-created_at')

            # Get all open reports
            open_reports = Report.objects.filter(resolved=False).order_by('-created_at')

            # Get total reports count
            total_reports = Report.objects.filter(resolved=False).count()

            # Get recent tickets and reports
            recent_tickets = Ticket.objects.filter(
                status__in=['open', 'in_progress', 'waiting']
            ).order_by('-created_at')[:5]

            recent_reports = Report.objects.filter(resolved=False).order_by('-created_at')[:5]

            recent_notifications = Notification.objects.filter(
                user=request.user
            ).order_by('-created_at')[:10]

            context = {
                'products': products,
                'active_announcements': active_announcements,
                'open_tickets': open_tickets,
                'open_reports': open_reports,
                'total_reports': total_reports,
                'recent_tickets': recent_tickets,
                'recent_reports': recent_reports,
                'recent_notifications': recent_notifications
            }
        else:
            context = {
                'products': products,
                'active_announcements': active_announcements
            }

        # Show welcome dashboard with Tailwind CSS and comfortable color gradients
        return render(request, 'marketplace/welcome_dashboard_tailwind.html', context)
    else:
        # Redirect to login page if not authenticated
        return redirect('login')

# Authentication views
def register_view(request):
    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            # Specify the backend when logging in the user
            from django.contrib.auth import authenticate
            # Authenticate the user with the username (more reliable than email)
            authenticated_user = authenticate(
                username=user.username,  # Use the generated username
                password=form.cleaned_data['password1']
            )
            if authenticated_user:
                login(request, authenticated_user)
                messages.success(request, "Registration successful!")

                if user.profile.role == 'buyer':
                    return redirect('buyer_dashboard')
                else:
                    return redirect('seller_dashboard')
    else:
        form = UserRegistrationForm()

    return render(request, 'marketplace/register_tailwind.html', {'form': form})

def login_view(request):
    if request.method == 'POST':
        email = request.POST.get('username')  # Field is still named 'username' in the form
        password = request.POST.get('password')
        user = authenticate(username=email, password=password)  # Our backend will check if it's an email

        if user is not None:
            login(request, user)
            messages.success(request, "Login successful!")

            # Redirect based on user role
            try:
                profile = UserProfile.objects.get(user=user)
                if profile.role == 'admin':
                    return redirect('admin_dashboard')
                elif profile.role == 'support':
                    return redirect('index')
                elif profile.role == 'seller':
                    return redirect('seller_dashboard')
                else:
                    return redirect('buyer_dashboard')
            except UserProfile.DoesNotExist:
                # If profile doesn't exist, create one with default role
                UserProfile.objects.create(user=user, role='buyer')
                return redirect('buyer_dashboard')
        else:
            messages.error(request, "Invalid email or password.")

    return render(request, 'marketplace/login_tailwind.html')

def logout_view(request):
    logout(request)
    messages.success(request, "You have been logged out successfully.")
    return redirect('index')

# Dashboard views
@login_required
def buyer_dashboard(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'buyer':
        messages.error(request, "Access denied. You need a buyer account.")
        return redirect('index')

    orders = Order.objects.filter(buyer=request.user).order_by('-created_at')

    # We don't need to manually set order_status as it's a property in the Order model
    # The template can directly use order.order_status

    return render(request, 'marketplace/buyer_dashboard.html', {'orders': orders})

@login_required
def get_order_details(request, order_id):
    """
    View to get order details for the modal.
    Returns JSON with order details.
    """
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'buyer':
        return JsonResponse({'error': 'Access denied'}, status=403)

    # Get the order and check if it belongs to the current user
    order = get_object_or_404(Order, id=order_id)
    if order.buyer != request.user:
        return JsonResponse({'error': 'Access denied'}, status=403)

    # Get order items
    items = []
    for item in order.items.all():
        # Extract company from specifications if available
        company = 'Not specified'
        if hasattr(item.product, 'specifications') and item.product.specifications:
            # Try to find company in specifications
            specs = item.product.specifications.lower()
            if 'company:' in specs:
                company_line = [line for line in specs.split('\n') if 'company:' in line.lower()]
                if company_line:
                    company = company_line[0].split('company:')[1].strip()

        item_data = {
            'id': item.id,
            'product_title': item.product.title,
            'price': float(item.price),
            'status': item.status,
            'status_display': dict(STATUS_CHOICES).get(item.status, item.status),
            'item_type': item.product.item_type,  # Add item type
            'ip': item.product.login_url or 'Not available',
            'url': item.product.url or 'Not available',  # Add URL for account items
            'username': item.product.username or 'Not available',
            'password': item.product.password or 'Not available',
            'company': company,
            'ram': item.product.ram or 'Not available',  # Add RAM for VPS items
            'description': item.product.proof or 'Not available',  # Add description for account items
        }

        # Add proof link if it exists in specifications
        if item.product.item_type == 'account' and 'Proof Link:' in item.product.specifications:
            import re
            proof_link_match = re.search(r'Proof Link:\s*(https?://\S+)', item.product.specifications)
            if proof_link_match:
                item_data['proof_link'] = proof_link_match.group(1)
            else:
                item_data['proof_link'] = None
        items.append(item_data)

    # Get payment information if available
    payment_info = None
    try:
        payment = Payment.objects.get(order=order)
        payment_info = {
            'txid': payment.txid,
            'verified': payment.verified,
            'created_at': payment.created_at.strftime('%Y-%m-%d %H:%M'),
            'verified_at': payment.verified_at.strftime('%Y-%m-%d %H:%M') if payment.verified_at else None,
        }
    except Payment.DoesNotExist:
        pass

    # Get status history
    history = []
    for item in order.items.all():
        for status_change in item.status_history.all():
            history.append({
                'old_status': status_change.old_status,
                'old_status_display': dict(STATUS_CHOICES).get(status_change.old_status, 'New'),
                'new_status': status_change.new_status,
                'new_status_display': dict(STATUS_CHOICES).get(status_change.new_status, status_change.new_status),
                'changed_at': status_change.changed_at.strftime('%Y-%m-%d %H:%M'),
            })

    # Prepare the response
    response_data = {
        'id': order.id,
        'created_at': order.created_at.strftime('%Y-%m-%d %H:%M'),
        'updated_at': order.updated_at.strftime('%Y-%m-%d %H:%M'),
        'total_amount': float(order.total_amount),
        'status': order.order_status,
        'items': items,
        'payment': payment_info,
        'history': history,
    }

    return JsonResponse(response_data)

@login_required
def seller_dashboard(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        messages.error(request, "Access denied. You need a seller account.")
        return redirect('index')

    # Get filter parameter from request
    filter_status = request.GET.get('filter', 'unsold')

    # Get all items by this seller
    if filter_status == 'sold':
        # Show only sold items
        products = Item.objects.filter(seller=request.user, is_sold=True).order_by('-created_at')
    elif filter_status == 'all':
        # Show all items
        products = Item.objects.filter(seller=request.user).order_by('-created_at')
    else:
        # Default: show only unsold items
        products = Item.objects.filter(seller=request.user, is_sold=False, is_active=True).order_by('-created_at')

    # Get all order items for this seller's products
    order_items = OrderItem.objects.filter(
        product__seller=request.user
    ).order_by('-order__created_at')

    # Calculate total paid orders
    total_paid_orders = order_items.filter(status='paid').count()

    # Calculate total gained balance (sum of all paid orders)
    from django.db.models import Sum
    total_gained_balance = order_items.filter(status='paid').aggregate(Sum('price'))['price__sum'] or 0

    # Get total reports count
    from django.db.models import Count
    total_reports = Report.objects.filter(
        order_item__product__seller=request.user
    ).count()

    # Get sold items count
    sold_count = Item.objects.filter(seller=request.user, is_sold=True).count()

    # Get unsold items count
    unsold_count = Item.objects.filter(seller=request.user, is_sold=False, is_active=True).count()

    # Get reports for each product
    reports = Report.objects.filter(order_item__product__seller=request.user)

    # Create a dictionary to store report info for each product
    product_reports = {}
    for report in reports:
        product_id = report.order_item.product.id
        product_reports[product_id] = {
            'has_report': True,
            'order_item_id': report.order_item.id,
            'decision': report.decision,
            'resolved': report.resolved
        }

    # Add report info to each product
    for product in products:
        if product.id in product_reports:
            product.report_exists = True
            product.report_order_item_id = product_reports[product.id]['order_item_id']
            product.report_decision = product_reports[product.id]['decision']
            product.report_resolved = product_reports[product.id]['resolved']
        else:
            product.report_exists = False
            product.report_decision = None
            product.report_resolved = False

    return render(request, 'marketplace/seller_dashboard.html', {
        'products': products,
        'order_items': order_items,
        'total_paid_orders': total_paid_orders,
        'total_gained_balance': total_gained_balance,
        'total_reports': total_reports,
        'sold_count': sold_count,
        'unsold_count': unsold_count,
        'current_filter': filter_status
    })

@login_required
def delete_item(request, item_id):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        messages.error(request, "Access denied. You need a seller account.")
        return redirect('index')

    # Get the item
    item = get_object_or_404(Item, id=item_id, seller=request.user)

    # Check if the item is already sold
    if item.is_sold:
        messages.error(request, "You cannot delete an item that has already been sold.")
        return redirect('seller_dashboard')

    # Delete the item
    item_title = item.title
    item.delete()

    messages.success(request, f"Item '{item_title}' has been deleted successfully.")
    return redirect('seller_dashboard')

@login_required
def admin_dashboard(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        messages.error(request, "Access denied. You need admin privileges.")
        return redirect('index')

    # Get all pending payments for verification
    pending_payments = Payment.objects.filter(verified=False).order_by('-created_at')

    # Get all pending top-up requests
    pending_topups = BalanceTopUp.objects.filter(verified=False).order_by('-created_at')

    # Get all reports that need admin attention
    open_reports = Report.objects.filter(resolved=False).order_by('-created_at')

    # Get recent order items with status changes
    recent_status_changes = OrderStatusHistory.objects.all().order_by('-changed_at')[:20]

    return render(request, 'marketplace/admin_dashboard.html', {
        'pending_payments': pending_payments,
        'pending_topups': pending_topups,
        'open_reports': open_reports,
        'recent_status_changes': recent_status_changes
    })

# Item listing views
def vps_list(request):
    # Base queryset for active items that are not sold
    products = Item.objects.filter(is_active=True, is_sold=False).order_by('-created_at')

    # Get item type filter from request
    item_type = request.GET.get('item_type', None)
    if item_type and item_type in dict(Item.ITEM_TYPE_CHOICES):
        products = products.filter(item_type=item_type)

    if request.user.is_authenticated:
        if request.user.profile.role == 'buyer':
            # For buyers, show items with required fields
            # For VPS/RDP items
            vps_items = products.filter(
                item_type='vps',
                username__isnull=False,
                password__isnull=False,
                login_url__isnull=False,
                ram__isnull=False
            )

            # For Account items
            account_items = products.filter(
                item_type='account',
                username__isnull=False,
                password__isnull=False,
                url__isnull=False,
                proof__isnull=False
            )

            # Combine the querysets
            products = vps_items | account_items

        elif request.user.profile.role == 'seller':
            # For sellers, show all available items (both their own and others')
            # But ensure we only show unsold items
            products = Item.objects.filter(is_active=True, is_sold=False).order_by('-created_at')

            # Pass a flag to indicate if an item belongs to the current user
            for product in products:
                product.is_own = (product.seller == request.user)

    return render(request, 'marketplace/vps_list.html', {
        'products': products
    })

@login_required
def add_account(request):
    """
    Temporary redirect view for add_account to add_vps_product
    This is to handle existing references in templates
    """
    messages.info(request, "Account creation has been replaced with VPS product creation.")
    return redirect('add_vps_product')

def vps_detail(request, product_id):
    product = get_object_or_404(Item, id=product_id, is_active=True)

    # If the user is a seller, redirect to seller dashboard
    if request.user.is_authenticated and hasattr(request.user, 'profile') and request.user.profile.role == 'seller':
        messages.info(request, "Product details are now available directly in the seller dashboard.")
        return redirect('seller_dashboard')

    # If the product is sold and the user is not the seller or buyer, redirect to product list
    if product.is_sold and request.user.is_authenticated:
        if request.user.profile.role == 'buyer' and product.buyer != request.user:
            messages.error(request, "This item has already been sold.")
            return redirect('vps_list')
        elif request.user.profile.role != 'seller' and request.user != product.seller:
            messages.error(request, "This item has already been sold.")
            return redirect('vps_list')

    # Add a flag to indicate if the product belongs to the current user
    if request.user.is_authenticated:
        product.is_own = (product.seller == request.user)
    else:
        product.is_own = False

    return render(request, 'marketplace/vps_detail.html', {'product': product})

@login_required
def add_vps_product(request):
    """
    View for adding a new item.
    Only sellers can add items, and they must include login URL/IP, username, password, and company information.
    """
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        messages.error(request, "Access denied. Only sellers can add items.")
        return redirect('index')

    if request.method == 'POST':
        form = ItemForm(request.POST)
        if form.is_valid():
            product = form.save(commit=False)
            product.seller = request.user
            product.save()

            messages.success(request, "Item added successfully!")
            return redirect('seller_dashboard')
    else:
        form = ItemForm()

    return render(request, 'marketplace/vps_form.html', {
        'form': form
    })

@login_required
def edit_vps_product(request, product_id):
    """
    View for editing an existing item.
    This functionality has been disabled - sellers can no longer edit items.
    """
    messages.error(request, "Editing items is no longer allowed. Please delete the item and create a new one if needed.")
    return redirect('seller_dashboard')

@login_required
def get_product_details(request, product_id):
    """
    View to get product details for the modal.
    Returns JSON with product details.
    """
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        return JsonResponse({'error': 'Access denied'}, status=403)

    # Get the product and check if it belongs to the current user
    product = get_object_or_404(Item, id=product_id)

    # Prepare the response data
    product_data = {
        'id': product.id,
        'title': product.title,
        'description': product.description,
        'price': float(product.price),
        'created_at': product.created_at.strftime('%Y-%m-%d %H:%M'),
        'is_sold': product.is_sold,
        'is_active': product.is_active,
        'item_type': product.item_type,
        'login_url': product.login_url or 'Not available',
        'username': product.username or 'Not available',
        'password': product.password or 'Not available',
    }

    # Filter out default specifications content
    if product.specifications and 'Details will be shown here' in product.specifications:
        # Remove the default specifications
        specs = product.specifications.replace('Details will be shown here', '').strip()
        # Remove the default Type and RAM entries that are added automatically
        specs_lines = []
        for line in specs.split('\n'):
            if not (line.startswith('Type:') or line.startswith('RAM:') or line.startswith('Company:')):
                if line.strip():
                    specs_lines.append(line.strip())
        product_data['specifications'] = '\n'.join(specs_lines) if specs_lines else 'Not available'
    else:
        product_data['specifications'] = product.specifications or 'Not available'

    # Add type-specific fields
    if product.item_type == 'vps':
        product_data['ram'] = product.ram or 'Not available'

        # Extract company from specifications if available
        company = 'Not available'
        if product.specifications:
            import re
            company_match = re.search(r'Company:\s*([^,\n]+)', product.specifications)
            if company_match:
                company = company_match.group(1).strip()
        product_data['company'] = company

    elif product.item_type == 'account':
        product_data['url'] = product.url or 'Not available'
        product_data['proof'] = product.proof or 'Not available'

        # Extract proof link if available
        if 'Proof Link:' in (product.specifications or ''):
            import re
            proof_link_match = re.search(r'Proof Link:\s*(https?://\S+)', product.specifications)
            if proof_link_match:
                product_data['proof_link'] = proof_link_match.group(1)

    # Add buyer info if sold
    if product.is_sold and product.buyer:
        product_data['buyer'] = {
            'username': product.buyer.username,
            'date_purchased': product.sold_at.strftime('%Y-%m-%d %H:%M') if product.sold_at else 'Unknown'
        }

    return JsonResponse(product_data)

@login_required
def delete_vps_product(request, product_id):
    """
    View for deleting an existing item.
    Only sellers can delete their own items.
    """
    product = get_object_or_404(Item, id=product_id)

    # Check if the user is the seller of this item
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller' or product.seller != request.user:
        messages.error(request, "Access denied. You can only delete your own items.")
        return redirect('index')

    if request.method == 'POST':
        # Instead of actually deleting, mark as inactive
        product.is_active = False
        product.save()
        messages.success(request, "Item deleted successfully!")
        return redirect('seller_dashboard')

    # If not POST, redirect to product detail
    return redirect('vps_detail', product_id=product.id)





# Balance top-up view
@login_required
def topup_balance(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'buyer':
        messages.error(request, "Only buyers can top up their balance.")
        # Redirect to appropriate dashboard based on role
        if hasattr(request.user, 'profile'):
            if request.user.profile.role == 'seller':
                return redirect('seller_dashboard')
            elif request.user.profile.role == 'admin':
                return redirect('admin_dashboard')
            elif request.user.profile.role == 'support':
                return redirect('support_dashboard')
        return redirect('index')

    # Get recent top-ups for this user
    recent_topups = BalanceTopUp.objects.filter(user=request.user).order_by('-created_at')[:5]

    # Check if there's an active payment in progress
    active_topup = None
    for topup in recent_topups:
        if not topup.verified and topup.payment_status in ['waiting', 'confirming', 'partially_paid']:
            # Get the latest status from NOWPayments
            if topup.nowpayments_payment_id:
                payment_result = get_payment_status(topup.nowpayments_payment_id)
                if payment_result.get('success'):
                    # Update the topup with the latest status
                    topup.update_from_nowpayments(payment_result.get('details', {}))

            # If still active after status update, use this as the active topup
            if not topup.verified and topup.payment_status in ['waiting', 'confirming', 'partially_paid']:
                active_topup = topup
                break

    if request.method == 'POST':
        form = TopUpBalanceForm(request.POST)
        if form.is_valid():
            # Get the amount from the form
            amount = form.cleaned_data['amount']

            # Create a new top-up request in the database
            topup = BalanceTopUp.objects.create(
                user=request.user,
                amount=amount,
                method='nowpayments',
                payment_status='waiting',
                payment_type='trc20'  # Set the payment type
            )

            # Generate a unique order ID
            order_id = f"topup-{request.user.id}-{topup.id}-{int(timezone.now().timestamp())}"

            # Create a payment using NOWPayments API
            # Use the public webhook URL for callbacks
            ipn_callback_url = settings.NOWPAYMENTS_WEBHOOK_URL if hasattr(settings, 'NOWPAYMENTS_WEBHOOK_URL') else request.build_absolute_uri(reverse('nowpayments_webhook'))
            payment_result = create_nowpayments_payment(
                amount=amount,
                currency='USD',
                order_id=order_id,
                order_description=f"Balance top-up of ${amount} for {request.user.username}",
                ipn_callback_url=ipn_callback_url
            )

            if payment_result.get('success'):
                # Update the topup with payment details
                payment_data = payment_result.get('details', {})

                # Log payment data for debugging
                print(f"NOWPayments API Response: {json.dumps(payment_data, indent=2)}")
                print(f"Pay Address: {payment_data.get('pay_address')}")

                topup.update_from_nowpayments(payment_data)

                # Log the updated topup object
                print(f"Updated TopUp Object: ID={topup.id}, Pay Address={topup.nowpayments_pay_address}")

                # Set this as the active topup
                active_topup = topup

                messages.success(request, "Payment created successfully! Please complete the payment to add funds to your balance.")
            else:
                # If payment creation failed, show error
                error_message = payment_result.get('message', 'Unknown error')
                messages.error(request, f"Failed to create payment: {error_message}")

                # Delete the topup record since payment creation failed
                topup.delete()

            # Redirect to the same page to show the payment details
            return redirect('topup_balance')
    else:
        form = TopUpBalanceForm()

    # Make settings available in the template context
    from django.conf import settings as django_settings

    return render(request, 'marketplace/topup_balance.html', {
        'form': form,
        'active_topup': active_topup,
        'recent_topups': recent_topups,
        'settings': django_settings  # Pass settings to the template
    })

# Order and payment views
@login_required
def create_order(request, product_id):
    if not hasattr(request.user, 'profile') or (request.user.profile.role != 'buyer' and request.user.profile.role != 'seller'):
        messages.error(request, "Only buyers and sellers can place orders.")
        return redirect('vps_detail', product_id=product_id)

    product = get_object_or_404(Item, id=product_id, is_active=True)

    # Prevent sellers from buying their own products
    if request.user.profile.role == 'seller' and request.user == product.seller:
        messages.error(request, "You cannot purchase your own product.")
        return redirect('vps_detail', product_id=product_id)

    # Check if the product is already sold
    if product.is_sold:
        messages.error(request, "This item has already been sold.")
        return redirect('vps_list')

    # Check if user has enough balance
    if request.user.profile.balance < product.price:
        needed_amount = product.price - request.user.profile.balance
        messages.error(request, f"Insufficient balance. You need ${needed_amount:.2f} more but your balance is ${request.user.profile.balance:.2f}. Please top up your balance.")
        return redirect('topup_balance')

    # Create a new order
    order = Order.objects.create(
        buyer=request.user,
        total_amount=product.price
    )

    # Add the item to the order
    OrderItem.objects.create(
        order=order,
        product=product,
        quantity=1,
        price=product.price,
        status='paid'  # Mark as paid immediately since we're using balance
    )

    # Mark the product as sold and set the buyer
    product.is_sold = True
    product.buyer = request.user
    product.save()

    # Deduct from user's balance
    profile = request.user.profile
    profile.balance -= product.price
    profile.save()

    # Create notification for the seller
    Notification.objects.create(
        user=product.seller,
        type='payment',
        title='New Sale',
        message=f'Your item "{product.title}" has been purchased by {request.user.username} for ${product.price}.',
        link='/seller/'
    )

    messages.success(request, "Order created and paid successfully! The seller will be notified.")
    return redirect('buyer_dashboard')

@login_required
def payment_view(request, order_id):
    order = get_object_or_404(Order, id=order_id)

    if order.buyer != request.user:
        return HttpResponseForbidden("You don't have permission to access this payment.")

    # Check if payment already exists
    try:
        payment = Payment.objects.get(order=order)
        if payment.verified:
            messages.info(request, "Payment has already been verified.")
            return redirect('buyer_dashboard')
    except Payment.DoesNotExist:
        payment = None

    # Get seller's wallet address from the first product in the order
    first_item = order.items.first()
    if first_item:
        seller_wallet = first_item.product.seller.profile.wallet_address
    else:
        seller_wallet = "No wallet address available"

    if request.method == 'POST':
        form = PaymentForm(request.POST)
        if form.is_valid():
            if payment:
                # Update existing payment
                payment.txid = form.cleaned_data['txid']
                payment.save()
            else:
                # Create new payment
                Payment.objects.create(
                    order=order,
                    txid=form.cleaned_data['txid'],
                    amount=order.total_amount
                )

            messages.success(request, "Payment information submitted. Awaiting verification.")
            return redirect('buyer_dashboard')
    else:
        form = PaymentForm(instance=payment)

    return render(request, 'marketplace/payment.html', {
        'order': order,
        'form': form,
        'seller_wallet': seller_wallet
    })

@login_required
def verify_payment(request, payment_id):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        messages.error(request, "Only admins can verify payments.")
        return redirect('index')

    payment = get_object_or_404(Payment, id=payment_id)

    if payment.verified:
        messages.info(request, "This payment has already been verified.")
        return redirect('admin_dashboard')

    if request.method == 'POST':
        form = PaymentVerificationForm(request.POST)
        if form.is_valid():
            if form.cleaned_data['verify']:
                # Verify the payment
                payment.verified = True
                payment.verified_at = timezone.now()
                payment.verified_by = request.user
                payment.save()

                # Update all order items to 'paid' status
                for item in payment.order.items.all():
                    item.status = 'paid'
                    item.save()

                messages.success(request, "Payment verified successfully!")
            else:
                # Reject the payment
                for item in payment.order.items.all():
                    item.status = 'rejected'
                    item.save()
                messages.warning(request, "Payment rejected.")

            return redirect('admin_dashboard')
    else:
        form = PaymentVerificationForm()

        # Try to auto-verify using TronScan API
        verification_result = verify_tron_transaction(payment.txid, payment.amount)
        if verification_result.get('verified'):
            form.initial = {'verify': True}
            messages.info(request, "Transaction appears valid based on automatic verification.")

    return render(request, 'marketplace/verify_payment_tailwind.html', {
        'payment': payment,
        'form': form
    })

@login_required
def verify_topup(request, topup_id):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'admin':
        messages.error(request, "Only admins can verify top-ups.")
        return redirect('index')

    topup = get_object_or_404(BalanceTopUp, id=topup_id)

    if topup.verified:
        messages.info(request, "This top-up has already been verified.")
        return redirect('admin_dashboard')

    if request.method == 'POST':
        form = PaymentVerificationForm(request.POST)
        if form.is_valid():
            if form.cleaned_data.get('verify'):
                # Verify the top-up
                topup.verified = True
                topup.verified_at = timezone.now()
                topup.verified_by = request.user
                topup.save()

                # Add amount to user's balance
                profile = topup.user.profile
                profile.balance += topup.amount
                profile.save()

                # Create notification for the user
                Notification.objects.create(
                    user=topup.user,
                    title="Balance Top-up Verified",
                    message=f"Your top-up of ${topup.amount} has been verified and added to your balance.",
                    notification_type="balance"
                )

                messages.success(request, f"Top-up verified successfully! ${topup.amount} added to {topup.user.username}'s balance.")
            elif form.cleaned_data.get('reject'):
                # Reject the top-up
                messages.warning(request, f"Top-up from {topup.user.username} rejected.")

            return redirect('admin_dashboard')
    else:
        form = PaymentVerificationForm()

        # Try to auto-verify using TronScan API
        verification_result = verify_tron_transaction(topup.txid, topup.amount)
        if verification_result.get('verified'):
            form.initial = {'verify': True}
            messages.info(request, "Transaction appears valid based on automatic verification.")

    return render(request, 'marketplace/verify_topup.html', {
        'topup': topup,
        'form': form
    })

# Support dashboard view
@login_required
def support_dashboard(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'support':
        messages.error(request, "Access denied. You need support privileges.")
        return redirect('index')

    # Get all open tickets
    open_tickets = Ticket.objects.filter(
        status__in=['open', 'in_progress', 'waiting']
    ).order_by('-created_at')

    # Get all open reports
    open_reports = Report.objects.filter(resolved=False).order_by('-created_at')

    return render(request, 'marketplace/support_dashboard.html', {
        'open_tickets': open_tickets,
        'open_reports': open_reports
    })

# Withdrawal system views
@login_required
def withdrawal_request(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role != 'seller':
        messages.error(request, "Only sellers can request withdrawals.")
        return redirect('index')

    # Get user's current balance
    profile = request.user.profile

    # Get pending withdrawals
    pending_withdrawals = Withdrawal.objects.filter(
        user=request.user,
        status__in=['pending', 'approved']
    ).order_by('-created_at')

    # Get completed withdrawals
    completed_withdrawals = Withdrawal.objects.filter(
        user=request.user,
        status__in=['completed', 'rejected']
    ).order_by('-created_at')[:10]  # Show only the last 10

    if request.method == 'POST':
        form = WithdrawalForm(request.POST)
        if form.is_valid():
            amount = form.cleaned_data['amount']

            # Check if user has enough balance
            if profile.balance < amount:
                messages.error(request, f"Insufficient balance. You requested ${amount} but your available balance is ${profile.balance}.")
                return redirect('withdrawal_request')

            # Create withdrawal request
            withdrawal = form.save(commit=False)
            withdrawal.user = request.user
            withdrawal.save()

            # Deduct from user's balance
            profile.balance -= amount
            profile.save()

            # Create notification for admin
            admin_users = User.objects.filter(profile__role__in=['admin', 'support'])
            for admin in admin_users:
                Notification.objects.create(
                    user=admin,
                    type='payment',
                    title='New Withdrawal Request',
                    message=f'New withdrawal request of ${amount} from {request.user.username}',
                    link=f'/admin/withdrawals/{withdrawal.id}/'
                )

            messages.success(request, f"Withdrawal request of ${amount} submitted successfully! Your request is pending approval.")
            return redirect('withdrawal_request')
    else:
        # Pre-fill wallet address if available
        initial_data = {}
        if profile.wallet_address:
            initial_data['wallet_address'] = profile.wallet_address

        form = WithdrawalForm(initial=initial_data)

    return render(request, 'marketplace/withdrawal_request.html', {
        'form': form,
        'profile': profile,
        'pending_withdrawals': pending_withdrawals,
        'completed_withdrawals': completed_withdrawals
    })

@login_required
def withdrawal_list(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    # Get all pending withdrawals
    pending_withdrawals = Withdrawal.objects.filter(
        status='pending'
    ).order_by('-created_at')

    # Get all approved withdrawals
    approved_withdrawals = Withdrawal.objects.filter(
        status='approved'
    ).order_by('-created_at')

    # Get recent completed/rejected withdrawals
    recent_withdrawals = Withdrawal.objects.filter(
        status__in=['completed', 'rejected']
    ).order_by('-processed_at')[:20]

    return render(request, 'marketplace/withdrawal_list.html', {
        'pending_withdrawals': pending_withdrawals,
        'approved_withdrawals': approved_withdrawals,
        'recent_withdrawals': recent_withdrawals
    })

@login_required
def process_withdrawal(request, withdrawal_id):
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    withdrawal = get_object_or_404(Withdrawal, id=withdrawal_id)

    # Don't allow processing already completed/rejected withdrawals
    if withdrawal.status in ['completed', 'rejected']:
        messages.info(request, f"This withdrawal has already been {withdrawal.status}.")
        return redirect('withdrawal_list')

    if request.method == 'POST':
        form = WithdrawalProcessForm(request.POST)
        if form.is_valid():
            action = form.cleaned_data['action']
            notes = form.cleaned_data['notes']

            if action == 'approve' and withdrawal.status == 'pending':
                # Approve the withdrawal
                withdrawal.status = 'approved'
                withdrawal.notes = notes
                withdrawal.save()

                messages.success(request, f"Withdrawal request of ${withdrawal.amount} for {withdrawal.user.username} has been approved.")

                # Notify the user
                Notification.objects.create(
                    user=withdrawal.user,
                    type='payment',
                    title='Withdrawal Request Approved',
                    message=f'Your withdrawal request of ${withdrawal.amount} has been approved and is being processed.',
                    link='/withdrawals/'
                )

            elif action == 'approve' and withdrawal.status == 'approved':
                # Mark as completed
                withdrawal.status = 'completed'
                withdrawal.txid = form.cleaned_data['txid']
                withdrawal.notes = notes
                withdrawal.processed_at = timezone.now()
                withdrawal.processed_by = request.user
                withdrawal.save()

                messages.success(request, f"Withdrawal of ${withdrawal.amount} for {withdrawal.user.username} has been marked as completed.")

                # Notify the user
                Notification.objects.create(
                    user=withdrawal.user,
                    type='payment',
                    title='Withdrawal Completed',
                    message=f'Your withdrawal of ${withdrawal.amount} has been completed. Transaction ID: {withdrawal.txid}',
                    link='/withdrawals/'
                )

            elif action == 'reject':
                # Reject the withdrawal
                withdrawal.status = 'rejected'
                withdrawal.notes = notes
                withdrawal.processed_at = timezone.now()
                withdrawal.processed_by = request.user
                withdrawal.save()

                # Return funds to user's balance
                profile = withdrawal.user.profile
                profile.balance += withdrawal.amount
                profile.save()

                messages.warning(request, f"Withdrawal request of ${withdrawal.amount} for {withdrawal.user.username} has been rejected. Funds have been returned to their balance.")

                # Notify the user
                Notification.objects.create(
                    user=withdrawal.user,
                    type='payment',
                    title='Withdrawal Request Rejected',
                    message=f'Your withdrawal request of ${withdrawal.amount} has been rejected. The funds have been returned to your balance.',
                    link='/withdrawals/'
                )

            return redirect('withdrawal_list')
    else:
        form = WithdrawalProcessForm()

    return render(request, 'marketplace/process_withdrawal.html', {
        'form': form,
        'withdrawal': withdrawal
    })


@csrf_exempt
def nowpayments_webhook(request):
    """
    Webhook handler for NOWPayments IPN (Instant Payment Notifications)
    This endpoint receives payment status updates from NOWPayments
    """
    if request.method != 'POST':
        return HttpResponse('Method not allowed', status=405)

    # Get the signature from the headers
    ipn_signature = request.headers.get('X-NOWPayments-Sig')

    # In test mode, we might not have a signature
    if not ipn_signature and not settings.NOWPAYMENTS_TEST_MODE:
        return HttpResponse('Missing signature', status=400)

    # Get the raw request body
    try:
        request_body = request.body.decode('utf-8')
        payment_data = json.loads(request_body)
    except Exception as e:
        return HttpResponse(f'Invalid request body: {str(e)}', status=400)

    # Verify the signature (skip in test mode)
    if not settings.NOWPAYMENTS_TEST_MODE and ipn_signature and not verify_nowpayments_ipn_request(request_body, ipn_signature):
        return HttpResponse('Invalid signature', status=403)

    # Extract payment details
    payment_id = payment_data.get('payment_id')
    payment_status = payment_data.get('payment_status')
    order_id = payment_data.get('order_id')

    if not payment_id or not payment_status:
        return HttpResponse('Missing required fields', status=400)

    # Find the corresponding top-up request
    try:
        # First try to find by payment_id
        topup = BalanceTopUp.objects.filter(nowpayments_payment_id=payment_id).first()

        # If not found, try to find by order_id
        if not topup and order_id:
            topup = BalanceTopUp.objects.filter(nowpayments_order_id=order_id).first()

        if not topup:
            return HttpResponse(f'Top-up not found for payment_id: {payment_id}', status=404)

        # Update the top-up with the payment data
        topup.update_from_nowpayments(payment_data)

        # Log the webhook event with detailed information
        print(f"Webhook received for payment {payment_id}, status: {payment_status}")
        print(f"Order ID: {order_id}")
        print(f"Payment details: {json.dumps(payment_data, indent=2)}")
        print(f"Updated topup record: ID={topup.id}, Status={topup.payment_status}, Verified={topup.verified}")

        return HttpResponse('OK', status=200)
    except Exception as e:
        return HttpResponse(f'Error processing webhook: {str(e)}', status=500)




# User role management
@login_required
def manage_user_roles(request):
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin']:
        messages.error(request, "Access denied. You need admin privileges.")
        return redirect('index')

    # Get all users with their profiles
    users = User.objects.filter(is_superuser=False).select_related('profile').order_by('username')

    return render(request, 'marketplace/manage_user_roles.html', {
        'users': users
    })

@login_required
def change_user_role(request, user_id):
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin']:
        messages.error(request, "Access denied. You need admin privileges.")
        return redirect('index')

    target_user = get_object_or_404(User, id=user_id)

    # Don't allow changing own role
    if target_user == request.user:
        messages.error(request, "You cannot change your own role.")
        return redirect('manage_user_roles')

    if request.method == 'POST':
        form = UserRoleForm(request.POST)
        if form.is_valid():
            new_role = form.cleaned_data['role']
            notes = form.cleaned_data['notes']

            # Change the user's role
            profile = target_user.profile
            old_role = profile.role
            profile.change_role(new_role, request.user)

            messages.success(request, f"User {target_user.username}'s role has been changed from {profile.get_role_display(old_role)} to {profile.get_role_display()}.")
            return redirect('manage_user_roles')
    else:
        form = UserRoleForm(initial={'role': target_user.profile.role})

    return render(request, 'marketplace/change_user_role.html', {
        'form': form,
        'target_user': target_user
    })

# Notification system
@login_required
def notifications(request):
    # Get user's unread notifications
    notifications = Notification.objects.filter(
        user=request.user,
        read=False
    ).order_by('-created_at')

    # Get user's read notifications (limited to last 20)
    read_notifications = Notification.objects.filter(
        user=request.user,
        read=True
    ).order_by('-created_at')[:20]

    return render(request, 'marketplace/notifications.html', {
        'notifications': notifications,
        'read_notifications': read_notifications
    })

@login_required
def mark_notification_read(request, notification_id):
    notification = get_object_or_404(Notification, id=notification_id, user=request.user)
    notification.read = True
    notification.save()

    # If there's a link, redirect to it
    if notification.link:
        return redirect(notification.link)

    return redirect('notifications')

@login_required
def mark_all_notifications_read(request):
    Notification.objects.filter(user=request.user, read=False).update(read=True)
    messages.success(request, "All notifications marked as read.")
    return redirect('notifications')

@login_required
def delete_notification(request, notification_id):
    try:
        notification = get_object_or_404(Notification, id=notification_id, user=request.user)
        notification.delete()

        # If it's an AJAX request, return JSON response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'success', 'message': 'Notification deleted successfully'})

        # Otherwise redirect back
        messages.success(request, "Notification deleted successfully.")
        return redirect('notifications')
    except Exception as e:
        # Log the error
        print(f"Error deleting notification: {str(e)}")

        # If it's an AJAX request, return error response
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

        # Otherwise redirect back with error message
        messages.error(request, f"Error deleting notification: {str(e)}")
        return redirect('notifications')

# Ticket system views
@login_required
def ticket_list(request):
    # Get status filter from URL parameter
    status_filter = request.GET.get('status', 'open')

    # For buyers and sellers, show only their tickets
    if hasattr(request.user, 'profile'):
        if request.user.profile.role in ['buyer', 'seller']:
            tickets = Ticket.objects.filter(user=request.user).order_by('-created_at')
        elif request.user.profile.role in ['admin', 'support']:
            # For admins and support, show all tickets
            tickets = Ticket.objects.all().order_by('-created_at')
        else:
            messages.error(request, "Invalid user role.")
            return redirect('index')
    else:
        messages.error(request, "User profile not found.")
        return redirect('index')

    return render(request, 'marketplace/ticket_list.html', {
        'tickets': tickets,
        'status_filter': status_filter
    })

@login_required
def user_reports(request):
    """View for users to see all their reports"""
    if not hasattr(request.user, 'profile'):
        messages.error(request, "User profile not found.")
        return redirect('index')

    # Get status filter from URL parameter
    status_filter = request.GET.get('status', 'open')

    # For buyers, show reports they've created
    if request.user.profile.role == 'buyer':
        reports = Report.objects.filter(created_by=request.user).order_by('-created_at')
    # For sellers, show reports related to their products
    elif request.user.profile.role == 'seller':
        reports = Report.objects.filter(order_item__product__seller=request.user).order_by('-created_at')
    # For admins and support, show all reports
    elif request.user.profile.role in ['admin', 'support']:
        reports = Report.objects.all().order_by('-created_at')
    else:
        messages.error(request, "Invalid user role.")
        return redirect('index')

    return render(request, 'marketplace/user_reports.html', {
        'reports': reports,
        'status_filter': status_filter
    })

@login_required
def create_ticket(request):
    is_support = hasattr(request.user, 'profile') and request.user.profile.role in ['support', 'admin']

    if request.method == 'POST':
        if is_support:
            form = SupportTicketForm(request.POST)
        else:
            form = TicketForm(request.POST)

        if form.is_valid():
            ticket = form.save(commit=False)

            # If support staff is creating a ticket for another user
            if is_support and 'user' in form.cleaned_data:
                ticket.user = form.cleaned_data['user']
            else:
                ticket.user = request.user

            ticket.save()

            # Add the first message as the ticket description
            TicketMessage.objects.create(
                ticket=ticket,
                user=request.user,  # The message is from the creator (support staff)
                message=ticket.description
            )

            # Create notifications for support staff when a new ticket is created
            if not is_support:  # If ticket is created by a regular user
                support_users = User.objects.filter(profile__role__in=['support', 'admin'])
                for support_user in support_users:
                    Notification.objects.create(
                        user=support_user,
                        type='ticket_update',
                        title="New Support Ticket Created",
                        message=f"{ticket.user.username} created a new ticket: {ticket.subject}",
                        link=f"/ticket/{ticket.id}/"
                    )
            else:  # If ticket is created by support for another user
                # Notify the user that a ticket was created for them
                Notification.objects.create(
                    user=ticket.user,
                    type='ticket_update',
                    title="Support Ticket Created For You",
                    message=f"Support staff created a ticket for you: {ticket.subject}",
                    link=f"/ticket/{ticket.id}/"
                )

            messages.success(request, "Ticket created successfully.")
            return redirect('ticket_detail', ticket_id=ticket.id)
    else:
        if is_support:
            form = SupportTicketForm()
        else:
            form = TicketForm()

    return render(request, 'marketplace/create_ticket.html', {
        'form': form,
        'is_support': is_support
    })

@login_required
def ticket_detail(request, ticket_id):
    ticket = get_object_or_404(Ticket, id=ticket_id)

    # Check permissions
    is_owner = ticket.user == request.user
    is_support = hasattr(request.user, 'profile') and request.user.profile.role == 'support'
    is_admin = hasattr(request.user, 'profile') and request.user.profile.role == 'admin'

    if not (is_owner or is_support or is_admin):
        messages.error(request, "You don't have permission to view this ticket.")
        return redirect('ticket_list')

    # Get all messages for this ticket
    messages_list = ticket.messages.all().order_by('created_at')

    # Handle new message submission
    if request.method == 'POST' and 'message' in request.POST:
        form = TicketMessageForm(request.POST)
        if form.is_valid():
            message = form.save(commit=False)
            message.ticket = ticket
            message.user = request.user

            # Check if this is an internal note
            if 'is_internal_note' in request.POST and (is_support or is_admin):
                message.is_internal_note = True

            message.save()

            # Update ticket status if needed
            if is_owner and ticket.status == 'waiting':
                ticket.status = 'open'
                ticket.save()
            elif (is_support or is_admin) and ticket.status == 'open':
                ticket.status = 'waiting'
                ticket.save()

            # Create notification for the recipient
            # If message is from owner, notify support staff
            # If message is from support, notify the ticket owner
            if not message.is_internal_note:  # Don't notify for internal notes
                if is_owner:
                    # Notify all support staff
                    support_users = User.objects.filter(profile__role__in=['support', 'admin'])
                    for support_user in support_users:
                        Notification.objects.create(
                            user=support_user,
                            type='ticket_update',
                            title=f"New reply in Ticket #{ticket.id}",
                            message=f"{request.user.username} replied to ticket: {ticket.subject}",
                            link=f"/ticket/{ticket.id}/"
                        )
                else:
                    # Notify the ticket owner
                    Notification.objects.create(
                        user=ticket.user,
                        type='ticket_update',
                        title=f"New reply in your Ticket #{ticket.id}",
                        message=f"Support staff replied to your ticket: {ticket.subject}",
                        link=f"/ticket/{ticket.id}/"
                    )

            messages.success(request, "Message sent successfully.")
            return redirect('ticket_detail', ticket_id=ticket.id)
    else:
        form = TicketMessageForm()

    # Ticket assignment has been removed

    # Handle ticket resolution
    if (is_support or is_admin) and request.method == 'POST' and 'resolve' in request.POST:
        ticket.status = 'resolved'
        ticket.resolved_at = timezone.now()
        ticket.resolved_by = request.user
        ticket.save()
        messages.success(request, "Ticket marked as resolved.")
        return redirect('ticket_detail', ticket_id=ticket.id)

    # Handle ticket reopening - only support and admin can reopen tickets
    if (is_support or is_admin) and ticket.status == 'resolved' and request.method == 'POST' and 'reopen' in request.POST:
        ticket.status = 'open'
        ticket.save()
        messages.success(request, "Ticket reopened.")
        return redirect('ticket_detail', ticket_id=ticket.id)

    return render(request, 'marketplace/ticket_detail.html', {
        'ticket': ticket,
        'messages_list': messages_list,
        'form': form,
        'is_owner': is_owner,
        'is_support': is_support,
        'is_admin': is_admin
    })

# Order management views
@login_required
def order_report(request, item_id):
    order_item = get_object_or_404(OrderItem, id=item_id)

    # Check permissions
    is_buyer = order_item.order.buyer == request.user
    is_seller = order_item.product.seller == request.user
    is_admin = hasattr(request.user, 'profile') and request.user.profile.role == 'admin'
    is_support = hasattr(request.user, 'profile') and request.user.profile.role == 'support'

    if not (is_buyer or is_seller or is_admin or is_support):
        return HttpResponseForbidden("You don't have permission to access this order.")

    # Get chat messages for this item
    chat_messages = ChatMessage.objects.filter(order_item=order_item).order_by('created_at')

    # Get status history
    status_history = order_item.status_history.all()

    # Check if there's an existing report
    try:
        report = Report.objects.get(order_item=order_item)
    except Report.DoesNotExist:
        report = None

    # Handle new messages
    if request.method == 'POST' and 'message' in request.POST:
        # Check if the report is resolved and the user is not support
        if report and report.resolved and not is_support:
            messages.error(request, "This report has been resolved. Only support staff can send messages.")
            return redirect('order_report', item_id=item_id)

        chat_form = ChatMessageForm(request.POST)
        if chat_form.is_valid():
            message = chat_form.save(commit=False)
            message.order_item = order_item
            message.sender = request.user
            message.save()

            # Create notification for the other party
            # Determine who should receive the notification
            if is_buyer:
                # Notify the seller and support
                Notification.objects.create(
                    user=order_item.product.seller,
                    type='message',
                    title=f"New message in Report #{report.id if report else order_item.id}",
                    message=f"{request.user.username} sent a message regarding order #{order_item.order.id}",
                    link=f"/order/item/{order_item.id}/report"
                )

                # Also notify support staff if there's a report
                if report:
                    support_users = User.objects.filter(profile__role__in=['support', 'admin'])
                    for support_user in support_users:
                        Notification.objects.create(
                            user=support_user,
                            type='message',
                            title=f"New message in Report #{report.id}",
                            message=f"Buyer {request.user.username} sent a message in a report",
                            link=f"/order/item/{order_item.id}/report"
                        )
            elif is_seller:
                # Notify the buyer and support
                Notification.objects.create(
                    user=order_item.order.buyer,
                    type='message',
                    title=f"New message in Report #{report.id if report else order_item.id}",
                    message=f"{request.user.username} sent a message regarding order #{order_item.order.id}",
                    link=f"/order/item/{order_item.id}/report"
                )

                # Also notify support staff if there's a report
                if report:
                    support_users = User.objects.filter(profile__role__in=['support', 'admin'])
                    for support_user in support_users:
                        Notification.objects.create(
                            user=support_user,
                            type='message',
                            title=f"New message in Report #{report.id}",
                            message=f"Seller {request.user.username} sent a message in a report",
                            link=f"/order/item/{order_item.id}/report"
                        )
            elif is_support or is_admin:
                # Notify both buyer and seller
                Notification.objects.create(
                    user=order_item.order.buyer,
                    type='message',
                    title=f"New message from support in Report #{report.id if report else order_item.id}",
                    message=f"Support staff sent a message regarding order #{order_item.order.id}",
                    link=f"/order/item/{order_item.id}/report"
                )

                Notification.objects.create(
                    user=order_item.product.seller,
                    type='message',
                    title=f"New message from support in Report #{report.id if report else order_item.id}",
                    message=f"Support staff sent a message regarding order #{order_item.order.id}",
                    link=f"/order/item/{order_item.id}/report"
                )

            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({
                    'status': 'success',
                    'message': {
                        'sender': message.sender.username,
                        'text': message.message,
                        'timestamp': message.created_at.strftime('%b %d, %Y, %I:%M %p')
                    }
                })

            messages.success(request, "Message sent.")
            return redirect('order_report', item_id=item_id)
    else:
        chat_form = ChatMessageForm()

    # Handle report creation
    if is_buyer and not report and request.method == 'POST' and 'submit_report' in request.POST:
        issue_type = request.POST.get('issue_type', '')
        first_message = request.POST.get('first_message', '')

        if issue_type and first_message:
            # Create a combined issue description
            combined_issue = f"Issue Type: {issue_type}\n\n{first_message}"

            # Create the report directly
            new_report = Report.objects.create(
                order_item=order_item,
                created_by=request.user,
                issue=combined_issue
            )

            # Create the first chat message
            ChatMessage.objects.create(
                order_item=order_item,
                sender=request.user,
                message=first_message
            )

            # Notify the seller about the report
            Notification.objects.create(
                user=order_item.product.seller,
                type='report_decision',
                title=f"New Report Filed",
                message=f"A report has been filed for your item: {order_item.product.title}",
                link=f"/order/item/{order_item.id}/report"
            )

            # Notify support staff about the new report
            support_users = User.objects.filter(profile__role__in=['support', 'admin'])
            for support_user in support_users:
                Notification.objects.create(
                    user=support_user,
                    type='report_decision',
                    title=f"New Report Filed",
                    message=f"A new report has been filed by {request.user.username} for order #{order_item.order.id}",
                    link=f"/order/item/{order_item.id}/report"
                )

            messages.success(request, "Report submitted successfully.")
            return redirect('order_report', item_id=item_id)
        else:
            messages.error(request, "Please select an issue type and provide a message.")
            return redirect('order_report', item_id=item_id)
    else:
        report_form = ReportForm()

    # Handle report resolution (for admins and support)
    if (is_admin or is_support) and report and not report.resolved and request.method == 'POST' and 'resolve_report' in request.POST:
        resolution_form = ReportResolutionForm(request.POST)
        if resolution_form.is_valid():
            decision = resolution_form.cleaned_data['decision']
            resolution_notes = resolution_form.cleaned_data['resolution_notes']

            # Resolve the report
            report.resolve(request.user, resolution_notes, decision)

            # If the decision is to refund, update the buyer's balance
            if decision == 'refunded':
                buyer = order_item.order.buyer
                buyer_profile = buyer.profile

                # Add the refund amount to the buyer's balance
                buyer_profile.balance += order_item.price
                buyer_profile.save()

                # Update the order item status to refunded
                order_item.status = 'refunded'
                order_item.save()

                # Create a notification for the buyer
                Notification.objects.create(
                    user=buyer,
                    type='report_decision',
                    title='Report Refunded',
                    message=f'Your report for order #{order_item.order.id}-{order_item.id} has been accepted and refunded.',
                    link=f'/order/item/{order_item.id}/report'
                )
            elif decision == 'rejected':
                # Create a notification for the buyer
                Notification.objects.create(
                    user=order_item.order.buyer,
                    type='report_decision',
                    title='Report Rejected',
                    message=f'Your report for order #{order_item.order.id}-{order_item.id} has been reviewed and rejected.',
                    link=f'/order/item/{order_item.id}/report'
                )

            # Add a system message about the resolution
            ChatMessage.objects.create(
                order_item=order_item,
                sender=request.user,
                message=f"Report marked as {decision}. Reason: {resolution_notes}"
            )

            messages.success(request, f"Report has been marked as {decision}.")
            return redirect('order_report', item_id=item_id)
    else:
        resolution_form = ReportResolutionForm()

    context = {
        'order_item': order_item,
        'chat_messages': chat_messages,
        'chat_form': chat_form,
        'report': report,
        'report_form': report_form,
        'resolution_form': resolution_form,
        'status_history': status_history,
        'is_buyer': is_buyer,
        'is_seller': is_seller,
        'is_admin': is_admin,
        'is_support': is_support
    }

    return render(request, 'marketplace/order_report.html', context)

# AJAX views for chat
@login_required
@require_POST
def send_chat_message(request, item_id):
    order_item = get_object_or_404(OrderItem, id=item_id)

    # Check permissions
    is_buyer = order_item.order.buyer == request.user
    is_seller = order_item.product.seller == request.user
    is_admin = hasattr(request.user, 'profile') and request.user.profile.role == 'admin'
    is_support = hasattr(request.user, 'profile') and request.user.profile.role == 'support'

    if not (is_buyer or is_seller or is_admin or is_support):
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    # Check if there's a report and if it's resolved
    try:
        report = Report.objects.get(order_item=order_item)
        # If report is resolved and user is not support, deny message sending
        if report.resolved and not is_support:
            return JsonResponse({
                'status': 'error',
                'message': 'This report has been resolved. Only support staff can send messages.'
            }, status=403)
    except Report.DoesNotExist:
        # No report exists, continue as normal
        pass

    message_text = request.POST.get('message', '').strip()
    if not message_text:
        return JsonResponse({'status': 'error', 'message': 'Message cannot be empty'}, status=400)

    # Create new message
    message = ChatMessage.objects.create(
        order_item=order_item,
        sender=request.user,
        message=message_text
    )

    # Create notification for the other party
    # Determine who should receive the notification
    is_buyer = order_item.order.buyer == request.user
    is_seller = order_item.product.seller == request.user
    is_support = hasattr(request.user, 'profile') and request.user.profile.role == 'support'
    is_admin = hasattr(request.user, 'profile') and request.user.profile.role == 'admin'

    try:
        report = Report.objects.get(order_item=order_item)
    except Report.DoesNotExist:
        report = None

    if is_buyer:
        # Notify the seller and support
        Notification.objects.create(
            user=order_item.product.seller,
            type='message',
            title=f"New message in Report #{report.id if report else order_item.id}",
            message=f"{request.user.username} sent a message regarding order #{order_item.order.id}",
            link=f"/order/item/{order_item.id}/report"
        )

        # Also notify support staff if there's a report
        if report:
            support_users = User.objects.filter(profile__role__in=['support', 'admin'])
            for support_user in support_users:
                if support_user != request.user:  # Don't notify self
                    Notification.objects.create(
                        user=support_user,
                        type='message',
                        title=f"New message in Report #{report.id}",
                        message=f"Buyer {request.user.username} sent a message in a report",
                        link=f"/order/item/{order_item.id}/report"
                    )
    elif is_seller:
        # Notify the buyer and support
        Notification.objects.create(
            user=order_item.order.buyer,
            type='message',
            title=f"New message in Report #{report.id if report else order_item.id}",
            message=f"{request.user.username} sent a message regarding order #{order_item.order.id}",
            link=f"/order/item/{order_item.id}/report"
        )

        # Also notify support staff if there's a report
        if report:
            support_users = User.objects.filter(profile__role__in=['support', 'admin'])
            for support_user in support_users:
                if support_user != request.user:  # Don't notify self
                    Notification.objects.create(
                        user=support_user,
                        type='message',
                        title=f"New message in Report #{report.id}",
                        message=f"Seller {request.user.username} sent a message in a report",
                        link=f"/order/item/{order_item.id}/report"
                    )
    elif is_support or is_admin:
        # Notify both buyer and seller
        Notification.objects.create(
            user=order_item.order.buyer,
            type='message',
            title=f"New message from support in Report #{report.id if report else order_item.id}",
            message=f"Support staff sent a message regarding order #{order_item.order.id}",
            link=f"/order/item/{order_item.id}/report"
        )

        Notification.objects.create(
            user=order_item.product.seller,
            type='message',
            title=f"New message from support in Report #{report.id if report else order_item.id}",
            message=f"Support staff sent a message regarding order #{order_item.order.id}",
            link=f"/order/item/{order_item.id}/report"
        )

    return JsonResponse({
        'status': 'success',
        'message': {
            'sender': message.sender.username,
            'text': message.message,
            'timestamp': message.created_at.strftime('%b %d, %Y, %I:%M %p')
        }
    })

@login_required
def get_chat_messages(request, item_id):
    order_item = get_object_or_404(OrderItem, id=item_id)

    # Check permissions
    is_buyer = order_item.order.buyer == request.user
    is_seller = order_item.product.seller == request.user
    is_admin = hasattr(request.user, 'profile') and request.user.profile.role == 'admin'
    is_support = hasattr(request.user, 'profile') and request.user.profile.role == 'support'

    if not (is_buyer or is_seller or is_admin or is_support):
        return JsonResponse({'status': 'error', 'message': 'Permission denied'}, status=403)

    # Get timestamp of last seen message
    last_seen = request.GET.get('last_seen')
    if last_seen:
        try:
            last_seen_dt = timezone.datetime.fromisoformat(last_seen)
            messages = ChatMessage.objects.filter(
                order_item=order_item,
                created_at__gt=last_seen_dt
            ).order_by('created_at')
        except ValueError:
            messages = ChatMessage.objects.filter(order_item=order_item).order_by('created_at')
    else:
        messages = ChatMessage.objects.filter(order_item=order_item).order_by('created_at')

    messages_data = [{
        'id': msg.id,
        'sender': msg.sender.username,
        'text': msg.message,
        'timestamp': msg.created_at.isoformat(),
        'formatted_time': msg.created_at.strftime('%b %d, %Y, %I:%M %p'),
        'is_sender': msg.sender == request.user
    } for msg in messages]

    # Check if there's a report and if it's resolved
    report_status = None
    try:
        report = Report.objects.get(order_item=order_item)
        if report.resolved:
            report_status = {
                'resolved': True,
                'decision': report.decision,
                'can_message': is_support
            }
    except Report.DoesNotExist:
        pass

    return JsonResponse({
        'status': 'success',
        'messages': messages_data,
        'report_status': report_status
    })


# User Profile Views
@login_required
def profile_view(request):
    """View for displaying user profile information"""
    user = request.user

    # Get user's profile information
    profile = user.profile

    # Get user's recent activity
    recent_orders = Order.objects.filter(buyer=user).order_by('-created_at')[:5]
    recent_reports = Report.objects.filter(created_by=user).order_by('-created_at')[:5]
    recent_tickets = Ticket.objects.filter(user=user).order_by('-created_at')[:5]

    # For sellers, get their products
    products = []
    if profile.role == 'seller':
        products = Item.objects.filter(seller=user, is_active=True).order_by('-created_at')[:5]

    context = {
        'user': user,
        'profile': profile,
        'recent_orders': recent_orders,
        'recent_reports': recent_reports,
        'recent_tickets': recent_tickets,
        'products': products,
    }

    return render(request, 'marketplace/profile.html', context)

@login_required
def edit_profile(request):
    """View for editing user profile information"""
    user = request.user

    # Handle password change form submission
    if request.method == 'POST' and 'change_password_form' in request.POST:
        password_form = CustomPasswordChangeForm(user=request.user, data=request.POST)
        if password_form.is_valid():
            password_form.save()
            # Update the session to prevent the user from being logged out
            from django.contrib.auth import update_session_auth_hash
            update_session_auth_hash(request, password_form.user)
            messages.success(request, "Your password has been changed successfully!")
            return redirect('edit_profile')
        profile_form = ProfileForm(instance=user, user=user)
    # Handle profile form submission
    elif request.method == 'POST':
        profile_form = ProfileForm(request.POST, instance=user, user=user)
        if profile_form.is_valid():
            profile_form.save()
            messages.success(request, "Profile updated successfully!")
            return redirect('profile')
        password_form = CustomPasswordChangeForm(user=request.user)
    else:
        profile_form = ProfileForm(instance=user, user=user)
        password_form = CustomPasswordChangeForm(user=request.user)

    context = {
        'form': profile_form,
        'password_form': password_form
    }

    return render(request, 'marketplace/edit_profile.html', context)

# Change password functionality has been moved to the profile view


# Announcement Management Views
@login_required
def announcement_list(request):
    """View for listing all announcements (admin/support only)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    # Sort announcements by most recently added first
    announcements = Announcement.objects.all().order_by('-created_at')

    return render(request, 'marketplace/announcement_list.html', {
        'announcements': announcements
    })

@login_required
def create_announcement(request):
    """View for creating a new announcement (admin/support only)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    if request.method == 'POST':
        form = AnnouncementForm(request.POST)
        if form.is_valid():
            announcement = form.save(commit=False)
            announcement.created_by = request.user
            announcement.save()
            messages.success(request, "Announcement created successfully!")
            return redirect('announcement_list')
    else:
        form = AnnouncementForm()

    return render(request, 'marketplace/announcement_form.html', {
        'form': form,
        'action': 'Create'
    })

@login_required
def edit_announcement(request, announcement_id):
    """View for editing an existing announcement (admin/support only)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    announcement = get_object_or_404(Announcement, id=announcement_id)

    if request.method == 'POST':
        form = AnnouncementForm(request.POST, instance=announcement)
        if form.is_valid():
            form.save()
            messages.success(request, "Announcement updated successfully!")
            return redirect('announcement_list')
    else:
        form = AnnouncementForm(instance=announcement)

    return render(request, 'marketplace/announcement_form.html', {
        'form': form,
        'announcement': announcement,
        'action': 'Edit'
    })

@login_required
def delete_announcement(request, announcement_id):
    """View for deleting an announcement (admin/support only)"""
    if not hasattr(request.user, 'profile') or request.user.profile.role not in ['admin', 'support']:
        messages.error(request, "Access denied. You need admin or support privileges.")
        return redirect('index')

    announcement = get_object_or_404(Announcement, id=announcement_id)

    if request.method == 'POST':
        announcement.delete()
        messages.success(request, "Announcement deleted successfully!")
        return redirect('announcement_list')

    return render(request, 'marketplace/announcement_confirm_delete.html', {
        'announcement': announcement
    })
