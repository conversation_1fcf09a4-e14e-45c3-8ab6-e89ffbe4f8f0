from django.contrib import admin
from django.contrib import messages
from .models import (
    UserProfile, Item, Order,
    OrderItem, Payment, ChatMessage,
    Report, OrderStatusHistory,
    BalanceTopUp, Announcement
)

class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'role', 'balance', 'wallet_address')
    list_filter = ('role',)
    search_fields = ('user__username', 'user__email', 'wallet_address')

    def save_model(self, request, obj, form, change):
        """Override save_model to use change_role method when role is changed"""
        if change:  # If this is an edit (not a new object)
            old_obj = UserProfile.objects.get(pk=obj.pk)
            if old_obj.role != obj.role:
                # Role has changed, use change_role method
                obj.change_role(obj.role, changed_by=request.user)
                messages.success(request, f"User role changed to {obj.get_role_display()} and username updated to match.")
            else:
                # No role change, use normal save
                super().save_model(request, obj, form, change)
        else:
            # New object, use normal save
            super().save_model(request, obj, form, change)

# Register models
admin.site.register(UserProfile, UserProfileAdmin)
admin.site.register(Item)
admin.site.register(Order)
admin.site.register(OrderItem)
admin.site.register(Payment)
admin.site.register(ChatMessage)
admin.site.register(Report)
admin.site.register(OrderStatusHistory)
admin.site.register(BalanceTopUp)

class AnnouncementAdmin(admin.ModelAdmin):
    list_display = ('title', 'created_by', 'created_at', 'priority', 'is_active')
    list_filter = ('priority', 'is_active', 'created_at')
    search_fields = ('title', 'content')

    def save_model(self, request, obj, form, change):
        if not change:  # If this is a new object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

admin.site.register(Announcement, AnnouncementAdmin)
