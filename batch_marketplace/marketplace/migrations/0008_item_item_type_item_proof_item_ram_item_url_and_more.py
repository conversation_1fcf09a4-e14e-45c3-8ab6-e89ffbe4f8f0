# Generated by Django 5.2 on 2025-05-17 07:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("marketplace", "0007_remove_ticket_priority"),
    ]

    operations = [
        migrations.AddField(
            model_name="item",
            name="item_type",
            field=models.CharField(
                choices=[("vps", "VPS/RDP"), ("account", "Account")],
                default="vps",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="proof",
            field=models.TextField(
                blank=True, help_text="Proof of account ownership", null=True
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="ram",
            field=models.Char<PERSON>ield(
                blank=True,
                help_text="RAM amount for VPS/RDP",
                max_length=100,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="item",
            name="url",
            field=models.CharField(
                blank=True, help_text="Login URL for account", max_length=500, null=True
            ),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="item",
            name="login_url",
            field=models.CharField(
                blank=True,
                help_text="IP address for VPS/RDP",
                max_length=500,
                null=True,
            ),
        ),
    ]
