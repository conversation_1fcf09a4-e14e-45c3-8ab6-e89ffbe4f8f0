# Generated by Django 4.2.21 on 2025-05-17 11:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("marketplace", "0009_item_proof_link_alter_item_proof"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="item",
            name="proof_link",
        ),
        migrations.CreateModel(
            name="Announcement",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("content", models.TextField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.<PERSON><PERSON>an<PERSON>ield(default=True)),
                (
                    "priority",
                    models.Char<PERSON>ield(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "icon",
                    models.<PERSON>r<PERSON>ield(
                        default="fa-bullhorn",
                        help_text="Font Awesome icon class (e.g., fa-bullhorn)",
                        max_length=50,
                    ),
                ),
                (
                    "bg_color",
                    models.CharField(
                        default="bg-gradient-to-br from-purple-400 to-indigo-600",
                        help_text="Tailwind CSS background color class",
                        max_length=50,
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="announcements",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
    ]
