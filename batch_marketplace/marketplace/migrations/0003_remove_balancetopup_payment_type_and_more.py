# Generated by Django 5.2 on 2025-05-08 08:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("marketplace", "0002_vpsproduct_buyer_is_sold"),
    ]

    operations = [
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_ipn_callback_url",
            field=models.URLField(blank=True, max_length=500, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_order_description",
            field=models.CharField(blank=True, max_length=200, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_order_id",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_outcome_amount",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=20, null=True
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="balancetopup",
            name="nowpayments_outcome_currency",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_pay_address",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_pay_amount",
            field=models.DecimalField(
                blank=True, decimal_places=8, max_digits=20, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_pay_currency",
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_payment_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_price_amount",
            field=models.DecimalField(
                blank=True, decimal_places=2, max_digits=10, null=True
            ),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_price_currency",
            field=models.CharField(blank=True, default="USD", max_length=10, null=True),
        ),
        migrations.AddField(
            model_name="balancetopup",
            name="nowpayments_purchase_id",
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="balancetopup",
            name="method",
            field=models.CharField(
                choices=[
                    ("manual", "Manual Verification"),
                    ("automatic", "Automatic Verification"),
                    ("nowpayments", "NOWPayments"),
                ],
                default="nowpayments",
                max_length=20,
            ),
        ),
        migrations.AlterField(
            model_name="balancetopup",
            name="payment_status",
            field=models.CharField(
                choices=[
                    ("waiting", "Waiting for Payment"),
                    ("confirming", "Confirming"),
                    ("confirmed", "Confirmed"),
                    ("failed", "Failed"),
                    ("expired", "Expired"),
                    ("finished", "Finished"),
                    ("partially_paid", "Partially Paid"),
                    ("refunded", "Refunded"),
                ],
                default="waiting",
                max_length=20,
            ),
        ),
    ]
