from django.urls import path
from django.contrib.auth import views as auth_views
from . import views

urlpatterns = [
    # Home page
    path('', views.index, name='index'),

    # Authentication
    path('login/', views.login_view, name='login'),
    path('register/', views.register_view, name='register'),
    path('logout/', views.logout_view, name='logout'),

    # Dashboards
    path('buyer/', views.buyer_dashboard, name='buyer_dashboard'),
    path('seller/', views.seller_dashboard, name='seller_dashboard'),
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('support-dashboard/', views.support_dashboard, name='support_dashboard'),

    # VPS Products
    path('products/', views.vps_list, name='vps_list'),
    path('product/<int:product_id>/', views.vps_detail, name='vps_detail'),
    path('product/<int:product_id>/details/', views.get_product_details, name='get_product_details'),
    path('product/add/', views.add_vps_product, name='add_vps_product'),
    path('product/<int:product_id>/edit/', views.edit_vps_product, name='edit_vps_product'),
    path('product/<int:product_id>/delete/', views.delete_vps_product, name='delete_vps_product'),
    path('item/<int:item_id>/delete/', views.delete_item, name='delete_item'),

    # Temporary redirect for add_account
    path('account/add/', views.add_account, name='add_account'),


    # Orders
    path('order/create/<int:product_id>/', views.create_order, name='create_order'),
    path('order/<int:order_id>/payment/', views.payment_view, name='payment'),
    path('order/item/<int:item_id>/report/', views.order_report, name='order_report'),
    path('order/<int:order_id>/details/', views.get_order_details, name='get_order_details'),

    # Payment and balance management
    path('verify-payment/<int:payment_id>/', views.verify_payment, name='verify_payment'),
    path('topup-balance/', views.topup_balance, name='topup_balance'),
    path('verify-topup/<int:topup_id>/', views.verify_topup, name='verify_topup'),
    path('nowpayments/webhook/', views.nowpayments_webhook, name='nowpayments_webhook'),

    # Chat API
    path('chat/send/<int:item_id>/', views.send_chat_message, name='send_chat_message'),
    path('chat/get/<int:item_id>/', views.get_chat_messages, name='get_chat_messages'),

    # Ticket System
    path('tickets/', views.ticket_list, name='ticket_list'),
    path('tickets/create/', views.create_ticket, name='create_ticket'),
    path('tickets/<int:ticket_id>/', views.ticket_detail, name='ticket_detail'),

    # Reports
    path('reports/', views.user_reports, name='user_reports'),

    # Withdrawal System
    path('withdrawals/', views.withdrawal_request, name='withdrawal_request'),
    path('admin/withdrawals/', views.withdrawal_list, name='withdrawal_list'),
    path('admin/withdrawals/<int:withdrawal_id>/', views.process_withdrawal, name='process_withdrawal'),

    # User Role Management
    path('admin/users/', views.manage_user_roles, name='manage_user_roles'),
    path('admin/users/<int:user_id>/change-role/', views.change_user_role, name='change_user_role'),

    # Notifications
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('notifications/mark-all-read/', views.mark_all_notifications_read, name='mark_all_notifications_read'),
    path('notifications/<int:notification_id>/delete/', views.delete_notification, name='delete_notification'),

    # User Profile
    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),

    # Announcements
    path('announcements/', views.announcement_list, name='announcement_list'),
    path('announcements/create/', views.create_announcement, name='create_announcement'),
    path('announcements/<int:announcement_id>/edit/', views.edit_announcement, name='edit_announcement'),
    path('announcements/<int:announcement_id>/delete/', views.delete_announcement, name='delete_announcement'),

    # Password Reset
    path('password-reset/',
         auth_views.PasswordResetView.as_view(
             template_name='marketplace/password_reset_form_tailwind.html',
             email_template_name='marketplace/password_reset_email.html',
             subject_template_name='marketplace/password_reset_subject.txt',
             success_url='/password-reset/done/'
         ),
         name='password_reset'),
    path('password-reset/done/',
         auth_views.PasswordResetDoneView.as_view(
             template_name='marketplace/password_reset_done_tailwind.html'
         ),
         name='password_reset_done'),
    path('password-reset-confirm/<uidb64>/<token>/',
         auth_views.PasswordResetConfirmView.as_view(
             template_name='marketplace/password_reset_confirm_tailwind.html',
             success_url='/password-reset-complete/'
         ),
         name='password_reset_confirm'),
    path('password-reset-complete/',
         auth_views.PasswordResetCompleteView.as_view(
             template_name='marketplace/password_reset_complete_tailwind.html'
         ),
         name='password_reset_complete'),
]
