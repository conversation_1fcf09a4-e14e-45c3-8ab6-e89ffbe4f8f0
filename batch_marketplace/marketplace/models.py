from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone

# User roles
ROLE_CHOICES = (
    ('buyer', 'Buyer'),
    ('seller', 'Seller'),
    ('admin', 'Admin'),
    ('support', 'Support'),
)

# Order statuses
STATUS_CHOICES = (
    ('pending', 'Pending'),
    ('paid', 'Paid'),
    ('delivered', 'Delivered'),
    ('rejected', 'Rejected'),
    ('refunded', 'Refunded'),
)

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default='buyer')
    wallet_address = models.CharField(max_length=100, blank=True, null=True)
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    last_role_change = models.DateTimeField(null=True, blank=True)
    role_changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='role_changes')

    def __str__(self):
        return f"{self.user.username} - {self.role}"

    def generate_unique_username(self, role):
        """Generate a unique username based on role with incremental number"""
        base_name = role.lower()
        from django.contrib.auth.models import User

        # Find the highest number for this role
        import re
        pattern = f"^{base_name}(\\d+)$"

        # Get all usernames that match the pattern (e.g., buyer1, buyer2, etc.)
        matching_users = User.objects.filter(username__regex=pattern)

        # Start with 1 or the highest existing number + 1
        new_number = 1

        if matching_users.exists():
            # Extract numbers from usernames and find the highest
            numbers = []
            for user in matching_users:
                match = re.match(pattern, user.username)
                if match:
                    try:
                        numbers.append(int(match.group(1)))
                    except (ValueError, IndexError):
                        pass

            if numbers:
                new_number = max(numbers) + 1

        # Create the new username
        username = f"{base_name}{new_number}"

        # Double-check it's unique (just in case)
        while User.objects.filter(username=username).exists():
            new_number += 1
            username = f"{base_name}{new_number}"

        return username

    def change_role(self, new_role, changed_by=None):
        """Change user role and create notification"""
        old_role = self.role
        self.role = new_role
        self.last_role_change = timezone.now()
        self.role_changed_by = changed_by
        self.save()

        # Update the username to match the new role
        new_username = self.generate_unique_username(new_role)
        self.user.username = new_username
        self.user.save()

        # Create notification for the user
        Notification.objects.create(
            user=self.user,
            type='role_change',
            title='Your account role has changed',
            message=f'Your role has been changed from {self.get_role_display(old_role)} to {self.get_role_display()}.',
            link='/profile/'
        )

        return True

    def get_role_display(self, role_value=None):
        """Get the display name for a role"""
        if role_value is None:
            role_value = self.role

        for role, display in ROLE_CHOICES:
            if role == role_value:
                return display
        return role_value

    def is_admin(self):
        """Check if user has admin role"""
        return self.role == 'admin'

    def is_support(self):
        """Check if user has support role"""
        return self.role == 'support'

    def is_seller(self):
        """Check if user has seller role"""
        return self.role == 'seller'

    def is_buyer(self):
        """Check if user has buyer role"""
        return self.role == 'buyer'

    def is_staff(self):
        """Check if user has admin or support role"""
        return self.role in ['admin', 'support']

class Item(models.Model):
    CATEGORY_CHOICES = (
        ('accounts', 'Accounts'),
        ('vps', 'VPS'),
        ('rdp', 'RDP'),
        ('hosting', 'Hosting'),
        ('other', 'Other'),
    )

    ITEM_TYPE_CHOICES = (
        ('vps', 'VPS/RDP'),
        ('account', 'Account'),
    )

    seller = models.ForeignKey(User, on_delete=models.CASCADE, related_name='products')
    title = models.CharField(max_length=200)
    description = models.TextField()
    specifications = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    image_url = models.CharField(max_length=500, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    is_sold = models.BooleanField(default=False)
    buyer = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='purchased_products')

    # Item type field to distinguish between VPS/RDP and Account
    item_type = models.CharField(max_length=20, choices=ITEM_TYPE_CHOICES, default='vps')

    # Additional fields for item details
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='accounts')

    # Common fields for both types
    username = models.CharField(max_length=100, null=True, blank=True)
    password = models.CharField(max_length=100, null=True, blank=True)

    # Fields for VPS/RDP items
    login_url = models.CharField(max_length=500, null=True, blank=True, help_text="IP address for VPS/RDP")
    ram = models.CharField(max_length=100, null=True, blank=True, help_text="RAM amount for VPS/RDP")

    # Fields for Account items
    url = models.CharField(max_length=500, null=True, blank=True, help_text="Login URL for account")
    proof = models.TextField(null=True, blank=True, help_text="Description of account")

    # Legacy field
    two_factor_bypass = models.CharField(max_length=500, null=True, blank=True)

    def __str__(self):
        return self.title

    @property
    def has_report(self):
        """Check if this item has any reports through order items"""
        return hasattr(self, 'orderitem') and hasattr(self.orderitem, 'report')

class Order(models.Model):
    buyer = models.ForeignKey(User, on_delete=models.CASCADE, related_name='orders')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)

    def __str__(self):
        return f"Order #{self.id} - {self.buyer.username}"

    @property
    def order_status(self):
        # Return the current status of the order based on its items
        statuses = [item.status for item in self.items.all()]
        if all(status == 'delivered' for status in statuses):
            return 'delivered'
        elif all(status == 'refunded' for status in statuses):
            return 'refunded'
        elif any(status == 'rejected' for status in statuses):
            return 'has_rejected'
        elif all(status == 'paid' for status in statuses):
            return 'paid'
        else:
            return 'pending'

class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Item, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    def __str__(self):
        return f"{self.product.title} - {self.status}"

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        old_status = None if is_new else OrderItem.objects.get(pk=self.pk).status

        # Save the order item
        super().save(*args, **kwargs)

        # Create status history if status changed or new item
        if is_new or old_status != self.status:
            OrderStatusHistory.objects.create(
                order_item=self,
                old_status=old_status,
                new_status=self.status
            )

class Payment(models.Model):
    order = models.OneToOneField(Order, on_delete=models.CASCADE, related_name='payment')
    txid = models.CharField(max_length=200)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"Payment for Order #{self.order.id} - {self.txid[:10]}..."

class ChatMessage(models.Model):
    order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        return f"Message from {self.sender.username} at {self.created_at}"

class Report(models.Model):
    DECISION_CHOICES = (
        ('pending', 'Pending'),
        ('rejected', 'Rejected'),
        ('refunded', 'Refunded'),
    )

    order_item = models.OneToOneField(OrderItem, on_delete=models.CASCADE, related_name='report')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    issue = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_reports')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, null=True)
    decision = models.CharField(max_length=20, choices=DECISION_CHOICES, default='pending')
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_reports')

    def __str__(self):
        return f"Report for {self.order_item.product.title} - {'Resolved' if self.resolved else 'Open'}"

    def resolve(self, user, notes, decision):
        self.resolved = True
        self.resolved_by = user
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.decision = decision
        self.save()

    def get_buyer(self):
        return self.order_item.order.buyer

    def get_seller(self):
        return self.order_item.product.seller

class ReportMessage(models.Model):
    report = models.ForeignKey(Report, on_delete=models.CASCADE, related_name='messages')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_internal_note = models.BooleanField(default=False)  # For support staff only notes

    def __str__(self):
        return f"Message from {self.user.username} on Report #{self.report.id}"

    class Meta:
        ordering = ['created_at']

class OrderStatusHistory(models.Model):
    order_item = models.ForeignKey(OrderItem, on_delete=models.CASCADE, related_name='status_history')
    old_status = models.CharField(max_length=20, choices=STATUS_CHOICES, null=True, blank=True)
    new_status = models.CharField(max_length=20, choices=STATUS_CHOICES)
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-changed_at']

    def __str__(self):
        return f"{self.order_item.product.title}: {self.old_status or 'New'} → {self.new_status}"

class BalanceTopUp(models.Model):
    PAYMENT_STATUS_CHOICES = (
        ('waiting', 'Waiting for Payment'),
        ('confirming', 'Confirming'),
        ('confirmed', 'Confirmed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
        ('finished', 'Finished'),
        ('partially_paid', 'Partially Paid'),
        ('refunded', 'Refunded'),
    )

    METHOD_CHOICES = (
        ('manual', 'Manual Verification'),
        ('automatic', 'Automatic Verification'),
        ('nowpayments', 'NOWPayments'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='topups')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    txid = models.CharField(max_length=200, null=True, blank=True)
    verified = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    verified_at = models.DateTimeField(null=True, blank=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_topups')

    # Payment processing fields
    method = models.CharField(max_length=20, choices=METHOD_CHOICES, default='nowpayments')
    payment_id = models.CharField(max_length=100, null=True, blank=True)
    payment_address = models.CharField(max_length=100, null=True, blank=True)
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='waiting')
    payment_expires_at = models.DateTimeField(null=True, blank=True)
    payment_type = models.CharField(max_length=20, default='trc20')

    # NOWPayments specific fields
    nowpayments_payment_id = models.CharField(max_length=100, null=True, blank=True)
    nowpayments_pay_address = models.CharField(max_length=100, null=True, blank=True)
    nowpayments_pay_amount = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    nowpayments_pay_currency = models.CharField(max_length=20, null=True, blank=True)
    nowpayments_price_amount = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    nowpayments_price_currency = models.CharField(max_length=10, null=True, blank=True, default='USD')
    nowpayments_ipn_callback_url = models.URLField(max_length=500, null=True, blank=True)
    nowpayments_order_id = models.CharField(max_length=100, null=True, blank=True)
    nowpayments_order_description = models.CharField(max_length=200, null=True, blank=True)
    nowpayments_purchase_id = models.CharField(max_length=100, null=True, blank=True)
    nowpayments_outcome_amount = models.DecimalField(max_digits=20, decimal_places=8, null=True, blank=True)
    nowpayments_outcome_currency = models.CharField(max_length=20, null=True, blank=True)

    def __str__(self):
        return f"TopUp of ${self.amount} by {self.user.username} - {'Verified' if self.verified else self.get_payment_status_display()}"

    def update_status_from_payment(self, payment_data):
        """Update status based on payment data from payment processor"""
        if not payment_data or not payment_data.get('success'):
            return False

        data = payment_data.get('details', {})
        status = data.get('payment_status', '').lower()

        # Update payment status
        self.payment_status = status

        # If payment is confirmed or finished, verify it
        if status in ['confirmed', 'finished'] and not self.verified:
            self.verified = True
            self.verified_at = timezone.now()
            self.txid = data.get('pay_txid') or data.get('transaction_id')

            # Update user balance
            profile = self.user.profile
            profile.balance += self.amount
            profile.save()

            # Create notification
            Notification.objects.create(
                user=self.user,
                type='payment',
                title="Balance Top-up Complete",
                message=f"Your top-up of ${self.amount} has been verified and added to your balance.",
                link='/topup-balance/'
            )

        self.save()
        return True

    def update_from_nowpayments(self, payment_data):
        """Update topup details from NOWPayments API response"""
        if not payment_data:
            return False

        # Debug print to see what's in the payment_data
        print(f"Payment data keys: {payment_data.keys()}")

        # Update basic payment info
        self.nowpayments_payment_id = payment_data.get('payment_id')

        # Check for pay_address in different possible locations in the API response
        pay_address = payment_data.get('pay_address')
        if not pay_address and 'payin_address' in payment_data:
            pay_address = payment_data.get('payin_address')
        if not pay_address and 'pay_in_address' in payment_data:
            pay_address = payment_data.get('pay_in_address')

        self.nowpayments_pay_address = pay_address
        self.nowpayments_pay_amount = payment_data.get('pay_amount')
        self.nowpayments_pay_currency = payment_data.get('pay_currency')
        self.nowpayments_price_amount = payment_data.get('price_amount')
        self.nowpayments_price_currency = payment_data.get('price_currency')
        self.payment_id = payment_data.get('payment_id')
        self.payment_address = pay_address

        # Debug print to confirm the address was set
        print(f"Payment address set to: {self.payment_address}")

        # Set payment status
        status = payment_data.get('payment_status', '').lower()
        if status:
            self.payment_status = status

        # If payment is confirmed or finished, verify it
        if status in ['confirmed', 'finished'] and not self.verified:
            self.verified = True
            self.verified_at = timezone.now()
            self.txid = payment_data.get('pay_txid')

            # Update user balance
            profile = self.user.profile
            profile.balance += self.amount
            profile.save()

            # Create notification
            Notification.objects.create(
                user=self.user,
                type='payment',
                title="Balance Top-up Complete",
                message=f"Your top-up of ${self.amount} has been verified and added to your balance.",
                link='/topup-balance/'
            )

        self.save()
        return True

# Support ticket system
class Ticket(models.Model):
    STATUS_CHOICES = (
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('waiting', 'Waiting for Customer'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    )

    CATEGORY_CHOICES = (
        ('general', 'General Inquiry'),
        ('account', 'Account Issue'),
        ('payment', 'Payment Problem'),
        ('product', 'Product Question'),
        ('technical', 'Technical Support'),
        ('other', 'Other'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tickets')
    subject = models.CharField(max_length=200)
    description = models.TextField()
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='general')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tickets')
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_tickets')

    def __str__(self):
        return f"Ticket #{self.id}: {self.subject} ({self.status})"

class TicketMessage(models.Model):
    ticket = models.ForeignKey(Ticket, on_delete=models.CASCADE, related_name='messages')
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_internal_note = models.BooleanField(default=False)  # For support staff only notes

    def __str__(self):
        return f"Message from {self.user.username} on Ticket #{self.ticket.id}"

class Notification(models.Model):
    TYPE_CHOICES = (
        ('message', 'New Message'),
        ('ticket_update', 'Ticket Update'),
        ('report_decision', 'Report Decision'),
        ('role_change', 'Role Change'),
        ('payment', 'Payment Update'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    type = models.CharField(max_length=20, choices=TYPE_CHOICES)
    title = models.CharField(max_length=100)
    message = models.TextField()
    link = models.CharField(max_length=200, blank=True, null=True)  # URL to redirect when clicked
    created_at = models.DateTimeField(auto_now_add=True)
    read = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.get_type_display()} for {self.user.username}: {self.title}"

class Withdrawal(models.Model):
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
        ('completed', 'Completed'),
    )

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='withdrawals')
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    wallet_address = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    txid = models.CharField(max_length=200, blank=True, null=True)  # Transaction ID after processing
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    processed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='processed_withdrawals')
    notes = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Withdrawal of ${self.amount} by {self.user.username} - {self.get_status_display()}"


class Announcement(models.Model):
    PRIORITY_CHOICES = (
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
    )

    title = models.CharField(max_length=200)
    content = models.TextField()
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='announcements')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='medium')
    icon = models.CharField(max_length=50, default='fa-bullhorn', help_text="Font Awesome icon class (e.g., fa-bullhorn)")
    bg_color = models.CharField(max_length=50, default='bg-gradient-to-br from-purple-400 to-indigo-600',
                               help_text="Tailwind CSS background color class")

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.title



