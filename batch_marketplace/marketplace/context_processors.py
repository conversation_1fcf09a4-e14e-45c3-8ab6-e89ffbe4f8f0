"""
Context processors for the marketplace app.
"""
from django.conf import settings
from django.db.models import Sum
from .models import Notification, OrderItem

def settings_context(request):
    """
    Add settings and notification data to the template context.
    This makes these variables available in all templates.
    """
    context = {
        'site_name': getattr(settings, 'SITE_NAME', 'Oleer Market'),
        'site_description': getattr(settings, 'SITE_DESCRIPTION', 'Online Marketplace for Digital Products'),
    }

    # Add notification data for authenticated users
    if request.user.is_authenticated:
        # Get unread notification count
        unread_notification_count = Notification.objects.filter(
            user=request.user,
            read=False
        ).count()

        # Get recent notifications (limited to 5)
        recent_notifications = Notification.objects.filter(
            user=request.user
        ).order_by('-created_at')[:5]

        context.update({
            'unread_notification_count': unread_notification_count,
            'recent_notifications': recent_notifications,
        })

        # Add total gained balance for sellers
        if hasattr(request.user, 'profile') and request.user.profile.role == 'seller':
            total_gained_balance = OrderItem.objects.filter(
                product__seller=request.user,
                status='paid'
            ).aggregate(Sum('price'))['price__sum'] or 0

            context.update({
                'total_gained_balance': total_gained_balance,
            })

    return context
