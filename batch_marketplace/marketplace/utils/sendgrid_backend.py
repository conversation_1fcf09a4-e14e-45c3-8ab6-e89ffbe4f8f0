"""
Custom email backend for SendGrid
"""
import logging
import sendgrid
from sendgrid.helpers.mail import Mail
from django.conf import settings
from django.core.mail.backends.base import BaseEmailBackend

logger = logging.getLogger(__name__)

class SendGridEmailBackend(BaseEmailBackend):
    """
    Custom email backend for SendGrid
    """
    def __init__(self, fail_silently=False, **kwargs):
        super().__init__(fail_silently=fail_silently, **kwargs)
        self.sg = sendgrid.SendGridAPIClient(api_key=settings.EMAIL_HOST_PASSWORD)
        self.from_email = settings.DEFAULT_FROM_EMAIL
        self.from_name = getattr(settings, 'DEFAULT_FROM_NAME', 'Batch VPS Marketplace')

    def send_messages(self, email_messages):
        """
        Send one or more EmailMessage objects and return the number of email
        messages sent.
        """
        if not email_messages:
            return 0

        num_sent = 0
        for message in email_messages:
            sent = self._send(message)
            if sent:
                num_sent += 1
        return num_sent

    def _send(self, email_message):
        """
        Send an EmailMessage using SendGrid API
        """
        if not email_message.recipients():
            return False

        try:
            # Extract HTML content if available
            html_content = None
            for content, mimetype in email_message.alternatives:
                if mimetype == 'text/html':
                    html_content = content
                    break

            # For password reset emails, keep them as plain text
            # Don't convert plain text to HTML with <br> tags

            # Log the from email address being used
            logger.info(f"Sending email from: {self.from_email} ({self.from_name})")
            print(f"Sending email from: {self.from_email} ({self.from_name})")

            # Check if this is a password reset email
            is_password_reset = "Password Reset" in email_message.subject

            # Create a simple message dictionary
            message = {
                "personalizations": [
                    {
                        "to": [{"email": email_message.to[0]}],
                        "subject": email_message.subject
                    }
                ],
                "from": {
                    "email": self.from_email,
                    "name": self.from_name
                },
                # Disable click tracking for password reset emails for security
                "tracking_settings": {
                    "click_tracking": {
                        "enable": False,
                        "enable_text": False
                    },
                    "open_tracking": {
                        "enable": False
                    }
                }
            }

            # For password reset emails, use the body as HTML content
            if is_password_reset:
                message["content"] = [
                    {
                        "type": "text/html",
                        "value": email_message.body
                    }
                ]
            # For other emails, use HTML content if provided, otherwise use plain text
            elif html_content:
                message["content"] = [
                    {
                        "type": "text/html",
                        "value": html_content
                    }
                ]
            else:
                message["content"] = [
                    {
                        "type": "text/plain",
                        "value": email_message.body
                    }
                ]

            # Send the email
            try:
                response = self.sg.client.mail.send.post(request_body=message)

                # Log the response with detailed information
                logger.info(f"SendGrid API response: {response.status_code}")
                print(f"SendGrid API response status code: {response.status_code}")

                # Print the full response for debugging
                print("SendGrid API response headers:")
                for header, value in response.headers.items():
                    print(f"  {header}: {value}")

                # Print the response body if available
                try:
                    print("SendGrid API response body:")
                    print(response.body.decode('utf-8') if hasattr(response, 'body') and response.body else "No response body")
                except Exception as e:
                    print(f"Error decoding response body: {e}")

                # Print the request that was sent
                print("SendGrid API request:")
                print(f"  Message: {message}")

                if response.status_code >= 200 and response.status_code < 300:
                    print("Email sent successfully!")
                    return True
                else:
                    logger.error(f"SendGrid API error: {response.status_code}")
                    print(f"SendGrid API error: {response.status_code}")

                    # Fall back to console output for development
                    if settings.DEBUG:
                        print("\n----- EMAIL THAT WOULD HAVE BEEN SENT -----")
                        print(f"From: {self.from_name} <{self.from_email}>")
                        print(f"To: {email_message.to[0]}")
                        print(f"Subject: {email_message.subject}")
                        print("\nBody:")
                        print(html_content if html_content else email_message.body)
                        print("----- END OF EMAIL -----\n")

                        # In debug mode, pretend the email was sent
                        return True

                    return False
            except Exception as e:
                logger.error(f"SendGrid API request error: {e}")
                print(f"SendGrid API request error: {e}")

                # Print more detailed error information
                import traceback
                print("SendGrid API request error traceback:")
                traceback.print_exc()

                # Print the request that was sent
                print("SendGrid API request that failed:")
                print(f"  Message: {message}")

                # Fall back to console output for development
                if settings.DEBUG:
                    print("\n----- EMAIL THAT WOULD HAVE BEEN SENT -----")
                    print(f"From: {self.from_name} <{self.from_email}>")
                    print(f"To: {email_message.to[0]}")
                    print(f"Subject: {email_message.subject}")
                    print("\nBody:")
                    print(html_content if html_content else email_message.body)
                    print("----- END OF EMAIL -----\n")

                    # In debug mode, pretend the email was sent
                    return True

                return False

        except Exception as e:
            logger.error(f"Error sending email via SendGrid: {e}")
            print(f"Error sending email via SendGrid: {e}")

            # Print more detailed error information
            import traceback
            print("Error traceback:")
            traceback.print_exc()

            # Print email details
            print("Email details:")
            print(f"  From: {self.from_name} <{self.from_email}>")
            print(f"  To: {email_message.to[0] if email_message.to else 'No recipients'}")
            print(f"  Subject: {email_message.subject}")

            if not self.fail_silently:
                raise
            return False
