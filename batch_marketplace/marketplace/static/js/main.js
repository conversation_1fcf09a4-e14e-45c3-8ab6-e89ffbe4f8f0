/**
 * Main JavaScript file for Batch VPS Marketplace
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips with Tippy.js if available
    if (typeof tippy !== 'undefined') {
        tippy('[data-tippy-content]');
    }

    // Global variable to track which dropdown is currently open
    window.currentOpenDropdown = null;

    // Ensure all dropdowns are properly hidden on page load
    function ensureAllDropdownsHidden() {
        // Hide Alpine.js dropdowns
        document.querySelectorAll('[x-data]').forEach(el => {
            if (el.hasAttribute('x-data') && el.getAttribute('x-data').includes('open')) {
                // Find child elements with x-show="open"
                el.querySelectorAll('[x-show="open"]').forEach(dropdown => {
                    if (!dropdown.classList.contains('hidden')) {
                        dropdown.classList.add('hidden');
                    }
                });
            }
        });

        // Hide vanilla JS dropdowns
        document.querySelectorAll('#userMenu, .dropdown-menu').forEach(dropdown => {
            if (!dropdown.classList.contains('hidden')) {
                dropdown.classList.add('hidden');
            }
        });
    }

    // Run on initial page load
    ensureAllDropdownsHidden();

    // Also run after a short delay to catch any late-initializing elements
    setTimeout(ensureAllDropdownsHidden, 100);

    // Handle Alpine.js dropdowns to ensure they close other dropdowns
    document.addEventListener('click', function(e) {
        const alpineDropdownButton = e.target.closest('[x-data*="open"]');
        if (alpineDropdownButton) {
            // If an Alpine.js dropdown button is clicked, close any vanilla JS dropdowns
            if (window.currentOpenDropdown) {
                window.currentOpenDropdown.classList.add('hidden');
                window.currentOpenDropdown = null;
            }
        }
    });

    // Function to toggle a dropdown and ensure only one is open at a time
    function toggleDropdown(dropdown, button) {
        // If this dropdown is already open, just close it
        if (window.currentOpenDropdown === dropdown) {
            dropdown.classList.add('hidden');
            window.currentOpenDropdown = null;
            return;
        }

        // Close any currently open dropdown
        if (window.currentOpenDropdown) {
            window.currentOpenDropdown.classList.add('hidden');
        }

        // Close sidebar on mobile when opening dropdown
        if (window.innerWidth < 1024) {
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                sidebar.classList.add('-translate-x-full');
                sidebar.classList.remove('translate-x-0');

                if (sidebarOverlay) {
                    sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                    sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                }

                // Update Alpine store if available
                if (window.Alpine && Alpine.store('sidebar')) {
                    Alpine.store('sidebar').open = false;
                }
            }
        }

        // Open this dropdown
        dropdown.classList.remove('hidden');
        window.currentOpenDropdown = dropdown;

        // Add click outside listener
        setTimeout(() => {
            const closeDropdown = (event) => {
                if (!dropdown.contains(event.target) && !button.contains(event.target)) {
                    dropdown.classList.add('hidden');
                    window.currentOpenDropdown = null;
                    document.removeEventListener('click', closeDropdown);
                }
            };
            document.addEventListener('click', closeDropdown);
        }, 0);
    }

    // Function to set up dropdown handlers
    function setupDropdownHandlers(buttonSelector, excludeIds) {
        const buttons = document.querySelectorAll(buttonSelector);
        buttons.forEach(button => {
            // Skip buttons with IDs that should be excluded
            if (excludeIds.some(id => button.id === id)) return;

            const dropdown = button.nextElementSibling;
            if (dropdown && dropdown.classList.contains('dropdown-menu')) {
                // Remove any existing event listeners by cloning the button
                const newButton = button.cloneNode(true);
                button.parentNode.replaceChild(newButton, button);

                // Add new event listener
                newButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    // If this dropdown is already open, just close it
                    if (window.currentOpenDropdown === dropdown) {
                        dropdown.classList.add('hidden');
                        window.currentOpenDropdown = null;
                        return;
                    }

                    // Close any currently open dropdown
                    if (window.currentOpenDropdown) {
                        window.currentOpenDropdown.classList.add('hidden');
                        window.currentOpenDropdown = null;
                    }

                    // Close sidebar on mobile when opening dropdown
                    if (window.innerWidth < 1024) {
                        const sidebar = document.getElementById('sidebar');
                        const sidebarOverlay = document.getElementById('sidebarOverlay');

                        if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');

                            if (sidebarOverlay) {
                                sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                                sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                            }

                            // Update Alpine store if available
                            if (window.Alpine && Alpine.store('sidebar')) {
                                Alpine.store('sidebar').open = false;
                            }
                        }
                    }

                    // Open this dropdown
                    dropdown.classList.remove('hidden');
                    window.currentOpenDropdown = dropdown;

                    // Add click outside listener
                    setTimeout(() => {
                        const closeDropdown = (event) => {
                            if (!dropdown.contains(event.target) && !newButton.contains(event.target)) {
                                dropdown.classList.add('hidden');
                                window.currentOpenDropdown = null;
                                document.removeEventListener('click', closeDropdown);
                            }
                        };
                        document.addEventListener('click', closeDropdown);
                    }, 20); // Increased timeout to ensure event binding happens after other events
                });
            }
        });
    }

    // Set up notification dropdowns
    setupDropdownHandlers('.notification-button', ['mainNotificationButton', 'notificationButton']);

    // Set up profile dropdowns
    setupDropdownHandlers('.profile-button', ['mainProfileButton', 'userMenuButton']);

    // Handle notification delete buttons
    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to delete notification
    function deleteNotification(notificationId, notificationItem) {
        const csrftoken = getCookie('csrftoken');

        // Send AJAX request to delete the notification
        fetch(`/notifications/${notificationId}/delete/`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': csrftoken
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Add fade-out animation
                notificationItem.style.transition = 'all 0.3s ease';
                notificationItem.style.opacity = '0';
                notificationItem.style.height = '0';
                notificationItem.style.padding = '0';
                notificationItem.style.margin = '0';
                notificationItem.style.overflow = 'hidden';

                // Remove the notification from the UI after animation
                setTimeout(() => {
                    notificationItem.remove();

                    // Update the notification counter
                    const counter = document.querySelector('.fa-bell').nextElementSibling;
                    if (counter && counter.classList.contains('absolute')) {
                        const currentCount = parseInt(counter.textContent.trim());
                        if (currentCount > 1) {
                            counter.textContent = currentCount - 1;
                        } else {
                            counter.remove();
                        }
                    }

                    // If no notifications left, show the empty message
                    const notificationsList = document.querySelector('.notification-list');
                    if (notificationsList && notificationsList.querySelectorAll('.notification-item').length === 0) {
                        const emptyMessage = document.createElement('div');
                        emptyMessage.className = 'list-group-item';
                        emptyMessage.innerHTML = '<div class="text-center text-muted py-3">You have no new notifications.</div>';
                        notificationsList.appendChild(emptyMessage);
                    }
                }, 300);
            }
        })
        .catch(error => {
            console.error('Error deleting notification:', error);
        });
    }

    // Use event delegation for all notification delete buttons (including dynamically added ones)
    document.addEventListener('click', function(e) {
        // Check if the click target is the delete button or its icon
        const deleteButton = e.target.closest('.notification-delete');
        if (deleteButton) {
            e.preventDefault();
            e.stopPropagation();

            const notificationId = deleteButton.getAttribute('data-notification-id');
            const notificationItem = deleteButton.closest('div');

            if (notificationId && notificationItem) {
                deleteNotification(notificationId, notificationItem);
            }
        }
    });

    // Add smooth scrolling to all links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Handle status filter in dashboards
    const statusFilter = document.getElementById('statusFilter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            const selectedStatus = this.value;
            const allItems = document.querySelectorAll('.order-item');

            allItems.forEach(item => {
                if (selectedStatus === 'all' || item.dataset.status === selectedStatus) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Handle Escape key for closing modals and dropdowns
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            // Close any open Alpine.js dropdowns or modals
            const openElements = document.querySelectorAll('[x-data]');
            openElements.forEach(el => {
                if (el.__x && typeof el.__x.getUnobservedData === 'function') {
                    const data = el.__x.getUnobservedData();
                    if (data && data.open === true) {
                        el.__x.setData('open', false);
                    }
                }
            });

            // Also close vanilla JS dropdowns
            document.querySelectorAll('#userMenu, .dropdown-menu').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });

            // Reset the current open dropdown tracker
            window.currentOpenDropdown = null;

            // Close sidebar on mobile
            if (window.innerWidth < 1024) {
                const sidebar = document.getElementById('sidebar');
                const sidebarOverlay = document.getElementById('sidebarOverlay');

                if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');

                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                        sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                    }

                    // Update Alpine store if available
                    if (window.Alpine && Alpine.store('sidebar')) {
                        Alpine.store('sidebar').open = false;
                    }
                }
            }
        }
    });

    // Form validation for payment form
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        paymentForm.addEventListener('submit', function(e) {
            const txidInput = document.getElementById('id_txid');
            const txidValue = txidInput.value.trim();

            if (!txidValue) {
                e.preventDefault();
                showError(txidInput, 'Please enter a valid transaction ID');
            } else if (txidValue.length < 10) {
                e.preventDefault();
                showError(txidInput, 'Transaction ID seems too short');
            }
        });
    }

    // Helper function to show form validation errors
    function showError(inputElement, message) {
        // Create or update error message
        let errorElement = inputElement.nextElementSibling;
        if (!errorElement || !errorElement.classList.contains('text-red-600')) {
            errorElement = document.createElement('p');
            errorElement.className = 'mt-1 text-sm text-red-600';
            inputElement.parentNode.insertBefore(errorElement, inputElement.nextSibling);
        }

        // Set error message and show it
        errorElement.textContent = message;
        inputElement.classList.add('border-red-300');
        inputElement.classList.add('focus:border-red-500');
        inputElement.classList.add('focus:ring-red-500');

        // Focus the input
        inputElement.focus();
    }

    // VPS product image carousel
    const productCarousel = document.getElementById('productCarousel');
    if (productCarousel) {
        // Using a simple carousel implementation with Alpine.js
        if (!productCarousel.hasAttribute('x-data')) {
            productCarousel.setAttribute('x-data', '{ activeSlide: 0, slides: Array.from(document.querySelectorAll("#productCarousel .carousel-item")).length }');

            // Add navigation controls if they don't exist
            if (!productCarousel.querySelector('.carousel-controls')) {
                const controls = document.createElement('div');
                controls.className = 'carousel-controls absolute bottom-4 left-0 right-0 flex justify-center space-x-2';

                const slides = productCarousel.querySelectorAll('.carousel-item');
                for (let i = 0; i < slides.length; i++) {
                    const dot = document.createElement('button');
                    dot.className = 'h-2 w-2 rounded-full bg-white bg-opacity-50 focus:outline-none';
                    dot.setAttribute('@click', `activeSlide = ${i}`);
                    dot.setAttribute(':class', `{'bg-opacity-100': activeSlide === ${i}}`);
                    controls.appendChild(dot);
                }

                productCarousel.appendChild(controls);
            }

            // Auto-advance slides
            setInterval(() => {
                const data = Alpine.store('carousel') || { activeSlide: 0, slides: productCarousel.querySelectorAll('.carousel-item').length };
                data.activeSlide = (data.activeSlide + 1) % data.slides;
                Alpine.store('carousel', data);
            }, 5000);
        }
    }

    // Copy wallet address to clipboard
    const copyWalletBtn = document.getElementById('copyWalletBtn');
    if (copyWalletBtn) {
        copyWalletBtn.addEventListener('click', function() {
            const walletAddress = document.getElementById('walletAddress').textContent;

            // Use the clipboard API
            navigator.clipboard.writeText(walletAddress).then(function() {
                // Show success message
                const originalText = copyWalletBtn.textContent;
                const originalIcon = copyWalletBtn.querySelector('i').className;

                copyWalletBtn.querySelector('span').textContent = 'Copied!';
                copyWalletBtn.querySelector('i').className = 'fas fa-check';

                copyWalletBtn.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                copyWalletBtn.classList.add('bg-green-500', 'hover:bg-green-600', 'text-white');

                // Reset button after a delay
                setTimeout(function() {
                    copyWalletBtn.querySelector('span').textContent = originalText;
                    copyWalletBtn.querySelector('i').className = originalIcon;

                    copyWalletBtn.classList.remove('bg-green-500', 'hover:bg-green-600', 'text-white');
                    copyWalletBtn.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
            });
        });
    }

    // Handle confirmation dialogs
    const confirmActions = document.querySelectorAll('[data-confirm]');
    confirmActions.forEach(item => {
        item.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Make tables responsive on mobile
    function makeTablesResponsive() {
        const tables = document.querySelectorAll('table:not(.no-responsive)');

        if (window.innerWidth <= 768) {
            tables.forEach(table => {
                // Add responsive class if not already present
                if (!table.classList.contains('md:table')) {
                    table.classList.add('block', 'md:table');

                    // Add wrapper if needed
                    const parent = table.parentNode;
                    if (!parent.classList.contains('overflow-x-auto')) {
                        const wrapper = document.createElement('div');
                        wrapper.className = 'overflow-x-auto sm:rounded-lg';
                        parent.insertBefore(wrapper, table);
                        wrapper.appendChild(table);
                    }
                }

                // Add data-label attributes to cells based on header text
                const headerCells = table.querySelectorAll('thead th');
                const headerTexts = Array.from(headerCells).map(th => th.textContent.trim());

                const bodyRows = table.querySelectorAll('tbody tr');
                bodyRows.forEach(row => {
                    row.classList.add('block', 'md:table-row', 'border-b', 'border-gray-200');

                    const cells = row.querySelectorAll('td');
                    cells.forEach((cell, index) => {
                        cell.classList.add('block', 'md:table-cell', 'py-2', 'px-4');

                        if (index < headerTexts.length && !cell.hasAttribute('data-label')) {
                            cell.setAttribute('data-label', headerTexts[index]);

                            // Create a label element for mobile view
                            if (!cell.querySelector('.cell-label')) {
                                const labelSpan = document.createElement('span');
                                labelSpan.className = 'cell-label font-medium text-gray-500 inline-block w-1/3 md:hidden';
                                labelSpan.textContent = headerTexts[index] + ': ';

                                // Wrap the existing content
                                const contentSpan = document.createElement('span');
                                contentSpan.className = 'cell-content inline-block w-2/3 md:w-full';

                                // Move the cell's content to the content span
                                while (cell.firstChild) {
                                    contentSpan.appendChild(cell.firstChild);
                                }

                                cell.appendChild(labelSpan);
                                cell.appendChild(contentSpan);
                            }
                        }
                    });
                });
            });
        }
    }

    // Run on page load
    makeTablesResponsive();

    // Run on window resize
    window.addEventListener('resize', function() {
        makeTablesResponsive();
    });
});
