/**
 * Payment handling functionality for Batch VPS Marketplace
 */

document.addEventListener('DOMContentLoaded', function() {
    // Handle TXID validation
    const txidInput = document.getElementById('id_txid');
    const paymentForm = document.getElementById('paymentForm');
    
    if (txidInput && paymentForm) {
        // Basic TXID format validation
        txidInput.addEventListener('input', function() {
            validateTxid(this.value);
        });
        
        // Form submission validation
        paymentForm.addEventListener('submit', function(e) {
            if (!validateTxid(txidInput.value)) {
                e.preventDefault();
            }
        });
    }
    
    // Validate TXID format
    function validateTxid(txid) {
        // Remove any error messages
        clearError(txidInput);
        
        // Trimmed value
        const trimmedTxid = txid.trim();
        
        // Check if empty
        if (!trimmedTxid) {
            showError(txidInput, 'Transaction ID is required');
            return false;
        }
        
        // Check minimum length (typical TRC-20 txids are quite long)
        if (trimmedTxid.length < 10) {
            showError(txidInput, 'Transaction ID seems too short');
            return false;
        }
        
        // Basic format check (alphanumeric only)
        if (!/^[a-zA-Z0-9]+$/.test(trimmedTxid)) {
            showError(txidInput, 'Transaction ID should contain only letters and numbers');
            return false;
        }
        
        return true;
    }
    
    // Show error message
    function showError(element, message) {
        // Add Bootstrap is-invalid class
        element.classList.add('is-invalid');
        
        // Create or update error message
        let errorElement = element.nextElementSibling;
        if (!errorElement || !errorElement.classList.contains('invalid-feedback')) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            element.parentNode.insertBefore(errorElement, element.nextSibling);
        }
        
        errorElement.textContent = message;
    }
    
    // Clear error message
    function clearError(element) {
        element.classList.remove('is-invalid');
        
        const errorElement = element.nextElementSibling;
        if (errorElement && errorElement.classList.contains('invalid-feedback')) {
            errorElement.textContent = '';
        }
    }
    
    // Copy wallet address functionality
    const copyWalletBtn = document.getElementById('copyWalletBtn');
    const walletAddress = document.getElementById('walletAddress');
    
    if (copyWalletBtn && walletAddress) {
        copyWalletBtn.addEventListener('click', function() {
            // Get the text
            const textToCopy = walletAddress.textContent;
            
            // Create a temporary element
            const tempInput = document.createElement('input');
            tempInput.value = textToCopy;
            document.body.appendChild(tempInput);
            
            // Select and copy the text
            tempInput.select();
            document.execCommand('copy');
            
            // Remove the temporary element
            document.body.removeChild(tempInput);
            
            // Update the button text
            const originalText = this.textContent;
            this.textContent = 'Copied!';
            this.classList.add('btn-success');
            this.classList.remove('btn-secondary');
            
            // Reset after 2 seconds
            setTimeout(() => {
                this.textContent = originalText;
                this.classList.remove('btn-success');
                this.classList.add('btn-secondary');
            }, 2000);
        });
    }
    
    // Payment verification helpers
    const verifyPaymentForm = document.getElementById('verifyPaymentForm');
    const verifyCheckbox = document.getElementById('id_verify');
    const rejectCheckbox = document.getElementById('id_reject');
    
    if (verifyPaymentForm && verifyCheckbox && rejectCheckbox) {
        // Make checkboxes mutually exclusive
        verifyCheckbox.addEventListener('change', function() {
            if (this.checked) {
                rejectCheckbox.checked = false;
            }
        });
        
        rejectCheckbox.addEventListener('change', function() {
            if (this.checked) {
                verifyCheckbox.checked = false;
            }
        });
        
        // Form submission validation
        verifyPaymentForm.addEventListener('submit', function(e) {
            if (!verifyCheckbox.checked && !rejectCheckbox.checked) {
                e.preventDefault();
                alert('Please choose to either verify or reject the payment.');
            }
        });
    }
});
