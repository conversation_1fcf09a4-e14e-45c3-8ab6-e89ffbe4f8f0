/**
 * Chat functionality for Batch VPS Marketplace
 */

document.addEventListener('DOMContentLoaded', function() {
    const chatContainer = document.getElementById('chatContainer');
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.getElementById('id_message');
    const orderItemId = chatContainer ? chatContainer.dataset.itemId : null;

    // Don't proceed if we're not on a chat page
    if (!chatContainer || !messageForm || !orderItemId) {
        return;
    }

    // Store last seen message timestamp
    let lastSeen = '';

    // Load initial messages
    loadMessages();

    // Set up regular polling for new messages (every 5 seconds)
    const messagePolling = setInterval(loadMessages, 5000);

    // Clean up on page leave
    window.addEventListener('beforeunload', function() {
        clearInterval(messagePolling);
    });

    // Send message via AJAX
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const message = messageInput.value.trim();
        if (!message) {
            return;
        }

        // Get CSRF token from cookie
        const csrftoken = getCookie('csrftoken');

        // Create form data
        const formData = new FormData();
        formData.append('message', message);

        // Send the message
        fetch(`/chat/send/${orderItemId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrftoken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // Clear the input
                messageInput.value = '';

                // Load messages to see the new one
                loadMessages();
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
            alert('Failed to send message. Please try again.');
        });
    });

    // Load messages from server
    function loadMessages() {
        fetch(`/chat/get/${orderItemId}/?last_seen=${encodeURIComponent(lastSeen)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // If we have new messages
                if (data.messages && data.messages.length > 0) {
                    // Update lastSeen to the timestamp of the latest message
                    lastSeen = data.messages[data.messages.length - 1].timestamp;

                    // Display the messages
                    appendMessages(data.messages);

                    // Scroll to bottom if we got new messages
                    scrollToBottom();
                }
            }
        })
        .catch(error => {
            console.error('Error loading messages:', error);
        });
    }

    // Append messages to the chat container
    function appendMessages(messages) {
        // If this is the first load, clear the container
        if (!lastSeen) {
            chatContainer.innerHTML = '';
        }

        messages.forEach(msg => {
            // Check if message already exists
            if (document.querySelector(`.chat-message[data-id="${msg.id}"]`)) {
                return;
            }

            const messageElement = document.createElement('div');
            messageElement.className = `chat-message ${msg.is_sender ? 'outgoing' : 'incoming'}`;
            messageElement.dataset.id = msg.id;

            messageElement.innerHTML = `
                <div class="message-content">
                    ${escapeHtml(msg.text).replace(/\n/g, '<br>')}
                </div>
                <div class="message-footer">
                    <span class="chat-sender">
                        ${msg.is_sender ? 'buyer' : 'seller'}
                    </span>
                    <span class="chat-timestamp">
                        - ${msg.formatted_time}
                    </span>
                </div>
            `;

            chatContainer.appendChild(messageElement);
        });
    }

    // Scroll the chat container to the bottom
    function scrollToBottom() {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    // Helper function to get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to escape HTML
    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // Initial scroll to bottom
    scrollToBottom();
});
