@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
    h1 {
        @apply text-2xl font-bold text-gray-900 dark:text-white;
    }
    h2 {
        @apply text-xl font-bold text-gray-900 dark:text-white;
    }
    h3 {
        @apply text-lg font-bold text-gray-900 dark:text-white;
    }
    h4 {
        @apply text-base font-bold text-gray-900 dark:text-white;
    }
    h5 {
        @apply text-sm font-bold text-gray-900 dark:text-white;
    }
    h6 {
        @apply text-xs font-bold text-gray-900 dark:text-white;
    }
    a {
        @apply text-secondary hover:text-secondary-dark dark:text-secondary-light dark:hover:text-secondary transition-colors;
    }
}

/* Custom component styles */
@layer components {
    /* Button styles */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
    }
    .btn-primary {
        @apply bg-primary text-white hover:bg-primary-dark focus:ring-primary-dark dark:bg-primary-light dark:hover:bg-primary;
    }
    .btn-secondary {
        @apply bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary-dark dark:bg-secondary-light dark:hover:bg-secondary;
    }
    .btn-success {
        @apply bg-success text-white hover:bg-success-dark focus:ring-success-dark dark:bg-success-light dark:hover:bg-success;
    }
    .btn-danger {
        @apply bg-danger text-white hover:bg-danger-dark focus:ring-danger-dark dark:bg-danger-light dark:hover:bg-danger;
    }
    .btn-warning {
        @apply bg-warning text-white hover:bg-warning-dark focus:ring-warning-dark dark:bg-warning-light dark:hover:bg-warning;
    }
    .btn-outline {
        @apply bg-transparent border border-current;
    }
    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }
    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    /* Card styles */
    .card {
        @apply bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700;
    }
    .card-header {
        @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
    }
    .card-body {
        @apply p-6;
    }
    .card-footer {
        @apply px-6 py-4 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700;
    }

    /* Form styles */
    .form-control {
        @apply block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-secondary dark:focus:border-secondary-light focus:ring-secondary dark:focus:ring-secondary-light dark:bg-gray-800 dark:text-white sm:text-sm;
    }
    .form-label {
        @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
    }
    .form-error {
        @apply mt-1 text-sm text-red-600 dark:text-red-400;
    }
    .form-help {
        @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
    }

    /* Badge styles */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }
    .badge-primary {
        @apply bg-primary-light/10 text-primary-dark dark:bg-primary-dark/20 dark:text-primary-light;
    }
    .badge-secondary {
        @apply bg-secondary-light/10 text-secondary-dark dark:bg-secondary-dark/20 dark:text-secondary-light;
    }
    .badge-success {
        @apply bg-success-light/10 text-success-dark dark:bg-success-dark/20 dark:text-success-light;
    }
    .badge-danger {
        @apply bg-danger-light/10 text-danger-dark dark:bg-danger-dark/20 dark:text-danger-light;
    }
    .badge-warning {
        @apply bg-warning-light/10 text-warning-dark dark:bg-warning-dark/20 dark:text-warning-light;
    }

    /* Alert styles */
    .alert {
        @apply rounded-md p-4;
    }
    .alert-info {
        @apply bg-blue-50 dark:bg-blue-900/30;
    }
    .alert-success {
        @apply bg-green-50 dark:bg-green-900/30;
    }
    .alert-warning {
        @apply bg-yellow-50 dark:bg-yellow-900/30;
    }
    .alert-danger {
        @apply bg-red-50 dark:bg-red-900/30;
    }

    /* Table styles */
    .table {
        @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
    }
    .table-header {
        @apply bg-gray-50 dark:bg-gray-700;
    }
    .table-header th {
        @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
    }
    .table-body {
        @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700;
    }
    .table-body td {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400;
    }
    .table-row-hover {
        @apply hover:bg-gray-50 dark:hover:bg-gray-700;
    }
}

/* Custom utility styles */
@layer utilities {
    .shadow-hover {
        @apply transition-shadow duration-300;
    }
    .shadow-hover:hover {
        @apply shadow-lg;
    }
    
    .transform-hover {
        @apply transition-transform duration-300;
    }
    .transform-hover:hover {
        @apply -translate-y-1;
    }
}
