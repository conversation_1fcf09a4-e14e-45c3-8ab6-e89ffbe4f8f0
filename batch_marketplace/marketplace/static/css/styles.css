/* Primary Styles for Oleer Market - Modern Theme */

:root {
  --primary-color: #1a2a3a;
  --secondary-color: #2563eb;
  --accent-color: #f43f5e;
  --light-color: #f8fafc;
  --dark-color: #0f172a;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --border-radius: 0.5rem;
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.3s ease;

  /* Oleer Market Theme Colors */
  --xleet-sidebar-bg: #1e293b;
  --xleet-sidebar-color: #cbd5e1;
  --xleet-sidebar-width: 260px;
  --xleet-sidebar-collapsed-width: 70px;
  --xleet-content-bg: #f5f7fb;
  --xleet-card-bg: #ffffff;
  --xleet-header-bg: #ffffff;
  --xleet-header-border: #dee2e6;
  --xleet-sidebar-header: #94a3b8;
  --xleet-sidebar-link-hover: #3b82f6;
  --xleet-sidebar-link-active: #3b82f6;
  --xleet-sidebar-badge-bg: #f43f5e;
  --xleet-sidebar-badge-color: #ffffff;
  --xleet-footer-bg: #ffffff;
  --xleet-footer-color: #6c757d;
}

/* General Styles */
body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--gray-800);
  line-height: 1.6;
  background-color: var(--xleet-content-bg);
  overflow-x: hidden;
}

a {
  color: var(--secondary-color);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.btn {
  border-radius: var(--border-radius);
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn-primary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.btn-danger {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-danger:hover {
  background-color: #e11d48;
  border-color: #e11d48;
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.btn-outline-primary {
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-outline-primary:hover {
  background-color: var(--secondary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.text-accent {
  color: var(--accent-color) !important;
}

/* Sidebar Styles */
.sidebar {
  width: var(--xleet-sidebar-width);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background: var(--xleet-sidebar-bg);
  transition: var(--transition);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
  overflow-y: auto;
}

.sidebar-collapsed .sidebar {
  width: var(--xleet-sidebar-collapsed-width);
}

/* Mobile sidebar behavior */
@media (max-width: 992px) {
  .sidebar {
    left: -100%;
    width: 100%;
    max-width: 280px;
  }

  .sidebar.show {
    left: 0;
  }

  /* Overlay when sidebar is open on mobile */
  .sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 99;
  }

  .sidebar.show + .sidebar-overlay {
    display: block;
  }
}

.sidebar-header {
  padding: 1.75rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.2), transparent);
}

.sidebar-brand {
  font-weight: 700;
  font-size: 1.35rem;
  color: white;
  display: flex;
  align-items: center;
  text-decoration: none;
  letter-spacing: 0.5px;
}

.sidebar-brand i {
  margin-right: 0.75rem;
  font-size: 1.6rem;
  color: var(--xleet-sidebar-link-active);
}

.sidebar-collapsed .sidebar-brand span {
  display: none;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  color: var(--xleet-sidebar-color);
  cursor: pointer;
  padding: 0;
  font-size: 1.25rem;
}

.sidebar-content {
  padding: 1.5rem 0;
}

.sidebar-user {
  padding: 0 1.5rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 1rem;
}

.sidebar-user-info {
  display: flex;
  flex-direction: column;
}

.sidebar-user-name {
  color: white;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.sidebar-user-role {
  font-size: 0.8rem;
  padding: 0.15rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-block;
  margin-top: 0.25rem;
}

.role-buyer {
  background-color: #3b82f6;
  color: white;
}

.role-seller {
  background-color: #10b981;
  color: white;
}

.role-admin {
  background-color: #ef4444;
  color: white;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-header {
  color: var(--xleet-sidebar-header);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  padding: 1.5rem 1.5rem 0.5rem;
  letter-spacing: 0.05em;
  margin-top: 0.5rem;
  position: relative;
}

.sidebar-header:after {
  content: '';
  position: absolute;
  left: 1.5rem;
  bottom: 0;
  width: 2rem;
  height: 2px;
  background: var(--xleet-sidebar-link-active);
  opacity: 0.6;
}

.sidebar-item {
  position: relative;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.85rem 1.5rem;
  color: var(--xleet-sidebar-color);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
  border-radius: 0 0.5rem 0.5rem 0;
  margin-bottom: 0.25rem;
}

.sidebar-link i {
  width: 1.25rem;
  font-size: 1rem;
  margin-right: 0.75rem;
  text-align: center;
  transition: var(--transition);
}

.sidebar-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.08);
  padding-left: 1.75rem;
}

.sidebar-link.active {
  color: white;
  background: var(--xleet-sidebar-link-active);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.sidebar-link.active i {
  color: rgba(255, 255, 255, 0.9);
}

.sidebar-badge {
  position: absolute;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: var(--xleet-sidebar-badge-bg);
  color: var(--xleet-sidebar-badge-color);
  font-size: 0.7rem;
  font-weight: 600;
  padding: 0.15rem 0.5rem;
  border-radius: 0.25rem;
}

.sidebar-collapsed .sidebar-link span,
.sidebar-collapsed .sidebar-header,
.sidebar-collapsed .sidebar-badge {
  display: none;
}

.sidebar-collapsed .sidebar-link {
  padding: 0.75rem;
  justify-content: center;
}

.sidebar-collapsed .sidebar-link i {
  margin-right: 0;
  font-size: 1.25rem;
}

/* Main Content Styles */
.main-content {
  margin-left: 0;
  transition: var(--transition);
  width: 100%;
}

.with-sidebar {
  margin-left: var(--xleet-sidebar-width);
  width: calc(100% - var(--xleet-sidebar-width));
}

.sidebar-collapsed .with-sidebar {
  margin-left: var(--xleet-sidebar-collapsed-width);
  width: calc(100% - var(--xleet-sidebar-collapsed-width));
}

.content {
  padding: 1.5rem;
}

/* Mobile main content */
@media (max-width: 992px) {
  .with-sidebar {
    margin-left: 0;
    width: 100%;
  }

  .sidebar-collapsed .with-sidebar {
    margin-left: 0;
    width: 100%;
  }

  .content {
    padding: 1rem;
  }
}

/* Navbar Styles */
.navbar-bg {
  background-color: var(--xleet-header-bg);
  border-bottom: 1px solid var(--xleet-header-border);
  box-shadow: 0 0 2rem 0 rgba(33, 37, 41, 0.1);
  padding: 0.75rem 1.5rem;
}

.navbar-brand-wrapper {
  display: flex;
  align-items: center;
}

.navbar-brand {
  font-weight: 700;
  color: var(--gray-800) !important;
  font-size: 1.4rem;
  letter-spacing: -0.5px;
}

.navbar-align {
  margin-left: auto;
}

.nav-icon {
  color: var(--gray-600);
  font-size: 1.2rem;
  padding: 0.5rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.nav-icon:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 18px;
  height: 18px;
  background-color: var(--accent-color);
  border-radius: 50%;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(25%, -25%);
}

.dropdown-menu-header {
  padding: 0.75rem 1rem;
  font-weight: 600;
  border-bottom: 1px solid var(--gray-200);
}

.dropdown-menu-footer {
  padding: 0.75rem 1rem;
  border-top: 1px solid var(--gray-200);
  text-align: center;
}

/* Mark all as read link */
.dropdown-menu-footer a:hover {
  color: var(--secondary-color) !important;
  text-decoration: none;
}

.dropdown-menu-footer a i.fa-check-double {
  color: var(--success-color);
}

/* Notification Styles */
.notification-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--gray-100);
  transition: background-color 0.2s ease;
}

.notification-item:hover {
  background-color: var(--gray-50);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  position: relative;
}

.notification-icon {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(37, 99, 235, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.notification-icon i {
  font-size: 14px;
  color: var(--secondary-color);
}

.notification-text {
  flex-grow: 1;
  padding-right: 30px; /* Space for delete button */
  overflow: hidden;
}

.notification-link {
  text-decoration: none;
  color: inherit;
  display: block;
}

.notification-title {
  font-weight: 600;
  font-size: 14px;
  color: var(--gray-800);
  margin-bottom: 4px;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: var(--gray-600);
  margin-bottom: 4px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.notification-time {
  font-size: 12px;
  color: var(--gray-500);
}

.notification-delete {
  position: absolute;
  right: 0;
  top: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: var(--gray-200);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0.6;
  transition: all 0.2s ease;
  padding: 0;
}

.notification-delete i {
  font-size: 10px;
  color: var(--gray-700);
}

.notification-delete:hover {
  opacity: 1;
  background-color: var(--danger-color);
}

.notification-delete:hover i {
  color: white;
}

.notification-item:hover .notification-delete {
  opacity: 0.8;
}

/* Notification dropdown styling */
.notification-dropdown {
  border: none;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  width: 320px;
  max-height: 480px;
}

/* Dropdown menu header styling */
.dropdown-menu-header {
  padding: 14px 16px;
  font-weight: 600;
  font-size: 14px;
  color: var(--gray-800);
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
  display: flex;
  align-items: center;
}

.dropdown-menu-header i {
  color: var(--secondary-color);
}

/* Notification list container */
.notification-list {
  max-height: 360px;
  overflow-y: auto;
  scrollbar-width: thin;
}

.notification-list::-webkit-scrollbar {
  width: 6px;
}

.notification-list::-webkit-scrollbar-track {
  background: var(--gray-100);
}

.notification-list::-webkit-scrollbar-thumb {
  background-color: var(--gray-300);
  border-radius: 6px;
}

/* Empty notification styling */
.list-group-item .text-center {
  padding: 16px 0;
  color: var(--gray-500);
  font-size: 14px;
}

.list-group-item {
  border: none;
  padding: 0.75rem 1rem;
}

/* Footer Styles */
.footer {
  padding: 1rem 1.5rem;
  background: var(--xleet-footer-bg);
  border-top: 1px solid var(--xleet-header-border);
}

/* Card Styles */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 0 0.875rem 0 rgba(33, 37, 41, 0.05);
  background-color: var(--xleet-card-bg);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: var(--transition);
}

.card-title {
  color: var(--gray-800);
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
}

.card-header {
  background-color: transparent;
  font-weight: 600;
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h4, .card-header h5 {
  margin-bottom: 0;
  font-weight: 600;
  font-size: 1.1rem;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: transparent;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--gray-200);
}

/* Dashboard Cards */
.dashboard-card {
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 0.875rem 0 rgba(33, 37, 41, 0.05);
  background-color: var(--xleet-card-bg);
  display: flex;
  align-items: center;
}

.dashboard-card-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  color: white;
}

.dashboard-card-info {
  flex: 1;
}

.dashboard-card-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-600);
  margin-bottom: 0.25rem;
}

.dashboard-card-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 0;
}

.dashboard-card-bg-primary .dashboard-card-icon {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.dashboard-card-bg-success .dashboard-card-icon {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.dashboard-card-bg-warning .dashboard-card-icon {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.dashboard-card-bg-danger .dashboard-card-icon {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Form Styles */
.form-control {
  border-radius: var(--border-radius);
  padding: 0.625rem 0.875rem;
  border: 1px solid var(--gray-300);
  font-size: 0.95rem;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
  outline: none;
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.input-group-text {
  background-color: var(--gray-100);
  border-color: var(--gray-300);
  color: var(--gray-600);
}

.form-text {
  color: var(--gray-500);
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.form-select {
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-300);
  padding: 0.625rem 2.25rem 0.625rem 0.875rem;
  font-size: 0.95rem;
  transition: var(--transition);
}

.form-select:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.15);
  outline: none;
}

/* Status Badges */
.badge {
  font-weight: 600;
  padding: 0.35em 0.65em;
  border-radius: 0.375rem;
  font-size: 0.85em;
}

.badge-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.badge-paid {
  background-color: #dbeafe;
  color: #1e40af;
}

.badge-delivered {
  background-color: #d1fae5;
  color: #065f46;
}

.badge-rejected {
  background-color: #fee2e2;
  color: #b91c1c;
}

.badge-refunded {
  background-color: #fef9c3;
  color: #854d0e;
}

/* xLeet Tabs */
.xleet-tabs {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  margin-bottom: 1.5rem;
  overflow-x: auto;
}

.xleet-tab {
  padding: 0.75rem 1.25rem;
  font-weight: 600;
  color: var(--gray-600);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: var(--transition);
  white-space: nowrap;
}

.xleet-tab:hover {
  color: var(--secondary-color);
}

.xleet-tab.active {
  color: var(--secondary-color);
  border-bottom-color: var(--secondary-color);
}

/* xLeet Breadcrumbs */
.xleet-breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 1rem;
  list-style: none;
}

.xleet-breadcrumb-item {
  display: flex;
  align-items: center;
}

.xleet-breadcrumb-item + .xleet-breadcrumb-item {
  padding-left: 0.5rem;
}

.xleet-breadcrumb-item + .xleet-breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: var(--gray-500);
  content: "/";
}

.xleet-breadcrumb-item a {
  color: var(--gray-600);
  text-decoration: none;
}

.xleet-breadcrumb-item a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

.xleet-breadcrumb-item.active {
  color: var(--gray-800);
  font-weight: 600;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--dark-color), #1e40af);
  color: white;
  padding: 5rem 0;
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

.hero-title {
  font-weight: 800;
  font-size: 3rem;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  letter-spacing: -0.5px;
}

/* VPS Product Cards */
.product-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card .card-img-top {
  height: 220px;
  object-fit: cover;
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
}

.product-card .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-card .card-text {
  flex: 1;
  color: var(--gray-600);
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0.75rem 0;
}

.product-specs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.product-spec {
  background-color: var(--gray-100);
  color: var(--gray-700);
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Dashboard Styles */
.dashboard-stats {
  background-color: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--gray-200);
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--gray-200);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-lg);
}

.stat-card h3 {
  font-size: 2.25rem;
  margin: 0.75rem 0;
  color: var(--secondary-color);
  font-weight: 700;
}

.stat-card p {
  color: var(--gray-600);
  margin: 0;
  font-weight: 500;
}

.stat-card i {
  font-size: 2rem;
  color: var(--gray-400);
  margin-bottom: 0.5rem;
}

/* Table styles */
.table {
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem 1.25rem;
  vertical-align: middle;
  border-top: 1px solid #dee2e6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
  background-color: transparent;
  color: var(--gray-600);
  font-weight: 600;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-bordered {
  border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6;
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Enhanced table responsiveness for mobile */
@media (max-width: 768px) {
  .table-responsive-mobile {
    display: block;
    width: 100%;
  }

  .table-responsive-mobile thead {
    display: none;
  }

  .table-responsive-mobile tbody tr {
    display: block;
    margin-bottom: 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
  }

  .table-responsive-mobile tbody td {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--gray-200);
    text-align: right;
  }

  .table-responsive-mobile tbody td:last-child {
    border-bottom: none;
  }

  .table-responsive-mobile tbody td:before {
    content: attr(data-label);
    font-weight: 600;
    margin-right: auto;
    text-align: left;
  }

  .table-responsive-mobile tbody td .btn-group {
    margin-left: auto;
  }
}

/* xLeet Table Styles */
.xleet-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.xleet-table th {
  padding: 0.75rem 1.25rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--gray-600);
  border-bottom: 1px solid #dee2e6;
  background-color: #f8f9fa;
}

.xleet-table td {
  padding: 0.75rem 1.25rem;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

.xleet-table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.xleet-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.xleet-table-title {
  font-weight: 600;
  margin-bottom: 0;
}

.xleet-table-actions {
  display: flex;
  gap: 0.5rem;
}

.xleet-table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* Chat styles */
.chat-container {
  height: 450px;
  overflow-y: auto;
  background-color: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  padding: 1.25rem;
  margin-bottom: 1.25rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.chat-message {
  margin-bottom: 1.25rem;
  padding: 1rem 1.25rem;
  border-radius: 1.25rem;
  max-width: 80%;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chat-message-sender {
  color: var(--gray-600);
  font-size: 0.8rem;
  margin-bottom: 0.5rem;
  display: block;
  font-weight: 600;
}

.chat-message-text {
  line-height: 1.5;
}

.chat-message-time {
  font-size: 0.7rem;
  color: var(--gray-500);
  text-align: right;
  margin-top: 0.5rem;
}

.chat-message.outgoing {
  background-color: var(--secondary-color);
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 0.25rem;
}

.chat-message.incoming {
  background-color: white;
  border-bottom-left-radius: 0.25rem;
  color: var(--gray-800);
}

.chat-message.outgoing .chat-message-sender,
.chat-message.outgoing .chat-message-time {
  color: rgba(255, 255, 255, 0.9);
}

.chat-form {
  position: relative;
}

.chat-form .form-control {
  padding-right: 4rem;
  border-radius: 1.5rem;
  resize: none;
}

/* Payment section */
.payment-info {
  background-color: white;
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  box-shadow: var(--box-shadow);
  border: 1px solid var(--gray-200);
}

.payment-info h4 {
  color: var(--gray-800);
  font-weight: 700;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.payment-info h4 i {
  margin-right: 0.5rem;
  color: var(--secondary-color);
}

.wallet-address {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  padding: 1rem;
  background-color: var(--gray-100);
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  word-break: break-all;
  margin-bottom: 1rem;
  position: relative;
}

.copy-btn {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: var(--gray-200);
  border: none;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  color: var(--gray-700);
  cursor: pointer;
  transition: var(--transition);
}

.copy-btn:hover {
  background-color: var(--gray-300);
}

/* Status history */
.status-timeline {
  position: relative;
  padding-left: 2.5rem;
  margin-bottom: 2rem;
}

.status-timeline::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0.75rem;
  width: 2px;
  background-color: var(--gray-300);
}

.status-timeline-item {
  position: relative;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
}

.status-timeline-item:last-child {
  margin-bottom: 0;
}

.status-timeline-item::before {
  content: '';
  position: absolute;
  left: -2.5rem;
  top: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 50%;
  background-color: var(--secondary-color);
  border: 3px solid white;
  box-shadow: 0 0 0 2px var(--secondary-color);
  z-index: 1;
}

.status-timeline-item.pending::before {
  background-color: var(--warning-color);
  box-shadow: 0 0 0 2px var(--warning-color);
}

.status-timeline-item.delivered::before {
  background-color: var(--success-color);
  box-shadow: 0 0 0 2px var(--success-color);
}

.status-timeline-item.rejected::before {
  background-color: var(--danger-color);
  box-shadow: 0 0 0 2px var(--danger-color);
}

.status-date {
  color: var(--gray-500);
  font-size: 0.85rem;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.status-description {
  margin-bottom: 0;
  color: var(--gray-800);
  font-weight: 500;
}

/* Mobile Responsiveness */
@media (max-width: 992px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .navbar-nav .nav-link {
    margin: 0.25rem 0;
  }

  /* Dashboard stats on tablet */
  .dashboard-stats .col-md-3 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  /* Adjust card layouts */
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header .card-actions {
    margin-top: 1rem;
    align-self: flex-start;
  }

  /* Adjust form layouts */
  .form-row {
    flex-direction: column;
  }

  .form-row > div {
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-section {
    padding: 3rem 0;
  }

  .chat-message {
    max-width: 90%;
  }

  .card-title {
    font-size: 1.2rem;
  }

  .btn {
    padding: 0.5rem 1rem;
  }

  .table thead th {
    font-size: 0.75rem;
    padding: 0.75rem;
  }

  .table tbody td {
    padding: 0.75rem;
  }

  .status-timeline {
    padding-left: 2rem;
  }

  .status-timeline-item::before {
    left: -2rem;
  }

  /* Navbar adjustments */
  .navbar-brand {
    font-size: 1.2rem;
  }

  .navbar-nav {
    flex-direction: row;
    justify-content: center;
    margin-top: 0.5rem;
  }

  .navbar-nav .nav-item {
    margin: 0 0.5rem;
  }

  /* Adjust dropdown menus */
  .dropdown-menu {
    position: fixed;
    top: auto;
    left: 0;
    right: 0;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    margin-top: 0;
    border-radius: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }

  /* Adjust chat layout */
  .chat-section, .details-section {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Adjust buttons in groups */
  .btn-group {
    display: flex;
    flex-wrap: wrap;
  }

  .btn-group .btn {
    flex: 1 1 auto;
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 1.75rem;
  }

  .product-price {
    font-size: 1.25rem;
  }

  .chat-container {
    height: 350px;
  }

  .stat-card h3 {
    font-size: 1.75rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  /* Dashboard stats on mobile */
  .dashboard-stats .col-md-3 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  /* Adjust form controls */
  .form-control {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  /* Adjust buttons */
  .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .btn-group .btn {
    width: auto;
  }

  /* Adjust card spacing */
  .card {
    margin-bottom: 1rem;
  }

  /* Adjust navbar */
  .navbar-bg {
    padding: 0.5rem 1rem;
  }

  /* Adjust dropdown toggles */
  .dropdown-toggle::after {
    display: none;
  }

  /* Fix table overflow */
  .table-responsive {
    margin-bottom: 0;
  }
}

/* Back to top button */
#btn-back-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: none;
  z-index: 99;
  opacity: 0;
  transition: opacity 0.3s;
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

@media (max-width: 576px) {
  #btn-back-to-top {
    bottom: 15px;
    right: 15px;
    width: 35px;
    height: 35px;
    font-size: 0.8rem;
  }
}

/* Utility Classes */
.hover-opacity-100:hover {
  opacity: 1 !important;
  transition: var(--transition);
}

.hover-scale {
  transition: var(--transition);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.shadow-hover {
  transition: var(--transition);
}

.shadow-hover:hover {
  box-shadow: var(--box-shadow-lg) !important;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.bg-gradient-dark {
  background: linear-gradient(135deg, var(--dark-color), var(--gray-800));
}

.rounded-xl {
  border-radius: 1rem !important;
}

.border-dashed {
  border-style: dashed !important;
}
