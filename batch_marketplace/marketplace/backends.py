"""
Custom authentication backends for the marketplace app.
"""
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from django.db.models import Q

class EmailBackend(ModelBackend):
    """
    Custom authentication backend that allows users to log in with their email address.
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # First try to find a user by exact username match (most specific)
            try:
                user = User.objects.get(username__exact=username)
                if user.check_password(password):
                    return user
            except User.DoesNotExist:
                pass

            # Then try to find users by email
            users = User.objects.filter(email__iexact=username)

            # If we have users with this email, check their passwords
            for user in users:
                if user.check_password(password):
                    return user

        except Exception as e:
            # Log the error for debugging
            print(f"Authentication error: {str(e)}")
            return None

        # No matching user or wrong password
        return None
