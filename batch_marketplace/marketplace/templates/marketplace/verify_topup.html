{% extends 'marketplace/base.html' %}

{% block title %}Verify Top-up - Oleer Market{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div class="mb-4 md:mb-0">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-check-circle mr-2 text-green-500"></i>
                Verify Top-up Request
            </h2>
            <p class="text-lg text-gray-600 mt-1">Review and verify the top-up transaction</p>
        </div>
        <div>
            <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                <div class="bg-blue-600 text-white px-6 py-4">
                    <h5 class="font-semibold flex items-center">
                        <i class="fas fa-wallet mr-2"></i>Top-up Details
                    </h5>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h6 class="text-sm font-medium text-gray-500 mb-1">User</h6>
                            <p class="font-medium">{{ topup.user.username }}</p>
                        </div>
                        <div>
                            <h6 class="text-sm font-medium text-gray-500 mb-1">Amount</h6>
                            <p class="font-medium">${{ topup.amount }}</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h6 class="text-sm font-medium text-gray-500 mb-1">Date Requested</h6>
                            <p>{{ topup.created_at|date:"F j, Y, g:i a" }}</p>
                        </div>
                        <div>
                            <h6 class="text-sm font-medium text-gray-500 mb-1">Status</h6>
                            <p>
                                {% if topup.verified %}
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                                {% else %}
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending Verification</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h6 class="text-sm font-medium text-gray-500 mb-1">Transaction ID</h6>
                        <div class="flex">
                            <input type="text" class="flex-grow px-4 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                value="{{ topup.txid }}" id="txid" readonly>
                            <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-r-lg hover:bg-gray-50 transition duration-300"
                                type="button" onclick="copyTxid()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-6">
                        <a href="https://tronscan.org/#/transaction/{{ topup.txid }}" target="_blank"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300">
                            <i class="fas fa-external-link-alt mr-2"></i>View on TronScan
                        </a>
                    </div>

                    {% if not topup.verified %}
                        <form method="post" id="verifyTopupForm" novalidate>
                            {% csrf_token %}

                            <div class="mb-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="verify" id="{{ form.verify.id_for_label }}"
                                            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="{{ form.verify.id_for_label }}" class="font-medium text-gray-700">
                                            Verify this top-up and add ${{ topup.amount }} to {{ topup.user.username }}'s balance
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="flex items-start">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="reject" id="{{ form.reject.id_for_label }}"
                                            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="{{ form.reject.id_for_label }}" class="font-medium text-gray-700">
                                            Reject this top-up request
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                    Notes (Optional)
                                </label>
                                <textarea name="notes" id="{{ form.notes.id_for_label }}" rows="3"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Add any notes about this verification"></textarea>
                            </div>

                            {% if form.non_field_errors %}
                                <div class="mb-4 p-4 rounded-md bg-red-50 text-red-800">
                                    {% for error in form.non_field_errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-check mr-2"></i>Submit Verification
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gray-700 text-white px-6 py-4">
                    <h5 class="font-semibold flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>Verification Guidelines
                    </h5>
                </div>
                <div class="p-6">
                    <p class="mb-3">Before verifying a top-up request, please check:</p>

                    <ol class="list-decimal pl-5 space-y-2 text-gray-700 mb-6">
                        <li>The transaction exists on TronScan</li>
                        <li>The amount matches the requested top-up amount</li>
                        <li>The transaction was sent to the correct wallet address</li>
                        <li>The transaction has enough confirmations (10+)</li>
                        <li>The transaction hasn't been used for a previous top-up</li>
                    </ol>

                    <div class="p-4 bg-yellow-50 text-yellow-800 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="ml-3">
                                <p><span class="font-bold">Important:</span> Once verified, the amount will be added to the user's balance immediately.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy TXID functionality
        function copyTxid() {
            const txid = document.getElementById('txid');
            txid.select();
            document.execCommand('copy');

            // Show a notification that it was copied
            const button = document.querySelector('button[onclick="copyTxid()"]');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i>';

            setTimeout(() => {
                button.innerHTML = originalHTML;
            }, 2000);
        }

        // Make the copy button work
        const copyButton = document.querySelector('button[onclick="copyTxid()"]');
        if (copyButton) {
            copyButton.onclick = copyTxid;
        }

        // Form validation
        const verifyTopupForm = document.getElementById('verifyTopupForm');
        const verifyCheckbox = document.getElementById('id_verify');
        const rejectCheckbox = document.getElementById('id_reject');

        if (verifyTopupForm && verifyCheckbox && rejectCheckbox) {
            // Make checkboxes mutually exclusive
            verifyCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    rejectCheckbox.checked = false;
                }
            });

            rejectCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    verifyCheckbox.checked = false;
                }
            });

            // Form submission validation
            verifyTopupForm.addEventListener('submit', function(e) {
                if (!verifyCheckbox.checked && !rejectCheckbox.checked) {
                    e.preventDefault();
                    alert('Please choose to either verify or reject the top-up.');
                }
            });
        }
    });
</script>
{% endblock %}
