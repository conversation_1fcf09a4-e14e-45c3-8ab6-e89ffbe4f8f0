{% extends 'marketplace/base.html' %}

{% block title %}Top Up Balance - Oleer Market{% endblock %}

{% block extra_css %}
<!-- QRCode.js library for fallback QR code generation -->
<script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div class="mb-4 md:mb-0">
            <h2 class="text-2xl font-bold flex items-center">
                <i class="fas fa-wallet mr-2 text-blue-500"></i>
                Top Up Your Balance
                {% if settings.NOWPAYMENTS_TEST_MODE %}
                <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Test Mode</span>
                {% endif %}
            </h2>
            <p class="text-lg text-gray-600 mt-2">Add funds to your account to purchase products</p>
        </div>
        <div>
            <a href="{% url 'buyer_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                <div class="bg-blue-600 text-white px-6 py-4">
                    <h3 class="font-semibold flex items-center">
                        <i class="fas fa-plus-circle mr-2"></i>Add Funds
                    </h3>
                </div>
                <div class="p-6">
                    {% if messages %}
                        {% for message in messages %}
                            {% if 'Insufficient balance' in message|stringformat:"s" %}
                                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4 rounded">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-circle"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p>{{ message }}</p>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                    <div class="bg-gray-50 rounded-lg p-6 mb-6 text-center">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Your Current Balance</h4>
                        <p class="text-3xl font-bold text-blue-600">${{ request.user.profile.balance|floatformat:2 }}</p>
                    </div>

                    {% if active_topup %}
                        <!-- Active payment in progress -->
                        <div class="border border-blue-300 rounded-lg mb-6 overflow-hidden">
                            <div class="bg-blue-600 text-white px-6 py-4">
                                <h3 class="font-semibold flex items-center">
                                    <i class="fas fa-sync-alt mr-2"></i>Payment in Progress
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="bg-blue-50 text-blue-700 p-4 rounded-lg mb-6">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p>You have an active payment of <span class="font-bold">${{ active_topup.amount }}</span> in progress.
                                            Please complete the payment to add funds to your balance.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <h4 class="text-sm font-semibold text-gray-700 mb-2">Payment Amount:</h4>
                                        <p class="text-xl font-medium">{{ active_topup.nowpayments_pay_amount|default:active_topup.amount }} {{ active_topup.nowpayments_pay_currency|default:"USDT" }}</p>
                                    </div>
                                    <div>
                                        <h4 class="text-sm font-semibold text-gray-700 mb-2">Status:</h4>
                                        <p>
                                            {% if active_topup.payment_status == 'waiting' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Waiting for Payment</span>
                                            {% elif active_topup.payment_status == 'confirming' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Confirming</span>
                                            {% elif active_topup.payment_status == 'confirmed' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Confirmed</span>
                                            {% elif active_topup.payment_status == 'finished' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Finished</span>
                                            {% elif active_topup.payment_status == 'failed' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
                                            {% elif active_topup.payment_status == 'expired' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Expired</span>
                                            {% elif active_topup.payment_status == 'partially_paid' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Partially Paid</span>
                                            {% else %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">{{ active_topup.payment_status|title }}</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <h4 class="text-sm font-semibold text-gray-700 mb-2">Send USDT (TRC-20) to this address:</h4>
                                    <div class="flex">
                                        <input type="text" class="flex-grow px-4 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            value="{{ active_topup.nowpayments_pay_address|default:'' }}" id="paymentAddress" readonly>
                                        <button class="px-4 py-2 bg-white border border-blue-500 text-blue-500 rounded-r-lg hover:bg-blue-50 transition duration-300"
                                            type="button" onclick="copyPaymentAddress()">
                                            <i class="fas fa-copy mr-1"></i>Copy
                                        </button>
                                    </div>
                                    {% if not active_topup.nowpayments_pay_address %}
                                    <p class="text-red-600 text-sm mt-2">
                                        <i class="fas fa-exclamation-circle"></i> Payment address not available. Please refresh the page or try again.
                                    </p>
                                    {% endif %}
                                </div>

                                {% if active_topup.nowpayments_pay_address %}
                                <div class="text-center mb-6">
                                    <div id="qrCodeContainer" class="inline-block">
                                        <!-- Primary QR code from Google Charts API -->
                                        <img src="https://chart.googleapis.com/chart?cht=qr&chl={{ active_topup.nowpayments_pay_address }}&chs=200x200&choe=UTF-8&chld=L|2"
                                             alt="QR Code" class="border border-gray-200 p-2 max-w-[200px]" id="primaryQrCode"
                                             onerror="generateFallbackQR('{{ active_topup.nowpayments_pay_address }}')">
                                    </div>
                                    <p class="text-gray-500 text-sm mt-2">Scan with your wallet app</p>
                                    <button class="mt-2 px-3 py-1 text-xs bg-white border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition duration-300"
                                        onclick="regenerateQR('{{ active_topup.nowpayments_pay_address }}')">
                                        <i class="fas fa-sync-alt mr-1"></i> Regenerate QR Code
                                    </button>
                                </div>
                                {% else %}
                                <div class="text-center mb-6 bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded">
                                    <i class="fas fa-qrcode text-2xl text-yellow-500 mb-2"></i>
                                    <p class="text-yellow-700">QR code could not be generated. Please use the payment address above or refresh the page.</p>
                                    <button class="mt-2 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition duration-300"
                                        onclick="window.location.reload()">
                                        <i class="fas fa-sync-alt mr-1"></i> Refresh Page
                                    </button>
                                </div>
                                {% endif %}

                                {% if settings.NOWPAYMENTS_TEST_MODE %}
                                <div class="bg-blue-50 text-blue-700 p-4 rounded-lg mb-6">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p><span class="font-bold">Test Mode:</span> This is a test payment. No real funds will be transferred. The payment will be automatically marked as successful for testing purposes.</p>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="bg-yellow-50 text-yellow-700 p-4 rounded-lg mb-6">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-triangle"></i>
                                        </div>
                                        <div class="ml-3">
                                            <p><span class="font-bold">Important:</span> Only send USDT on the Tron (TRC-20) network. Sending any other cryptocurrency or using a different network may result in permanent loss of funds.</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <div class="space-y-3">
                                    <button type="button" class="w-full px-4 py-2 border border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition duration-300"
                                        onclick="window.location.reload()">
                                        <i class="fas fa-sync-alt mr-2"></i>Check Payment Status
                                    </button>


                                </div>
                            </div>
                        </div>
                    {% else %}
                        <!-- New payment form -->
                        <form method="post" novalidate id="topupForm">
                            {% csrf_token %}

                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Select Amount</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
                                    {% for value, label in form.amount_preset.field.choices %}
                                    <div>
                                        <div class="amount-option">
                                            <input class="sr-only" type="radio" name="amount_preset"
                                                id="amount_{{ value }}" value="{{ value }}"
                                                {% if forloop.first %}checked{% endif %}>
                                            <label class="block text-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:border-blue-500 hover:bg-gray-50 transition duration-300"
                                                for="amount_{{ value }}">
                                                <span class="block text-lg font-semibold">{{ label }}</span>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>

                                <div class="mb-4">
                                    <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">Custom Amount (USD)</label>
                                    <div class="flex">
                                        <span class="inline-flex items-center px-3 py-2 border border-r-0 border-gray-300 bg-gray-50 text-gray-500 rounded-l-md">
                                            $
                                        </span>
                                        {{ form.amount }}
                                    </div>
                                    {% if form.amount.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.amount.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                </div>
                            </div>

                            <div>
                                <button type="submit" class="w-full px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition duration-300">
                                    <i class="fas fa-wallet mr-2"></i>Create Payment
                                </button>
                            </div>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                <div class="bg-gray-700 text-white px-6 py-4">
                    <h3 class="font-semibold flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>Payment Information
                    </h3>
                </div>
                <div class="p-6">
                    <div class="bg-blue-50 text-blue-700 p-4 rounded-lg mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="ml-3">
                                <p><span class="font-bold">Automatic Payments:</span> Our system now supports automatic USDT TRC-20 payments.</p>
                            </div>
                        </div>
                    </div>

                    <ol class="list-decimal pl-5 space-y-2 text-gray-700">
                        <li>Select or enter the amount you want to add</li>
                        <li>Click "Create Payment" to generate a payment address</li>
                        <li>Send USDT TRC-20 to the provided address</li>
                        <li>Your balance will be updated automatically once the payment is confirmed</li>
                    </ol>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="bg-gray-100 px-6 py-4 border-b">
                    <h3 class="font-semibold flex items-center text-gray-700">
                        <i class="fas fa-history mr-2 text-blue-500"></i>Recent Top-ups
                    </h3>
                </div>
                <div class="p-6">
                    {% if recent_topups %}
                        <div class="divide-y divide-gray-200">
                            {% for topup in recent_topups %}
                                <div class="py-3 hover:bg-gray-50 transition duration-150">
                                    <div class="flex justify-between items-start">
                                        <h4 class="font-medium text-gray-900">${{ topup.amount }}</h4>
                                        <span class="text-sm text-gray-500">{{ topup.created_at|date:"M d, Y" }}</span>
                                    </div>
                                    <p class="text-sm mt-1 flex items-center">
                                        <span class="text-gray-600 mr-1">Status:</span>
                                        {% if topup.verified %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Verified</span>
                                        {% elif topup.payment_status == 'waiting' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Waiting</span>
                                        {% elif topup.payment_status == 'confirming' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Confirming</span>
                                        {% elif topup.payment_status == 'confirmed' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Confirmed</span>
                                        {% elif topup.payment_status == 'finished' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Finished</span>
                                        {% elif topup.payment_status == 'failed' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Failed</span>
                                        {% elif topup.payment_status == 'expired' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Expired</span>
                                        {% elif topup.payment_status == 'partially_paid' %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Partially Paid</span>
                                        {% else %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">{{ topup.payment_status|title }}</span>
                                        {% endif %}
                                    </p>
                                    {% if topup.txid %}
                                        <p class="text-xs text-gray-500 mt-1">TX: {{ topup.txid|truncatechars:15 }}</p>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-gray-500">No recent top-ups found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Copy payment address to clipboard
    function copyPaymentAddress() {
        const paymentAddress = document.getElementById('paymentAddress');
        paymentAddress.select();
        document.execCommand('copy');

        // Show a notification that it was copied
        const button = document.querySelector('button[onclick="copyPaymentAddress()"]');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';

        setTimeout(() => {
            button.innerHTML = originalText;
        }, 2000);
    }

    // Generate a fallback QR code using QRCode.js library
    function generateFallbackQR(address) {
        if (!address) {
            console.error('No payment address provided for QR code generation');
            return;
        }

        // Check if QRCode.js is loaded, if not, load it
        if (typeof QRCode === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js';
            script.onload = function() {
                createQRCode(address);
            };
            document.head.appendChild(script);
        } else {
            createQRCode(address);
        }
    }

    // Create QR code using QRCode.js
    function createQRCode(address) {
        const container = document.getElementById('qrCodeContainer');

        // Clear container
        container.innerHTML = '';

        // Create a new div for the QR code
        const qrDiv = document.createElement('div');
        qrDiv.id = 'qrcode';
        qrDiv.className = 'border border-gray-200 p-2 inline-block';
        container.appendChild(qrDiv);

        // Generate QR code
        new QRCode(qrDiv, {
            text: address,
            width: 200,
            height: 200,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });

        console.log('Fallback QR code generated for address:', address);
    }

    // Regenerate QR code (useful if the first one fails)
    function regenerateQR(address) {
        // Try Google Charts API first
        const primaryQrCode = document.getElementById('primaryQrCode');
        if (primaryQrCode) {
            // Force reload by adding a timestamp
            const timestamp = new Date().getTime();
            primaryQrCode.src = `https://chart.googleapis.com/chart?cht=qr&chl=${address}&chs=200x200&choe=UTF-8&chld=L|2&t=${timestamp}`;

            // Set up error handler
            primaryQrCode.onerror = function() {
                generateFallbackQR(address);
            };
        } else {
            // If primary QR code element doesn't exist, use fallback
            generateFallbackQR(address);
        }
    }

    // Handle amount preset selection
    document.addEventListener('DOMContentLoaded', function() {
        // Style for selected amount options
        const amountOptions = document.querySelectorAll('.amount-option input');
        const amountInput = document.getElementById('id_amount');

        // Add click event to all amount options
        amountOptions.forEach(option => {
            // Initial styling
            if (option.checked) {
                option.parentElement.querySelector('label').classList.add('border-blue-500', 'bg-blue-50');
                amountInput.value = option.value;
            }

            // Click event
            option.addEventListener('change', function() {
                // Reset all options
                amountOptions.forEach(opt => {
                    opt.parentElement.querySelector('label').classList.remove('border-blue-500', 'bg-blue-50');
                });

                // Style the selected option
                if (this.checked) {
                    this.parentElement.querySelector('label').classList.add('border-blue-500', 'bg-blue-50');
                    amountInput.value = this.value;
                }
            });
        });

        // Custom amount input
        amountInput.addEventListener('input', function() {
            // Uncheck all preset options when custom amount is entered
            amountOptions.forEach(opt => {
                opt.checked = false;
                opt.parentElement.querySelector('label').classList.remove('border-blue-500', 'bg-blue-50');
            });
        });
    });


</script>
{% endblock %}
