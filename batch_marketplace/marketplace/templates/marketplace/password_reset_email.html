{% autoescape off %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset - Oleer Market</title>
    <style>
        /* Base styles - Tailwind-inspired */
        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
            color: #1f2937;
            margin: 0;
            padding: 0;
            background-color: #f3f4f6;
        }

        /* Container */
        .container {
            max-width: 500px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        /* Card */
        .card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            overflow: hidden;
        }

        /* Header */
        .header {
            text-align: center;
            padding: 2rem 1.5rem 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
        }

        .header h2 {
            font-weight: 700;
            margin: 0 0 0.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .header p {
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-size: 0.875rem;
        }

        /* Content */
        .content {
            padding: 2rem;
        }

        .content p {
            margin-bottom: 1rem;
            color: #4b5563;
        }

        /* Button */
        .button {
            display: block;
            width: 100%;
            background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
            color: white !important;
            text-decoration: none;
            padding: 0.75rem 0;
            border-radius: 0.375rem;
            margin: 1.5rem 0;
            font-weight: 600;
            font-size: 1rem;
            text-align: center;
            border: none;
            transition: all 150ms ease-in-out;
            box-shadow: 0 4px 6px -1px rgba(37, 99, 235, 0.3);
        }

        .button:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            transform: translateY(-1px);
            box-shadow: 0 6px 8px -1px rgba(37, 99, 235, 0.4);
        }

        /* Link */
        .link {
            color: #2563eb;
            word-break: break-all;
            font-size: 0.875rem;
            background-color: #f3f4f6;
            padding: 0.75rem;
            border-radius: 0.375rem;
            display: block;
            border: 1px solid #e5e7eb;
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 1.25rem;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 0.75rem;
            background-color: #f9fafb;
        }

        .footer p {
            margin: 0.25rem 0;
        }

        /* Responsive */
        @media (max-width: 600px) {
            .container {
                padding: 20px 10px;
            }

            .content {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <div class="logo">
                    <span>Oleer Market</span>
                </div>
                <h2>Password Reset</h2>
                <p>Reset your account password</p>
            </div>
            <div class="content">
                <p>Hello {{ user.get_username }},</p>

                <p>You're receiving this email because you requested a password reset for your account.</p>

                <p>Please click the button below to reset your password:</p>

                <a href="{{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}" class="button">Reset Password</a>

                <p>If the button above doesn't work, copy and paste the following link into your browser:</p>

                <p class="link">
                    {{ protocol }}://{{ domain }}{% url 'password_reset_confirm' uidb64=uid token=token %}
                </p>

                <p>Your username, in case you've forgotten: <strong>{{ user.get_username }}</strong></p>

                <p>If you didn't request this password reset, you can safely ignore this email.</p>
            </div>
            <div class="footer">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>&copy; {% now "Y" %} Oleer Market. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
{% endautoescape %}
