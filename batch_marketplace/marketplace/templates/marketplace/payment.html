{% extends 'marketplace/base.html' %}
{% load static %}

{% block title %}Payment - Oleer Market{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <a href="{% url 'buyer_dashboard' %}" class="text-gray-700 hover:text-blue-600">Dashboard</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <span class="text-gray-500">Payment</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <!-- Payment Instructions -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-credit-card mr-2 text-blue-500"></i>
                        Payment Instructions
                    </h4>
                </div>
                <div class="p-6">
                    <div class="p-4 bg-blue-50 text-blue-800 rounded-lg mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <div class="ml-3">
                                <p>Please complete your payment using USDT TRC-20 cryptocurrency. Once payment is confirmed, your order will be processed.</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-8">
                        <!-- Step 1 -->
                        <div class="relative flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 text-white font-bold">1</div>
                                <div class="absolute left-5 top-10 h-full w-0.5 bg-gray-200 hidden md:block"></div>
                            </div>
                            <div class="ml-4 flex-grow pb-8">
                                <h5 class="text-lg font-medium text-gray-900 mb-2">Send Payment</h5>
                                <p class="text-gray-700 mb-3">Send the exact amount of <strong>${{ order.total_amount }} USDT</strong> to the seller's wallet address:</p>
                                <div class="bg-gray-100 p-4 rounded-lg mb-3 flex items-center justify-between">
                                    <code id="walletAddress" class="text-gray-800 break-all">{{ seller_wallet }}</code>
                                    <button id="copyWalletBtn" class="ml-2 px-3 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-sm flex items-center transition-colors">
                                        <i class="far fa-copy mr-1"></i>Copy
                                    </button>
                                </div>
                                <p class="text-red-600 flex items-start">
                                    <i class="fas fa-exclamation-triangle mt-1 mr-2"></i>
                                    <span>Important: Make sure you are sending USDT via the TRC-20 network only.</span>
                                </p>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="relative flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 text-white font-bold">2</div>
                                <div class="absolute left-5 top-10 h-full w-0.5 bg-gray-200 hidden md:block"></div>
                            </div>
                            <div class="ml-4 flex-grow pb-8">
                                <h5 class="text-lg font-medium text-gray-900 mb-2">Get Transaction ID</h5>
                                <p class="text-gray-700 mb-3">After sending the payment, copy the transaction ID (TXID) from your wallet or exchange.</p>
                                <div class="mb-3">
                                    <img src="https://pixabay.com/get/gf1c4c2f14dbef0c513e629166c011ea3142df3dba887ce84a92b629783741cba7070b258789b3abb3e552b8f805a532dc39ad59475b6e7fe43c28572ac41979e_1280.jpg"
                                        alt="TXID Example" class="rounded-lg max-h-48 mb-2">
                                    <span class="text-sm text-gray-500">Example: Find the TXID in your wallet's transaction history</span>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="relative flex">
                            <div class="flex-shrink-0">
                                <div class="flex items-center justify-center w-10 h-10 rounded-full bg-gray-800 text-white font-bold">3</div>
                            </div>
                            <div class="ml-4 flex-grow">
                                <h5 class="text-lg font-medium text-gray-900 mb-2">Submit Transaction ID</h5>
                                <p class="text-gray-700 mb-4">Enter the TXID in the form below and submit. An admin will verify your payment.</p>

                                <form method="post" id="paymentForm">
                                    {% csrf_token %}

                                    <div class="mb-4">
                                        <label for="{{ form.txid.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Transaction ID (TXID)
                                        </label>
                                        <div class="flex">
                                            <span class="inline-flex items-center px-3 py-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                                                <i class="fas fa-hashtag"></i>
                                            </span>
                                            <input type="text" name="txid" id="{{ form.txid.id_for_label }}"
                                                class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border border-gray-300 focus:ring-blue-500 focus:border-blue-500"
                                                placeholder="Enter transaction ID">
                                        </div>
                                        {% if form.txid.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.txid.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                        <p class="mt-1 text-sm text-gray-500">Enter the complete transaction ID from your USDT TRC-20 transfer</p>
                                    </div>

                                    <div>
                                        <button type="submit" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                            <i class="fas fa-paper-plane mr-2"></i>Submit Payment
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1 space-y-6">
            <!-- Order Summary -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h5 class="text-lg font-semibold text-gray-800">Order Summary</h5>
                </div>
                <div class="p-6">
                    <p class="mb-2"><span class="font-semibold">Order #:</span> {{ order.id }}</p>
                    <p class="mb-4"><span class="font-semibold">Date:</span> {{ order.created_at|date:"M d, Y" }}</p>

                    <div class="border-t border-b border-gray-200 py-4 my-4">
                        <h6 class="font-medium mb-3">Items:</h6>
                        <ul class="space-y-3">
                            {% for item in order.items.all %}
                                <li>
                                    <div class="text-gray-800">{{ item.product.title }}</div>
                                    <div class="flex justify-between mt-1">
                                        <span class="text-sm text-gray-500">Price</span>
                                        <span class="font-medium">${{ item.price }}</span>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>

                    <div class="flex justify-between items-center">
                        <span class="font-semibold">Total Amount:</span>
                        <span class="text-xl font-bold text-blue-600">${{ order.total_amount }}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Help -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h5 class="text-lg font-semibold text-gray-800">Need Help?</h5>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <h6 class="font-medium flex items-center text-gray-800">
                            <i class="fas fa-question-circle mr-2 text-blue-500"></i>How to get USDT?
                        </h6>
                        <p class="mt-1 text-sm text-gray-600">USDT can be purchased on cryptocurrency exchanges like Binance, Coinbase, or Kraken.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="font-medium flex items-center text-gray-800">
                            <i class="fas fa-exchange-alt mr-2 text-blue-500"></i>Make sure it's TRC-20
                        </h6>
                        <p class="mt-1 text-sm text-gray-600">When withdrawing USDT, select TRC-20 as the network to ensure compatibility.</p>
                    </div>

                    <div>
                        <h6 class="font-medium flex items-center text-gray-800">
                            <i class="fas fa-history mr-2 text-blue-500"></i>Verification time
                        </h6>
                        <p class="mt-1 text-sm text-gray-600">Payment verification usually takes 1-24 hours depending on admin availability.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/payment.js' %}"></script>
{% endblock %}
