{% extends 'marketplace/base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}Order Report - Oleer Market{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Head<PERSON> and Breadcrumb -->
    <div class="mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-flag mr-2 text-red-500"></i>Report Details
            </h1>
            <nav class="flex mt-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{% url 'index' %}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                            <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                            </svg>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <a href="{% url 'user_reports' %}" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">Reports</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Report #{{ report.id|default:order_item.id }}</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
        <!-- Report Header -->
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <div>
                <span class="px-2 py-1 text-xs font-semibold rounded bg-gray-200 text-gray-800 mr-2">{{ order_item.product.category|default:"accounts" }}</span>
                {% if report %}
                    <span class="text-red-600 font-medium">{{ report.issue|truncatechars:30 }}</span>
                    {% if report.resolved %}
                        {% if report.decision == 'refunded' %}
                            <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Refunded</span>
                        {% elif report.decision == 'rejected' %}
                            <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                        {% else %}
                            <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                        {% endif %}
                    {% else %}
                        <span class="ml-2 px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                    {% endif %}
                {% else %}
                    <span class="text-gray-500">Order details</span>
                {% endif %}
                <span class="ml-2 px-2 py-1 text-xs font-semibold rounded bg-gray-800 text-white">Order #{{ order_item.order.id }}-{{ order_item.id }}</span>
            </div>
            <div>
                {% if order_item.status == 'pending' %}
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                {% elif order_item.status == 'paid' %}
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Paid</span>
                {% elif order_item.status == 'delivered' %}
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Delivered</span>
                {% elif order_item.status == 'rejected' %}
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                {% elif order_item.status == 'refunded' %}
                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Refunded</span>
                {% endif %}
            </div>
        </div>

        <div class="p-6">
            <div class="flex flex-col md:flex-row md:space-x-6">
                <!-- Chat section (70%) -->
                <div class="w-full md:w-2/3 lg:w-3/4">
                {% if report %}
                <!-- Chat section (only shown if report exists) -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="p-6">
                        <!-- Chat Messages -->
                        <div class="space-y-4">
                            {% if chat_messages %}
                                {% for message in chat_messages %}
                                    <div class="rounded-lg border overflow-hidden shadow-sm
                                        {% if message.sender == order_item.order.buyer %}border-l-4 border-l-blue-500
                                        {% elif message.sender == order_item.product.seller %}border-l-4 border-l-green-500
                                        {% elif message.sender.profile.role == 'admin' %}border-l-4 border-l-red-500
                                        {% elif message.sender.profile.role == 'support' %}border-l-4 border-l-red-500
                                        {% else %}border-l-4 border-l-gray-400 italic{% endif %}">
                                        <div class="p-4">
                                            {{ message.message|linebreaksbr }}
                                        </div>
                                        <div class="bg-gray-100 px-4 py-2 flex items-center border-t">
                                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded mr-2
                                                {% if message.sender == order_item.order.buyer %}bg-blue-500 text-white
                                                {% elif message.sender == order_item.product.seller %}bg-green-500 text-white
                                                {% elif message.sender.profile.role == 'admin' %}bg-red-500 text-white
                                                {% elif message.sender.profile.role == 'support' %}bg-red-500 text-white
                                                {% else %}bg-gray-500 text-white{% endif %}">
                                                {% if message.sender == order_item.order.buyer %}
                                                    {{ message.sender.username }}
                                                {% elif message.sender == order_item.product.seller %}
                                                    {{ message.sender.username }}
                                                {% elif message.sender.profile.role == 'admin' %}
                                                    admin
                                                {% elif message.sender.profile.role == 'support' %}
                                                    {% if is_support or is_admin %}
                                                        {{ message.sender.username }}
                                                    {% else %}
                                                        support
                                                    {% endif %}
                                                {% else %}
                                                    system
                                                {% endif %}
                                            </span>
                                            <span class="text-xs text-gray-500">
                                                {{ message.created_at|date:"Y-m-d H:i:s" }}
                                            </span>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% else %}
                                <div class="rounded-lg border overflow-hidden shadow-sm border-l-4 border-l-gray-400 italic">
                                    <div class="p-4">
                                        No messages yet. Start the conversation!
                                    </div>
                                    <div class="bg-gray-100 px-4 py-2 flex items-center border-t">
                                        <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold rounded mr-2 bg-gray-500 text-white">
                                            system
                                        </span>
                                        <span class="text-xs text-gray-500">
                                            {{ order_item.order.created_at|date:"Y-m-d H:i:s" }}
                                        </span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Chat Form -->
                        {% if not report.resolved or is_support %}
                            <form method="post" class="mt-6 bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
                                {% csrf_token %}
                                <div class="mb-4 relative">
                                    <textarea id="{{ chat_form.message.id_for_label }}" name="{{ chat_form.message.name }}" rows="4"
                                        class="block w-full px-4 py-3 text-gray-700 border border-gray-300 rounded-lg focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200"
                                        placeholder="Type your message here"></textarea>
                                </div>
                                <button type="submit" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-emerald-300 shadow-sm transition-all duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Send Message
                                </button>
                            </form>
                        {% elif report.resolved %}
                            <div class="mt-6 p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100">
                                <div class="flex">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div>
                                        This report has been resolved. The chat is now closed for new messages.
                                        {% if report.decision == 'refunded' %}
                                            A refund has been issued to your account balance.
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Report Form (for buyers) -->
                {% if is_buyer and not report and order_item.status != 'pending' %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 border border-gray-100">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-amber-50 to-yellow-50">
                            <h3 class="text-lg font-semibold text-amber-800 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-amber-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                Report an Issue
                            </h3>
                        </div>
                        <div class="p-6">
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-4">
                                    <label for="issue_type" class="block text-sm font-medium text-gray-700 mb-1">Select Issue Type</label>
                                    <select id="issue_type" name="issue_type" required
                                        class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 transition-colors duration-200">
                                        <option value="" selected disabled>-- Select an issue --</option>
                                        <option value="Access Problem (Can't Login)">Access Problem (Can't Login)</option>
                                        <option value="Invalid Credentials">Invalid Credentials</option>
                                        <option value="Service Unavailable">Service Unavailable</option>
                                        <option value="Not As Described">Not As Described</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label for="first_message" class="block text-sm font-medium text-gray-700 mb-1">First Message</label>
                                    <textarea id="first_message" name="first_message" rows="4" required
                                        class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-amber-500 focus:border-amber-500 transition-colors duration-200"
                                        placeholder="Provide details about your issue..."></textarea>
                                </div>

                                <button type="submit" name="submit_report"
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-amber-500 to-yellow-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:from-amber-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-amber-300 shadow-sm transition-all duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    Submit Report
                                </button>
                            </form>
                        </div>
                    </div>
                {% endif %}

                <!-- Resolution Form (for admins and support) -->
                {% if report and not report.resolved and is_admin or report and not report.resolved and is_support %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6 border border-gray-100">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50">
                            <h3 class="text-lg font-semibold text-emerald-800 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Resolve Report
                            </h3>
                        </div>
                        <div class="p-6">
                            <form method="post">
                                {% csrf_token %}
                                <div class="mb-4">
                                    <label for="{{ resolution_form.decision.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Decision</label>
                                    <select id="{{ resolution_form.decision.id_for_label }}" name="{{ resolution_form.decision.name }}"
                                        class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200">
                                        <option value="rejected">Reject Report - No Refund</option>
                                        <option value="refunded">Accept Report - Issue Refund</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="{{ resolution_form.resolution_notes.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Resolution Notes</label>
                                    <textarea id="{{ resolution_form.resolution_notes.id_for_label }}" name="{{ resolution_form.resolution_notes.name }}" rows="4"
                                        class="block w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 transition-colors duration-200"
                                        placeholder="Enter resolution details..."></textarea>
                                </div>
                                <button type="submit" name="resolve_report"
                                    class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-emerald-300 shadow-sm transition-all duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    Resolve Report
                                </button>
                            </form>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- Details section (30%) -->
            <div class="w-full md:w-1/3 lg:w-1/4">
                <!-- Compact Order Details -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-4 border border-gray-100">
                    <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                            Order Info
                        </h3>
                    </div>
                    <div class="p-4">
                        <div class="text-sm">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-700">Status:</span>
                                <span>
                                    {% if order_item.status == 'pending' %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                    {% elif order_item.status == 'paid' %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Paid</span>
                                    {% elif order_item.status == 'delivered' %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Delivered</span>
                                    {% elif order_item.status == 'rejected' %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                                    {% elif order_item.status == 'refunded' %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Refunded</span>
                                    {% endif %}
                                </span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-700">Price:</span>
                                <span class="text-emerald-600 font-medium">${{ order_item.price }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-700">Buyer:</span>
                                <span class="text-gray-600">{{ order_item.order.buyer.username }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-700">Seller:</span>
                                <span class="text-gray-600">{{ order_item.product.seller.username }}</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium text-gray-700">Date:</span>
                                <span class="text-gray-600">{{ order_item.order.created_at|date:"Y-m-d" }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Compact Login Details -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-4 border border-gray-100">
                    <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                            <svg class="w-4 h-4 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                            </svg>
                            Login Details
                        </h3>
                    </div>
                    <div class="p-4">
                        <div class="text-sm space-y-3">
                            <!-- Common fields for both types -->
                            <div class="flex flex-col space-y-1">
                                <label class="text-xs font-medium text-gray-500">
                                    {% if order_item.product.item_type == 'account' %}Login URL{% else %}IP Address{% endif %}
                                </label>
                                <div class="flex">
                                    <input type="text" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700"
                                        value="{% if order_item.product.item_type == 'account' %}{{ order_item.product.url|default:'http://example.com' }}{% else %}{{ order_item.product.login_url|default:'http://example.com' }}{% endif %}" readonly>
                                    <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy"
                                        onclick="copyToClipboard('{% if order_item.product.item_type == 'account' %}{{ order_item.product.url|default:'http://example.com' }}{% else %}{{ order_item.product.login_url|default:'http://example.com' }}{% endif %}')">
                                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex flex-col space-y-1">
                                <label class="text-xs font-medium text-gray-500">Username</label>
                                <div class="flex">
                                    <input type="text" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700" value="{{ order_item.product.username|default:'admin' }}" readonly>
                                    <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy" onclick="copyToClipboard('{{ order_item.product.username|default:'admin' }}')">
                                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex flex-col space-y-1">
                                <label class="text-xs font-medium text-gray-500">Password</label>
                                <div class="flex">
                                    <input type="password" id="passwordField" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700" value="{{ order_item.product.password|default:'password123' }}" readonly>
                                    <button class="px-2 py-1 bg-gray-100 border border-gray-300 border-l-0 border-r-0 hover:bg-gray-200 transition-colors" type="button" id="togglePassword">
                                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                    <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy" onclick="copyToClipboard('{{ order_item.product.password|default:'password123' }}')">
                                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Type-specific fields -->
                            {% if order_item.product.item_type == 'vps' %}
                                <!-- VPS-specific fields -->
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs font-medium text-gray-500">Company</label>
                                    <div class="flex">
                                        {% with specs=order_item.product.specifications.split %}
                                            {% with company_found=False %}
                                                {% for spec in specs %}
                                                    {% if 'company:' in spec|lower %}
                                                        {% with company=spec|slice:"8:" %}
                                                            <input type="text" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700" value="{{ company|default:'Not specified' }}" readonly>
                                                            <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy" onclick="copyToClipboard('{{ company|default:'Not specified' }}')">
                                                                <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                                                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                                                </svg>
                                                            </button>
                                                            {% with company_found=True %}{% endwith %}
                                                        {% endwith %}
                                                    {% endif %}
                                                {% endfor %}

                                                {% if not company_found %}
                                                    <input type="text" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-md bg-gray-50 text-gray-700" value="Not specified" readonly>
                                                {% endif %}
                                            {% endwith %}
                                        {% endwith %}
                                    </div>
                                </div>

                                {% if order_item.product.ram %}
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs font-medium text-gray-500">RAM</label>
                                    <div class="flex">
                                        <input type="text" class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700" value="{{ order_item.product.ram }}" readonly>
                                        <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy" onclick="copyToClipboard('{{ order_item.product.ram }}')">
                                            <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                {% endif %}
                            {% elif order_item.product.item_type == 'account' %}
                                <!-- Account-specific fields -->
                                {% if order_item.product.proof %}
                                <div class="flex flex-col space-y-1">
                                    <label class="text-xs font-medium text-gray-500">Description</label>
                                    <div class="flex">
                                        <textarea class="flex-grow px-3 py-1.5 text-sm border border-gray-300 rounded-l-md bg-gray-50 text-gray-700 resize-none" rows="2" readonly>{{ order_item.product.proof }}</textarea>
                                        <button class="px-2 py-1 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors" type="button" title="Copy" onclick="copyToClipboard('{{ order_item.product.proof }}')">
                                            <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                {% endif %}
                            {% endif %}

                            {% if order_item.product.item_type == 'account' %}
                                <!-- Proof Link for Account Items -->
                                <!-- Check for proof link in the specifications -->
                                {% if 'Proof Link:' in order_item.product.specifications %}
                                    <!-- Extract and display proof link URL -->
                                    {% with proof_url=order_item.product.specifications|extract_url %}
                                        <div class="flex flex-col space-y-1">
                                            <div class="flex justify-between items-center">
                                                <label class="text-xs font-medium text-gray-500">Screenshot Proof</label>
                                                <button
                                                    type="button"
                                                    class="px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-lg hover:bg-indigo-100 hover:text-indigo-700 shadow-sm transition-all duration-300"
                                                    onclick="window.open('{{ proof_url }}', '_blank', 'width=800,height=600')">
                                                    <i class="fas fa-image"></i>
                                                </button>
                                            </div>
                                        </div>
                                    {% endwith %}
                                {% else %}
                                    <div class="flex flex-col space-y-1">
                                        <label class="text-xs font-medium text-gray-500">Screenshot Proof</label>
                                        <div class="flex">
                                            <span class="flex-grow px-3 py-1.5 text-xs border border-gray-300 rounded-md bg-gray-50 text-gray-500 flex items-center justify-center">
                                                <i class="fas fa-image mr-2"></i> -
                                            </span>
                                        </div>
                                    </div>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>



                <!-- Report Status (if report exists) -->
                {% if report %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-4 border border-gray-100">
                        <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                            <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-amber-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"></path>
                                </svg>
                                Report Status
                            </h3>
                        </div>
                        <div class="p-4">
                            <div class="text-sm">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-medium text-gray-700">Status:</span>
                                    <span>
                                        {% if report.resolved %}
                                            {% if report.decision == 'refunded' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Refunded</span>
                                            {% elif report.decision == 'rejected' %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>
                                            {% else %}
                                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                                        {% endif %}
                                    </span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-medium text-gray-700">Created:</span>
                                    <span class="text-gray-600">{{ report.created_at|date:"Y-m-d H:i" }}</span>
                                </div>
                                {% if report.resolved %}
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-medium text-gray-700">Resolved:</span>
                                    <span class="text-gray-600">{{ report.resolved_at|date:"Y-m-d H:i" }}</span>
                                </div>
                                <div class="flex justify-between items-center mb-2">
                                    <span class="font-medium text-gray-700">Resolved by:</span>
                                    <span class="text-gray-600">{{ report.resolved_by.username }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Seller Info (No Actions) -->
                {% if is_seller %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden mb-4 border border-gray-100">
                        <div class="px-4 py-3 border-b border-gray-200 bg-gray-50">
                            <h3 class="text-sm font-semibold text-gray-800 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Seller Info
                            </h3>
                        </div>
                        <div class="p-4">
                            <div class="text-sm text-gray-600">
                                <p class="mb-0">As a seller, you can only reply to messages in this report. Please use the chat section to communicate with the buyer.</p>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Proof image modal functionality
        window.openProofInModal = function(title, imageUrl) {
            console.log("Opening proof in modal:", imageUrl); // Debug log

            const modal = document.getElementById('proofImageModal');
            const modalTitle = document.getElementById('proofImageModalTitle');
            const modalIframe = document.getElementById('proofImageModalIframe');
            const modalImage = document.getElementById('proofImageModalImage');

            // Clean up the URL if needed
            imageUrl = imageUrl.trim();
            if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
                imageUrl = 'https://' + imageUrl;
            }

            modalTitle.textContent = title + ' - Proof Screenshot';

            // Determine if we should use iframe or img based on URL
            const isImageUrl = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(imageUrl);
            const isImageHostingService = /(imgur|prnt\.sc|prntscr|lightshot|gyazo|ibb\.co)/i.test(imageUrl);

            if (isImageUrl) {
                // Direct image URL - use img tag
                modalImage.src = imageUrl;
                modalImage.alt = title + ' Proof';
                modalImage.classList.remove('hidden');
                modalIframe.classList.add('hidden');

                // Show loading indicator
                modalImage.classList.add('opacity-50');

                // When image loads, remove loading indicator
                modalImage.onload = function() {
                    modalImage.classList.remove('opacity-50');
                };

                // If image fails to load
                modalImage.onerror = function() {
                    modalImage.src = 'https://via.placeholder.com/800x600?text=Image+Not+Found';
                    modalImage.classList.remove('opacity-50');
                };
            } else {
                // Not a direct image URL - use iframe
                modalIframe.src = imageUrl;
                modalIframe.classList.remove('hidden');
                modalImage.classList.add('hidden');
            }

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        };

        // Close proof image modal
        const closeProofImageModal = document.getElementById('closeProofImageModal');
        if (closeProofImageModal) {
            closeProofImageModal.addEventListener('click', function() {
                document.getElementById('proofImageModal').classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            });
        }

        // Close proof image modal when clicking outside
        const proofImageModalOverlay = document.getElementById('proofImageModalOverlay');
        if (proofImageModalOverlay) {
            proofImageModalOverlay.addEventListener('click', function(e) {
                if (e.target === this) {
                    document.getElementById('proofImageModal').classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            });
        }

        // Check if we're on a report page with AJAX chat
        const chatContainer = document.querySelector('.chat-section');
        if (chatContainer) {
            // Function to fetch chat messages
            function fetchChatMessages() {
                const itemId = window.location.pathname.split('/').pop();
                fetch(`/get-chat-messages/${itemId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Dispatch custom event with the response data
                        const event = new CustomEvent('ajaxChatResponse', { detail: data });
                        document.dispatchEvent(event);

                        // Process the report status
                        if (data.report_status && data.report_status.resolved && !data.report_status.can_message) {
                            // Disable the chat form if the report is resolved and user is not support
                            const chatForm = document.querySelector('.chat-form');
                            if (chatForm) {
                                chatForm.innerHTML = `
                                    <div class="mt-6 p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100">
                                        <div class="flex">
                                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                            </svg>
                                            <div>
                                                This report has been resolved. The chat is now closed for new messages.
                                                ${data.report_status.decision === 'refunded' ? 'A refund has been issued to your account balance.' : ''}
                                            </div>
                                        </div>
                                    </div>
                                `;
                            }
                        }
                    })
                    .catch(error => console.error('Error fetching chat messages:', error));
            }

            // Fetch messages on page load
            fetchChatMessages();

            // Add event listener for AJAX responses
            document.addEventListener('ajaxChatResponse', function(e) {
                const data = e.detail;
                if (data.report_status && data.report_status.resolved && !data.report_status.can_message) {
                    // Disable the chat form if the report is resolved and user is not support
                    const chatForm = document.querySelector('.chat-form');
                    if (chatForm) {
                        chatForm.innerHTML = `
                            <div class="mt-6 p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100">
                                <div class="flex">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div>
                                        This report has been resolved. The chat is now closed for new messages.
                                        ${data.report_status.decision === 'refunded' ? 'A refund has been issued to your account balance.' : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }
            });
        }

        // Toggle password visibility
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            const passwordInput = document.getElementById('passwordField');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Toggle the eye icon
                if (type === 'text') {
                    togglePassword.innerHTML = `
                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z" clip-rule="evenodd"></path>
                            <path d="M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"></path>
                        </svg>
                    `;
                } else {
                    togglePassword.innerHTML = `
                        <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                            <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                        </svg>
                    `;
                }
            });
        }

        // Form validation for report submission
        const issueTypeSelect = document.getElementById('issue_type');
        const firstMessageTextarea = document.getElementById('first_message');

        if (issueTypeSelect && firstMessageTextarea) {
            // Add client-side validation if needed
            console.log('Report form elements found and ready');
        }

        // Auto-select predefined messages for report resolution
        const decisionSelect = document.getElementById('id_decision');
        const resolutionNotes = document.getElementById('id_resolution_notes');

        if (decisionSelect && resolutionNotes) {
            // Predefined messages
            const rejectionMessage = "Your report has been reviewed and rejected. We have determined that the product meets our quality standards and functions as described. The credentials provided are valid and have been verified by our support team. No refund will be issued for this purchase.";
            const refundMessage = "Your report has been reviewed and accepted. We apologize for the inconvenience you experienced with this product. A full refund has been issued to your account balance. The refund should be reflected in your balance immediately.";

            // Set initial message based on current selection
            if (decisionSelect.value === 'rejected') {
                resolutionNotes.value = rejectionMessage;
            } else if (decisionSelect.value === 'refunded') {
                resolutionNotes.value = refundMessage;
            }

            // Update message when decision changes
            decisionSelect.addEventListener('change', function() {
                if (this.value === 'rejected') {
                    resolutionNotes.value = rejectionMessage;
                } else if (this.value === 'refunded') {
                    resolutionNotes.value = refundMessage;
                }
            });
        }
    });

    function copyToClipboard(text) {
        // Use the modern Clipboard API if available
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('Copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy: ', err);
                fallbackCopyToClipboard(text);
            });
        } else {
            fallbackCopyToClipboard(text);
        }
    }

    function fallbackCopyToClipboard(text) {
        // Create a temporary input element
        const input = document.createElement('input');
        input.setAttribute('value', text);
        input.style.position = 'absolute';
        input.style.left = '-9999px';
        document.body.appendChild(input);

        // Select the text
        input.select();
        input.setSelectionRange(0, 99999); // For mobile devices

        // Copy the text to clipboard
        try {
            document.execCommand('copy');
            showToast('Copied to clipboard!');
        } catch (err) {
            console.error('Failed to copy: ', err);
            alert('Failed to copy: ' + text);
        }

        // Remove the temporary element
        document.body.removeChild(input);
    }

    function showToast(message) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed bottom-4 right-4 z-50';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toastId = 'toast-' + Date.now();
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = 'flex items-center p-4 mb-4 text-gray-500 bg-white rounded-lg shadow-md border-l-4 border-emerald-500 transform transition-all duration-300 opacity-0 translate-y-2';
        toast.setAttribute('role', 'alert');

        toast.innerHTML = `
            <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-emerald-500 bg-emerald-100 rounded-lg">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                </svg>
            </div>
            <div class="ml-3 text-sm font-normal">${message}</div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8" onclick="dismissToast('${toastId}')">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Animate the toast in
        setTimeout(() => {
            toast.classList.remove('opacity-0', 'translate-y-2');
            toast.classList.add('opacity-100', 'translate-y-0');
        }, 10);

        // Auto-dismiss after 3 seconds
        setTimeout(() => {
            dismissToast(toastId);
        }, 3000);
    }

    function dismissToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.remove('opacity-100', 'translate-y-0');
            toast.classList.add('opacity-0', 'translate-y-2');

            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }
</script>
{% endblock %}

<!-- Proof Image Modal -->
<div id="proofImageModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div id="proofImageModalOverlay" class="fixed inset-0 bg-black bg-opacity-75 transition-opacity"></div>
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto z-10 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="proofImageModalTitle" class="text-lg font-semibold text-gray-900"></h3>
                <button id="closeProofImageModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6 flex justify-center">
                <div class="text-gray-700 max-w-full">
                    <div class="bg-gray-100 p-2 rounded-lg">
                        <!-- Image for direct image URLs -->
                        <img id="proofImageModalImage" class="max-w-full h-auto rounded shadow-lg" src="" alt="Proof Screenshot">

                        <!-- iFrame for non-image URLs (like prnt.sc, imgur, etc.) -->
                        <iframe id="proofImageModalIframe" class="w-full h-[600px] rounded shadow-lg hidden" src="" frameborder="0" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                <button onclick="document.getElementById('proofImageModal').classList.add('hidden'); document.body.classList.remove('overflow-hidden');"
                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
