{% extends 'marketplace/base.html' %}
{% load static %}

{% block title %}Ticket #{{ ticket.id }} - Batch VPS Marketplace{% endblock %}

{% block extra_css %}
<style>
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .animate-fade-in {
        animation: fadeIn 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-ticket-alt mr-3 text-emerald-500"></i>
                    Ticket Details
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                    Ticket #{{ ticket.id }}
                </p>
            </div>
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-emerald-600 hover:text-emerald-700">
                    Dashboard
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <a href="{% url 'ticket_list' %}" class="text-emerald-600 hover:text-emerald-700">Support Tickets</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="text-gray-500">Ticket #{{ ticket.id }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Main Ticket Card -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
        <!-- Ticket Header -->
        <div class="border-b border-gray-200 px-6 py-4 bg-gradient-to-r from-emerald-50 to-teal-50 flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
                <h4 class="text-lg font-semibold text-gray-800 flex flex-wrap items-center gap-2">
                    <span class="px-2.5 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">
                        {{ ticket.get_category_display }}
                    </span>
                    <span class="text-emerald-700">{{ ticket.subject|truncatechars:30 }}</span>
                    <span class="px-2.5 py-1 text-xs font-medium rounded-full
                        {% if ticket.status == 'open' %}bg-blue-100 text-blue-800
                        {% elif ticket.status == 'in_progress' %}bg-cyan-100 text-cyan-800
                        {% elif ticket.status == 'waiting' %}bg-yellow-100 text-yellow-800
                        {% elif ticket.status == 'resolved' %}bg-green-100 text-green-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ ticket.get_status_display }}
                    </span>
                </h4>
            </div>

        </div>

        <div class="p-6">
            <div class="flex flex-col lg:flex-row gap-6">
                <!-- Chat section (70%) -->
                <div class="w-full lg:w-2/3">
                    <!-- Chat section -->
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
                        <div class="p-6">
                            <!-- Chat Messages -->
                            {% for message in messages_list %}
                                <div class="p-4 rounded-lg border mb-4 shadow-sm animate-fade-in
                                    {% if message.is_internal_note %}border-l-4 border-l-amber-500
                                    {% elif message.user.profile.role == 'buyer' %}border-l-4 border-l-blue-500
                                    {% elif message.user.profile.role == 'seller' %}border-l-4 border-l-green-500
                                    {% elif message.user.profile.role == 'admin' %}border-l-4 border-l-red-500
                                    {% elif message.user.profile.role == 'support' %}border-l-4 border-l-red-500
                                    {% else %}border-l-4 border-l-gray-500{% endif %}">
                                    <div class="mb-3">
                                        {% if message.is_internal_note %}
                                            <div class="mb-2">
                                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800">Internal Note</span>
                                            </div>
                                        {% endif %}
                                        <div class="text-gray-700">
                                            {{ message.message|linebreaksbr }}
                                        </div>
                                    </div>
                                    <div class="flex items-center bg-gray-100 -mx-4 -mb-4 mt-2 px-4 py-2 border-t border-gray-200 text-sm">
                                        <span class="px-2 py-0.5 text-xs font-medium rounded text-white mr-2
                                            {% if message.is_internal_note %}bg-amber-500
                                            {% elif message.user.profile.role == 'buyer' %}bg-blue-500
                                            {% elif message.user.profile.role == 'seller' %}bg-green-500
                                            {% elif message.user.profile.role == 'admin' %}bg-red-600
                                            {% elif message.user.profile.role == 'support' %}bg-red-600
                                            {% else %}bg-gray-600{% endif %}">
                                            {% if message.user.profile.role == 'buyer' or message.user.profile.role == 'seller' %}
                                                {{ message.user.username }}
                                            {% elif message.user.profile.role == 'support' %}
                                                {% if is_support or is_admin %}
                                                    {{ message.user.username }}
                                                {% else %}
                                                    support
                                                {% endif %}
                                            {% elif message.user.profile.role == 'admin' %}
                                                admin
                                            {% else %}
                                                {{ message.user.username }}
                                            {% endif %}
                                        </span>
                                        <span class="text-xs text-gray-500">{{ message.created_at|date:"Y-m-d H:i:s" }}</span>
                                    </div>
                                </div>
                            {% empty %}
                                <div class="p-4 rounded-lg border border-l-4 border-l-gray-400 mb-4 shadow-sm animate-fade-in">
                                    <div class="mb-3 italic text-gray-600">
                                        No messages yet. Start the conversation!
                                    </div>
                                    <div class="flex items-center bg-gray-100 -mx-4 -mb-4 mt-2 px-4 py-2 border-t border-gray-200 text-sm">
                                        <span class="px-2 py-0.5 text-xs font-medium rounded bg-gray-600 text-white mr-2">
                                            system
                                        </span>
                                        <span class="text-xs text-gray-500">{{ ticket.created_at|date:"Y-m-d H:i:s" }}</span>
                                    </div>
                                </div>
                            {% endfor %}

                            <!-- Chat Form -->
                            {% if ticket.status != 'resolved' %}
                                <form method="post" class="mt-8 bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                                    {% csrf_token %}
                                    <div class="mb-4 relative">
                                        <label for="{{ form.message.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Type your message here</label>
                                        {{ form.message }}
                                    </div>

                                    {% if is_support or is_admin %}
                                        <div class="flex items-center mb-4">
                                            <input id="internalNote" name="is_internal_note" type="checkbox" class="w-4 h-4 text-emerald-600 border-gray-300 rounded focus:ring-emerald-500">
                                            <label for="internalNote" class="ml-2 text-sm text-gray-700">
                                                Mark as internal note (only visible to support staff)
                                            </label>
                                        </div>
                                    {% endif %}

                                    <button type="submit" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-emerald-500 to-teal-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-emerald-300 shadow-sm transition-all duration-200">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                        </svg>
                                        Send Message
                                    </button>
                                </form>
                            {% else %}
                                <div class="mt-6 p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100">
                                    <div class="flex">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            This ticket has been resolved. The chat is now closed for new messages.
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Details section (30%) -->
                <div class="w-full lg:w-1/3">
                    <!-- Ticket Information -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-6">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-500 to-teal-600">
                            <h3 class="text-lg font-semibold text-white flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Ticket Information
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-2 mb-4">
                                <p class="text-sm"><span class="font-medium text-gray-700">Ticket ID:</span> <span class="text-gray-600">#{{ ticket.id }}</span></p>
                                <p class="text-sm"><span class="font-medium text-gray-700">Created:</span> <span class="text-gray-600">{{ ticket.created_at|date:"M d, Y, h:i A" }}</span></p>
                                <p class="text-sm"><span class="font-medium text-gray-700">Last Updated:</span> <span class="text-gray-600">{{ ticket.updated_at|date:"M d, Y, h:i A" }}</span></p>
                                <p class="text-sm flex items-center">
                                    <span class="font-medium text-gray-700 mr-2">Status:</span>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {% if ticket.status == 'open' %}bg-blue-100 text-blue-800
                                        {% elif ticket.status == 'in_progress' %}bg-cyan-100 text-cyan-800
                                        {% elif ticket.status == 'waiting' %}bg-yellow-100 text-yellow-800
                                        {% elif ticket.status == 'resolved' %}bg-green-100 text-green-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ ticket.get_status_display }}
                                    </span>
                                </p>

                            </div>

                            {% if ticket.assigned_to %}
                                <div class="p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100 mb-4">
                                    <div class="flex">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                                        </svg>
                                        <div>
                                            This ticket is assigned to <span class="font-semibold">{{ ticket.assigned_to.username }}</span>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}

                            {% if is_support or is_admin %}
                                <div class="flex flex-wrap gap-2 mb-4">

                                    {% if ticket.status != 'resolved' %}
                                        <form method="post">
                                            {% csrf_token %}
                                            <button type="submit" name="resolve" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 border border-green-200 rounded-lg hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-green-300 transition-colors">
                                                <svg class="w-3.5 h-3.5 mr-1.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                                </svg>
                                                Mark as Resolved
                                            </button>
                                        </form>
                                    {% endif %}
                                </div>
                            {% endif %}

                            {% if ticket.status == 'resolved' and is_support or is_admin %}
                                <form method="post" class="mb-4">
                                    {% csrf_token %}
                                    <button type="submit" name="reopen" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-yellow-700 bg-yellow-100 border border-yellow-200 rounded-lg hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-300 transition-colors">
                                        <svg class="w-3.5 h-3.5 mr-1.5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                        </svg>
                                        Reopen Ticket
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>

                    <!-- User Information -->
                    <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-6">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-500 to-teal-600">
                            <h3 class="text-lg font-semibold text-white flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                User Information
                            </h3>
                        </div>
                        <div class="p-6">
                            <div class="space-y-2 mb-4">
                                <p class="text-sm"><span class="font-medium text-gray-700">Username:</span> <span class="text-gray-600">{{ ticket.user.username }}</span></p>
                                <p class="text-sm"><span class="font-medium text-gray-700">Email:</span> <span class="text-gray-600">{{ ticket.user.email }}</span></p>
                                <p class="text-sm"><span class="font-medium text-gray-700">Role:</span> <span class="text-gray-600">{{ ticket.user.profile.get_role_display }}</span></p>
                                <p class="text-sm"><span class="font-medium text-gray-700">Member Since:</span> <span class="text-gray-600">{{ ticket.user.date_joined|date:"M d, Y" }}</span></p>
                            </div>

                            <h4 class="text-sm font-semibold text-gray-700 mb-2 mt-4">User's Tickets</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">{{ ticket.user.tickets.count }} Total</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">{{ ticket.user.tickets.filter.count }} Resolved</span>
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">{{ ticket.user.tickets.filter.count }} Open</span>
                            </div>
                        </div>
                    </div>

                    {% if is_support or is_admin %}
                        <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-6">
                            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-500 to-teal-600">
                                <h3 class="text-lg font-semibold text-white flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                    Support Notes
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="p-4 bg-blue-50 text-blue-800 rounded-lg border border-blue-100">
                                    <div class="flex">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <div>
                                            Use internal notes to communicate with other support staff without the customer seeing the messages.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add copy functionality for ticket information
        const copyButtons = document.querySelectorAll('.copy-btn');
        copyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-copy');
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Show toast notification
                    showToast('Copied to clipboard!', 'success');
                }).catch(err => {
                    console.error('Failed to copy text: ', err);
                    showToast('Failed to copy text', 'error');
                });
            });
        });

        // Function to show toast notifications
        function showToast(message, type = 'info') {
            // Create toast container if it doesn't exist
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'fixed bottom-4 right-4 z-50';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toast = document.createElement('div');
            toast.id = toastId;

            // Set toast style based on type
            let bgColor, textColor, iconPath;

            if (type === 'success') {
                bgColor = 'bg-emerald-100 border-emerald-500';
                textColor = 'text-emerald-800';
                iconPath = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>';
            } else if (type === 'error') {
                bgColor = 'bg-red-100 border-red-500';
                textColor = 'text-red-800';
                iconPath = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>';
            } else {
                bgColor = 'bg-blue-100 border-blue-500';
                textColor = 'text-blue-800';
                iconPath = '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>';
            }

            toast.className = `flex items-center p-4 mb-4 ${textColor} ${bgColor} rounded-lg shadow-md border-l-4 transform transition-all duration-300 opacity-0 translate-y-2`;
            toast.setAttribute('role', 'alert');

            toast.innerHTML = `
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 ${textColor} rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        ${iconPath}
                    </svg>
                </div>
                <div class="ml-3 text-sm font-normal">${message}</div>
                <button type="button" class="ml-auto -mx-1.5 -my-1.5 ${textColor} hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8" onclick="dismissToast('${toastId}')">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            `;

            toastContainer.appendChild(toast);

            // Animate the toast in
            setTimeout(() => {
                toast.classList.remove('opacity-0', 'translate-y-2');
                toast.classList.add('opacity-100', 'translate-y-0');
            }, 10);

            // Auto-dismiss after 3 seconds
            setTimeout(() => {
                dismissToast(toastId);
            }, 3000);
        }

        function dismissToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.remove('opacity-100', 'translate-y-0');
                toast.classList.add('opacity-0', 'translate-y-2');

                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // Make dismissToast function globally available
        window.dismissToast = dismissToast;

        // Focus the message input field if it exists
        const messageInput = document.querySelector('textarea[name="message"]');
        if (messageInput) {
            messageInput.focus();
        }
    });
</script>

<!-- Add Heroicons (replacement for Bootstrap Icons) -->
<link rel="stylesheet" href="https://unpkg.com/heroicons@1.0.6/outline.css">
{% endblock %}
