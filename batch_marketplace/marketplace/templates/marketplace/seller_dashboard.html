{% extends 'marketplace/base.html' %}

{% block title %}My Items - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-store mr-3 text-emerald-500"></i>
                My Items
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Manage your listed items here
            </p>
        </div>
        <div>
            <a href="{% url 'add_vps_product' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 focus:ring-4 focus:ring-emerald-200 shadow-md transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Add New Item
            </a>
        </div>
    </div>
</div>

<!-- Manage Items -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-list mr-2 text-emerald-500"></i> Item List
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800">{{ products|length }} items</span>
                <span class="ml-2 text-sm text-gray-500">({{ unsold_count }} unsold, {{ sold_count }} sold)</span>
            </h3>
            <div class="flex items-center space-x-4">
                <div class="flex rounded-md shadow-sm">
                    <a href="{% url 'seller_dashboard' %}?filter=unsold" class="px-3 py-2 text-xs font-medium rounded-l-lg {% if current_filter == 'unsold' %}bg-gradient-to-r from-emerald-500 to-teal-600 text-white{% else %}bg-white text-gray-700 border border-gray-300 hover:bg-emerald-50{% endif %}">
                        Unsold ({{ unsold_count }})
                    </a>
                    <a href="{% url 'seller_dashboard' %}?filter=sold" class="px-3 py-2 text-xs font-medium {% if current_filter == 'sold' %}bg-gradient-to-r from-emerald-500 to-teal-600 text-white{% else %}bg-white text-gray-700 border-t border-b border-gray-300 hover:bg-emerald-50{% endif %}">
                        Sold ({{ sold_count }})
                    </a>
                    <a href="{% url 'seller_dashboard' %}?filter=all" class="px-3 py-2 text-xs font-medium rounded-r-lg {% if current_filter == 'all' %}bg-gradient-to-r from-emerald-500 to-teal-600 text-white{% else %}bg-white text-gray-700 border border-gray-300 hover:bg-emerald-50{% endif %}">
                        All
                    </a>
                </div>
                <div class="flex items-center">
                    <label for="itemStatusFilter" class="mr-2 text-sm text-gray-700">Filter view:</label>
                    <select id="itemStatusFilter" class="rounded-md border-gray-300 shadow-sm focus:border-emerald-300 focus:ring focus:ring-emerald-200 focus:ring-opacity-50 text-sm">
                        <option value="all">All visible items</option>
                        <option value="reported">Only reported</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    {% if products %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-emerald-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Item Details</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Buyer</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Report Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-emerald-700 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for product in products %}
                        <tr class="hover:bg-gray-50 product-item"
                            data-status="{% if product.is_sold %}sold{% else %}unsold{% endif %}"
                            data-reported="{% if product.report_exists %}reported{% else %}not-reported{% endif %}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <h6 class="text-sm font-medium text-gray-900">{{ product.title }}</h6>
                                    <p class="text-xs text-gray-500">IP: {{ product.login_url }}</p>
                                    <p class="text-xs text-gray-500">Username: {{ product.username }}</p>
                                    <p class="text-xs text-gray-500">Password: {{ product.password }}</p>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${{ product.price }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.created_at|date:"M d, Y" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                {% if product.is_sold %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Sold</span>
                                {% elif product.is_active %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Available</span>
                                {% else %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Inactive</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {% if product.buyer %}
                                    {{ product.buyer.username }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                {% if product.report_exists %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Reported</span>
                                    {% if product.report_resolved %}
                                        {% if product.report_decision == 'refunded' %}
                                            <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full bg-cyan-100 text-cyan-800">Refunded</span>
                                        {% elif product.report_decision == 'rejected' %}
                                            <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Rejected</span>
                                        {% endif %}
                                    {% else %}
                                        <span class="ml-1 px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">Pending</span>
                                    {% endif %}
                                {% elif product.is_sold %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">No Issues</span>
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                {% if product.report_exists %}
                                    <a href="{% url 'order_report' product.report_order_item_id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors">
                                        <i class="fas fa-exclamation-circle mr-1"></i> View Report
                                    </a>
                                {% elif not product.is_sold %}
                                    <div class="flex space-x-2">
                                        <button onclick="showProductDetails({{ product.id }})" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors">
                                            <i class="fas fa-eye mr-1"></i> Detail
                                        </button>
                                        <button type="button" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors"
                                                onclick="showDeleteModal('{{ product.id }}', '{{ product.title }}')">
                                            <i class="fas fa-trash mr-1"></i> Delete
                                        </button>
                                    </div>
                                {% else %}
                                    <span class="text-gray-500">No action needed</span>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="mb-4 text-gray-400">
                <i class="fas fa-box-open text-5xl"></i>
            </div>
            <h5 class="text-xl font-semibold text-gray-900 mb-2">You haven't listed any items yet</h5>
            <p class="text-gray-500 mb-6">Start by adding your first item</p>
            <a href="{% url 'add_vps_product' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-md transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Add New Item
            </a>
        </div>
    {% endif %}
</div>
</div>

<!-- Delete Item Modal -->
<div id="deleteModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeDeleteModal()"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            Confirm Deletion
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-red-600">
                                Are you sure you want to delete this item? This action cannot be undone.
                            </p>
                            <p class="text-sm text-gray-500 mt-2">
                                Item: <span id="deleteItemTitle" class="font-medium"></span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteItemForm" method="post" action="{% url 'delete_item' 0 %}">
                    {% csrf_token %}
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete Item
                    </button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Product Details Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="productDetailsModal">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center" id="productDetailsModalLabel">
                            <i class="fas fa-info-circle mr-2 text-emerald-500"></i> Product Details
                        </h3>
                        <div class="mt-4" id="productDetailsContent">
                            <div class="flex flex-col items-center justify-center py-6">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-emerald-500"></div>
                                <p class="mt-2 text-gray-500">Loading product details...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm" id="closeProductModalBtn">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Item status filtering
        const itemStatusFilter = document.getElementById('itemStatusFilter');
        if (itemStatusFilter) {
            itemStatusFilter.addEventListener('change', function() {
                const selectedStatus = this.value;
                const allItems = document.querySelectorAll('.product-item');

                allItems.forEach(item => {
                    if (selectedStatus === 'all') {
                        item.style.display = '';
                    } else if (selectedStatus === 'reported' && item.dataset.reported === 'reported') {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }

        // Product details modal functionality
        const productModal = document.getElementById('productDetailsModal');
        const closeProductBtn = document.getElementById('closeProductModalBtn');

        closeProductBtn.addEventListener('click', function() {
            productModal.classList.add('hidden');
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === productModal) {
                productModal.classList.add('hidden');
            }
        });
    });

    // Delete modal functions
    function showDeleteModal(itemId, itemTitle) {
        const modal = document.getElementById('deleteModal');
        const titleSpan = document.getElementById('deleteItemTitle');
        const deleteForm = document.getElementById('deleteItemForm');

        titleSpan.textContent = itemTitle;
        deleteForm.action = deleteForm.action.replace('/0/', '/' + itemId + '/');

        modal.classList.remove('hidden');
    }

    function closeDeleteModal() {
        const modal = document.getElementById('deleteModal');
        modal.classList.add('hidden');
    }

    // Product details modal functionality
    function showProductDetails(productId) {
        // Get the modal
        const modal = document.getElementById('productDetailsModal');

        // Show the modal
        modal.classList.remove('hidden');

        // Set the content to loading state
        const content = document.getElementById('productDetailsContent');
        content.innerHTML = `
            <div class="flex flex-col items-center justify-center py-6">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-emerald-500"></div>
                <p class="mt-2 text-gray-500">Loading product details...</p>
            </div>
        `;

        // Fetch product details from the server
        fetch(`/product/${productId}/details/`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Build the HTML for product details
                let detailsHtml = `
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 mb-6 overflow-hidden">
                        <div class="px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
                            <h3 class="text-base font-semibold">Product Details</h3>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <tbody class="divide-y divide-gray-200 bg-white">
                                    <!-- Basic Information -->
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 w-1/4">Product ID</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">#${data.id}</td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Title</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.title}</td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Price</th>
                                        <td class="px-4 py-2 text-sm text-gray-900 font-medium text-emerald-600">$${data.price.toFixed(2)}</td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Created</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.created_at}</td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Status</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">
                                            ${data.is_sold ?
                                                '<span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Sold</span>' :
                                                (data.is_active ?
                                                    '<span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Available</span>' :
                                                    '<span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">Inactive</span>'
                                                )
                                            }
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Type</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">
                                            ${data.item_type === 'vps' ?
                                                '<span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">VPS/RDP</span>' :
                                                '<span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">Account</span>'
                                            }
                                        </td>
                                    </tr>

                                    <!-- Access Details - with copy buttons -->
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                                            ${data.item_type === 'account' ? 'Login URL' : 'IP Address'}
                                        </th>
                                        <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                            <span>${data.item_type === 'account' ? data.url : data.login_url}</span>
                                            <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                    data-value="${data.item_type === 'account' ? data.url : data.login_url}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Username</th>
                                        <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                            <span>${data.username}</span>
                                            <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                    data-value="${data.username}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Password</th>
                                        <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                            <span>${data.password}</span>
                                            <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                    data-value="${data.password}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </td>
                                    </tr>

                                    <!-- Type-specific fields - without copy buttons -->
                                    ${data.item_type === 'vps' ? `
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Company</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.company}</td>
                                    </tr>
                                    ${data.ram && data.ram !== 'Not available' ? `
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">RAM</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.ram}</td>
                                    </tr>
                                    ` : ''}
                                    ` : `
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Description</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">
                                            ${data.proof && data.proof !== 'Not available' ?
                                                data.proof :
                                                'No description available'}
                                        </td>
                                    </tr>
                                    `}

                                    <!-- Specifications section removed as requested -->

                                    <!-- Buyer information if sold -->
                                    ${data.is_sold && data.buyer ? `
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Buyer</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.buyer.username}</td>
                                    </tr>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Date Purchased</th>
                                        <td class="px-4 py-2 text-sm text-gray-900">${data.buyer.date_purchased}</td>
                                    </tr>
                                    ` : ''}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Proof link for account items - keep this outside the table -->
                    ${data.item_type === 'account' && data.proof_link ? `
                    <div class="mt-4">
                        <button class="w-full px-3 py-2 text-sm border border-indigo-200 rounded-md bg-indigo-50 text-indigo-600 hover:bg-indigo-100 hover:text-indigo-700 transition-colors flex items-center justify-center"
                                onclick="window.open('${data.proof_link}', '_blank', 'width=800,height=600')">
                            <i class="fas fa-image mr-2"></i> View Screenshot Proof
                        </button>
                    </div>
                    ` : ''}
                `;

                // Update the modal content with real data
                content.innerHTML = detailsHtml;

                // Set up copy buttons after content is loaded
                setTimeout(() => {
                    setupCopyButtons();
                }, 100);
            })
            .catch(error => {
                console.error('Error fetching product details:', error);
                content.innerHTML = `
                    <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    Error loading product details. Please try again later.
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });
    }

    // Function to set up copy buttons
    function setupCopyButtons() {
        const copyButtons = document.querySelectorAll('.copy-btn');

        copyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-value');
                const originalIcon = this.innerHTML;

                // Copy text to clipboard
                navigator.clipboard.writeText(textToCopy)
                    .then(() => {
                        // Show success state
                        this.innerHTML = '<i class="fas fa-check text-green-500"></i>';
                        this.classList.add('bg-green-50');

                        // Reset after 2 seconds
                        setTimeout(() => {
                            this.innerHTML = originalIcon;
                            this.classList.remove('bg-green-50');
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy text: ', err);
                        // Show error state
                        this.innerHTML = '<i class="fas fa-times text-red-500"></i>';
                        this.classList.add('bg-red-50');
                    });
            });
        });
    }

    // Make these functions globally available
    window.showDeleteModal = showDeleteModal;
    window.closeDeleteModal = closeDeleteModal;
    window.showProductDetails = showProductDetails;
</script>
{% endblock %}

{% endblock %}
