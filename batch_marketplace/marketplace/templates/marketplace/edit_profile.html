{% extends 'marketplace/base.html' %}

{% block title %}Edit Profile - Oleer Market{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <a href="{% url 'profile' %}" class="text-gray-700 hover:text-blue-600">My Profile</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <span class="text-gray-500">Edit Profile</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="flex justify-center">
        <div class="w-full max-w-3xl">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
                <!-- Card Header -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <h4 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-user-edit mr-2 text-blue-500"></i>
                        Edit Profile
                    </h4>
                </div>

                <!-- Card Body -->
                <div class="p-6">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="mb-6 p-4 rounded-md bg-red-50 text-red-800">
                                {% for error in form.non_field_errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Email Field -->
                        <div class="mb-6">
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Email Address
                            </label>
                            <input type="email" name="email" id="{{ form.email.id_for_label }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                value="{{ form.email.value|default:'' }}">
                            {% if form.email.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Password Change Section -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                                <h5 class="font-medium text-gray-800 mb-3">Change Your Password</h5>

                                <form method="post" id="password-change-form" novalidate>
                                    {% csrf_token %}
                                    <input type="hidden" name="change_password_form" value="1">

                                    {% if password_form.non_field_errors %}
                                        <div class="mb-4 p-3 rounded-md bg-red-50 text-red-800 text-sm">
                                            {% for error in password_form.non_field_errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}

                                    <!-- Current Password Field -->
                                    <div class="mb-3">
                                        <label for="{{ password_form.old_password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-lock text-gray-400"></i>
                                            </div>
                                            <input type="password" name="old_password" id="{{ password_form.old_password.id_for_label }}" placeholder="Current password" required class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                        </div>
                                        {% if password_form.old_password.errors %}
                                            <div class="mt-1 text-sm text-red-600">
                                                {% for error in password_form.old_password.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- New Password Field -->
                                    <div class="mb-3">
                                        <label for="{{ password_form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-key text-gray-400"></i>
                                            </div>
                                            <input type="password" name="new_password1" id="{{ password_form.new_password1.id_for_label }}" placeholder="New password" required class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                        </div>
                                        {% if password_form.new_password1.errors %}
                                            <div class="mt-1 text-sm text-red-600">
                                                {% for error in password_form.new_password1.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        {% if password_form.new_password1.help_text %}
                                            <div class="mt-1 text-xs text-gray-500">
                                                {{ password_form.new_password1.help_text|safe }}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Confirm New Password Field -->
                                    <div class="mb-3">
                                        <label for="{{ password_form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-key text-gray-400"></i>
                                            </div>
                                            <input type="password" name="new_password2" id="{{ password_form.new_password2.id_for_label }}" placeholder="Confirm new password" required class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                        </div>
                                        {% if password_form.new_password2.errors %}
                                            <div class="mt-1 text-sm text-red-600">
                                                {% for error in password_form.new_password2.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="mt-4">
                                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                            <i class="fas fa-key mr-2"></i> Change Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Wallet Address Field -->
                        <div class="mb-6">
                            <label for="{{ form.wallet_address.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                Wallet Address
                            </label>
                            <input type="text" name="wallet_address" id="{{ form.wallet_address.id_for_label }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                value="{{ form.wallet_address.value|default:'' }}">
                            <p class="mt-1 text-sm text-gray-500">
                                Your USDT TRC-20 wallet address for receiving payments{% if user.profile.role == 'seller' %} (required for sellers){% endif %}.
                            </p>
                            {% if form.wallet_address.errors %}
                                <div class="mt-1 text-sm text-red-600">
                                    {% for error in form.wallet_address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Form Actions -->
                        <div class="flex justify-between items-center mt-8">
                            <a href="{% url 'profile' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i> Back to Profile
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-save mr-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
