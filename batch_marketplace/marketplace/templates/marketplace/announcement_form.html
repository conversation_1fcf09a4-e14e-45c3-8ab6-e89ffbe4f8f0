{% extends 'marketplace/base.html' %}

{% block title %}{{ action }} Announcement - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-bullhorn mr-3 text-purple-500"></i>
                {{ action }} Announcement
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                {% if action == 'Create' %}
                Create a new announcement for all users
                {% else %}
                Edit an existing announcement
                {% endif %}
            </p>
        </div>
        <div>
            <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 shadow-sm transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to List
            </a>
        </div>
    </div>
</div>

<!-- Announcement Form -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-edit mr-2 text-purple-500"></i> Announcement Details
        </h3>
    </div>
    <div class="p-6">
        <form method="post" class="space-y-6">
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700">
                                {{ form.non_field_errors }}
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Title Field -->
            <div>
                <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Title <span class="text-red-500">*</span>
                </label>
                {{ form.title }}
                {% if form.title.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</p>
                {% endif %}
            </div>

            <!-- Content Field -->
            <div>
                <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Content <span class="text-red-500">*</span>
                </label>
                {{ form.content }}
                {% if form.content.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.content.errors.0 }}</p>
                {% endif %}
                <p class="mt-1 text-sm text-gray-500">Provide detailed information for the announcement</p>
            </div>

            <!-- Priority and Status -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.priority.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Priority <span class="text-red-500">*</span>
                    </label>
                    {{ form.priority }}
                    {% if form.priority.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.priority.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">High priority announcements will be shown first</p>
                </div>

                <div>
                    <div class="flex items-center mt-6">
                        {{ form.is_active }}
                        <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                            Active
                        </label>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Only active announcements will be displayed to users</p>
                </div>
            </div>

            <!-- Icon and Background Color with Visual Previews -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="{{ form.icon.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Icon
                    </label>
                    <div class="relative">
                        {{ form.icon }}
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i id="icon-preview" class="fas fa-bullhorn text-gray-500"></i>
                        </div>
                    </div>
                    {% if form.icon.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.icon.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Select an icon for your announcement</p>

                    <!-- Icon Preview Grid -->
                    <div class="mt-3 grid grid-cols-5 gap-2">
                        {% for icon_value, icon_label in form.fields.icon.choices %}
                            <div class="icon-preview-item cursor-pointer p-2 rounded-lg hover:bg-gray-100 text-center"
                                 data-icon="{{ icon_value }}">
                                <i class="fas {{ icon_value }} text-xl mb-1"></i>
                                <div class="text-xs truncate">{{ icon_value }}</div>
                            </div>
                        {% endfor %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.bg_color.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Background Color
                    </label>
                    {{ form.bg_color }}
                    {% if form.bg_color.errors %}
                        <p class="mt-1 text-sm text-red-600">{{ form.bg_color.errors.0 }}</p>
                    {% endif %}
                    <p class="mt-1 text-sm text-gray-500">Select a background color for your announcement</p>

                    <!-- Color Preview Grid -->
                    <div class="mt-3 grid grid-cols-5 gap-2">
                        {% for color_value, color_label in form.fields.bg_color.choices %}
                            <div class="color-preview-item cursor-pointer rounded-lg overflow-hidden border border-gray-200 h-12"
                                 data-color="{{ color_value }}">
                                <div class="w-full h-full {{ color_value }}"></div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Preview of the announcement -->
            <div class="mt-6">
                <h4 class="text-sm font-medium text-gray-700 mb-2">Preview</h4>
                <div class="p-5 border border-gray-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div id="preview-icon-bg" class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center text-white">
                                <i id="preview-icon" class="fas fa-bullhorn text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between items-start mb-1">
                                <h4 id="preview-title" class="text-lg font-semibold text-gray-900">Announcement Title</h4>
                                <span class="text-sm text-gray-500">Just now</span>
                            </div>
                            <p id="preview-content" class="text-gray-700 mb-2">
                                Announcement content will appear here...
                            </p>
                            <div class="flex justify-between items-center">
                                <span id="preview-priority" class="bg-yellow-100 text-yellow-800 text-xs font-medium px-3 py-1 rounded-full">
                                    Medium
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- JavaScript for live preview -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // Get form elements
                    const titleInput = document.getElementById('{{ form.title.id_for_label }}');
                    const contentInput = document.getElementById('{{ form.content.id_for_label }}');
                    const prioritySelect = document.getElementById('{{ form.priority.id_for_label }}');
                    const iconSelect = document.getElementById('{{ form.icon.id_for_label }}');
                    const bgColorSelect = document.getElementById('{{ form.bg_color.id_for_label }}');

                    // Get preview elements
                    const previewTitle = document.getElementById('preview-title');
                    const previewContent = document.getElementById('preview-content');
                    const previewPriority = document.getElementById('preview-priority');
                    const previewIcon = document.getElementById('preview-icon');
                    const previewIconBg = document.getElementById('preview-icon-bg');
                    const iconPreview = document.getElementById('icon-preview');

                    // Update preview on input changes
                    titleInput.addEventListener('input', function() {
                        previewTitle.textContent = this.value || 'Announcement Title';
                    });

                    contentInput.addEventListener('input', function() {
                        previewContent.textContent = this.value || 'Announcement content will appear here...';
                    });

                    prioritySelect.addEventListener('change', function() {
                        const priorityValue = this.value;
                        previewPriority.className = 'text-xs font-medium px-3 py-1 rounded-full';

                        if (priorityValue === 'high') {
                            previewPriority.classList.add('bg-red-100', 'text-red-800');
                            previewPriority.textContent = 'High';
                        } else if (priorityValue === 'medium') {
                            previewPriority.classList.add('bg-yellow-100', 'text-yellow-800');
                            previewPriority.textContent = 'Medium';
                        } else {
                            previewPriority.classList.add('bg-blue-100', 'text-blue-800');
                            previewPriority.textContent = 'Low';
                        }
                    });

                    iconSelect.addEventListener('change', function() {
                        const iconValue = this.value;
                        previewIcon.className = 'fas ' + iconValue + ' text-xl';
                        iconPreview.className = 'fas ' + iconValue + ' text-gray-500';
                    });

                    bgColorSelect.addEventListener('change', function() {
                        const bgColorValue = this.value;
                        previewIconBg.className = 'w-12 h-12 rounded-full flex items-center justify-center text-white ' + bgColorValue;
                    });

                    // Icon preview items click handler
                    document.querySelectorAll('.icon-preview-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const iconValue = this.getAttribute('data-icon');
                            iconSelect.value = iconValue;
                            previewIcon.className = 'fas ' + iconValue + ' text-xl';
                            iconPreview.className = 'fas ' + iconValue + ' text-gray-500';
                        });
                    });

                    // Color preview items click handler
                    document.querySelectorAll('.color-preview-item').forEach(item => {
                        item.addEventListener('click', function() {
                            const colorValue = this.getAttribute('data-color');
                            bgColorSelect.value = colorValue;
                            previewIconBg.className = 'w-12 h-12 rounded-full flex items-center justify-center text-white ' + colorValue;
                        });
                    });

                    // Initialize preview with form values
                    if (titleInput.value) previewTitle.textContent = titleInput.value;
                    if (contentInput.value) previewContent.textContent = contentInput.value;

                    if (prioritySelect.value) {
                        const priorityValue = prioritySelect.value;
                        previewPriority.className = 'text-xs font-medium px-3 py-1 rounded-full';

                        if (priorityValue === 'high') {
                            previewPriority.classList.add('bg-red-100', 'text-red-800');
                            previewPriority.textContent = 'High';
                        } else if (priorityValue === 'medium') {
                            previewPriority.classList.add('bg-yellow-100', 'text-yellow-800');
                            previewPriority.textContent = 'Medium';
                        } else {
                            previewPriority.classList.add('bg-blue-100', 'text-blue-800');
                            previewPriority.textContent = 'Low';
                        }
                    }

                    if (iconSelect.value) {
                        previewIcon.className = 'fas ' + iconSelect.value + ' text-xl';
                        iconPreview.className = 'fas ' + iconSelect.value + ' text-gray-500';
                    }

                    if (bgColorSelect.value) {
                        previewIconBg.className = 'w-12 h-12 rounded-full flex items-center justify-center text-white ' + bgColorSelect.value;
                    }
                });
            </script>

            <!-- Submit Button -->
            <div class="flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 focus:ring-4 focus:ring-purple-200 shadow-md transition-all duration-300">
                    <i class="fas fa-save mr-2"></i> {{ action }} Announcement
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
