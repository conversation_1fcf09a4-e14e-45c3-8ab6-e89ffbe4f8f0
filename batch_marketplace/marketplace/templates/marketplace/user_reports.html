{% extends 'marketplace/base.html' %}
{% load static %}

{% block title %}My Reports - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="mb-8">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-flag mr-3 text-red-500"></i>
                My Reports
            </h1>
            <p class="mt-2 text-gray-600">View and manage your reported issues</p>
        </div>

        <div class="flex items-center space-x-4">
            {% if request.user.profile.role == 'buyer' %}
            <div class="flex items-center bg-gradient-to-r from-emerald-50 to-teal-50 px-4 py-2 rounded-lg border border-emerald-100">
                <span class="text-gray-600 mr-2">Balance:</span>
                <span class="font-semibold text-emerald-600">${{ request.user.profile.balance|floatformat:2 }}</span>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Breadcrumb -->
    <nav class="flex mt-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-gray-700 hover:text-blue-600 inline-flex items-center">
                    <i class="fas fa-home mr-2 text-gray-500"></i>
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <span class="text-gray-500">My Reports</span>
                </div>
            </li>
        </ol>
    </nav>
</div>

<!-- Alert Messages -->
{% if messages %}
<div class="mb-6 space-y-3">
    {% for message in messages %}
    <div class="p-4 rounded-lg border {% if message.tags == 'success' %}bg-green-50 border-green-200 text-green-800{% elif message.tags == 'error' %}bg-red-50 border-red-200 text-red-800{% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-200 text-yellow-800{% elif message.tags == 'info' %}bg-blue-50 border-blue-200 text-blue-800{% endif %} relative transition-all duration-300" role="alert">
        <div class="flex items-center">
            {% if message.tags == 'success' %}
            <i class="fas fa-check-circle text-green-500 mr-3 text-lg"></i>
            {% elif message.tags == 'error' %}
            <i class="fas fa-exclamation-circle text-red-500 mr-3 text-lg"></i>
            {% elif message.tags == 'warning' %}
            <i class="fas fa-exclamation-triangle text-yellow-500 mr-3 text-lg"></i>
            {% elif message.tags == 'info' %}
            <i class="fas fa-info-circle text-blue-500 mr-3 text-lg"></i>
            {% endif %}
            <p>{{ message }}</p>
        </div>
        <button type="button" class="absolute top-4 right-4 text-gray-400 hover:text-gray-900" data-dismiss-target="[role='alert']" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    </div>
    {% endfor %}
</div>
{% endif %}

<!-- Main Content -->
<div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div class="flex items-center">
            <h2 class="text-lg font-semibold text-gray-800 flex items-center mr-4">
                <i class="fas fa-clipboard-list text-blue-500 mr-2"></i>
                Report History
            </h2>
            <div class="flex space-x-2">
                <button id="filterOpen" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
                    Open
                </button>
                <button id="filterResolved" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors">
                    Resolved
                </button>
                <button id="filterAll" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors">
                    All
                </button>
            </div>
        </div>

        {% if reports %}
        <span class="text-sm text-gray-500">{{ reports|length }} report{{ reports|length|pluralize }}</span>
        {% endif %}
    </div>

    <div class="p-6">
        {% if reports %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-gray-50 text-left">
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tl-lg">ID</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Issue</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider rounded-tr-lg">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for report in reports %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ report.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ report.order_item.product.title }}</td>
                        <td class="px-6 py-4 text-sm text-gray-700 max-w-xs truncate">{{ report.issue|truncatechars:50 }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ report.created_at|date:"M d, Y" }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if report.resolved %}
                                {% if report.decision == 'refunded' %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                                    <i class="fas fa-money-bill-wave mr-1.5"></i>Refunded
                                </span>
                                {% elif report.decision == 'rejected' %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                    <i class="fas fa-times-circle mr-1.5"></i>Rejected
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                    <i class="fas fa-check-circle mr-1.5"></i>Resolved
                                </span>
                                {% endif %}
                            {% else %}
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                                    <i class="fas fa-clock mr-1.5"></i>Pending
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'order_report' report.order_item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-lg text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-200">
                                <i class="fas fa-eye mr-1.5"></i>
                                View Details
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-16 px-4">
            <div class="bg-gray-50 rounded-xl p-8 max-w-md mx-auto">
                <div class="w-20 h-20 bg-blue-100 text-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-flag text-3xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Reports Found</h3>
                <p class="text-gray-600 mb-6">You haven't submitted any reports yet. When you report an issue with a purchase, it will appear here.</p>
                <a href="{% url 'vps_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                    <i class="fas fa-shopping-cart mr-2"></i>
                    Browse Products
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
    // Alert dismissal with animation
    document.addEventListener('DOMContentLoaded', function() {
        const dismissButtons = document.querySelectorAll('[data-dismiss-target]');
        dismissButtons.forEach(button => {
            button.addEventListener('click', () => {
                const target = button.closest(button.getAttribute('data-dismiss-target'));
                if (target) {
                    target.classList.add('opacity-0');
                    setTimeout(() => {
                        target.classList.add('hidden');
                    }, 300);
                }
            });
        });

        // Report filtering functionality
        const filterOpenBtn = document.getElementById('filterOpen');
        const filterResolvedBtn = document.getElementById('filterResolved');
        const filterAllBtn = document.getElementById('filterAll');
        const reportTable = document.querySelector('table');

        let currentFilter = 'open'; // Default filter

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const statusParam = urlParams.get('status');

        // Set initial filter based on URL parameter
        if (statusParam) {
            currentFilter = statusParam;
            updateFilterButtons(currentFilter);
        }

        // Apply initial filter
        applyFilters();

        // Filter buttons click handlers
        if (filterOpenBtn) {
            filterOpenBtn.addEventListener('click', function() {
                currentFilter = 'open';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('open');
            });
        }

        if (filterResolvedBtn) {
            filterResolvedBtn.addEventListener('click', function() {
                currentFilter = 'resolved';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('resolved');
            });
        }

        if (filterAllBtn) {
            filterAllBtn.addEventListener('click', function() {
                currentFilter = 'all';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('all');
            });
        }

        // Update filter button styles
        function updateFilterButtons(filter) {
            // Reset all buttons
            [filterOpenBtn, filterResolvedBtn, filterAllBtn].forEach(btn => {
                if (btn) {
                    btn.classList.remove('bg-blue-500', 'text-white');
                    btn.classList.add('bg-gray-200', 'text-gray-700');
                }
            });

            // Highlight active button
            if (filter === 'open' && filterOpenBtn) {
                filterOpenBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterOpenBtn.classList.add('bg-blue-500', 'text-white');
            } else if (filter === 'resolved' && filterResolvedBtn) {
                filterResolvedBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterResolvedBtn.classList.add('bg-blue-500', 'text-white');
            } else if (filter === 'all' && filterAllBtn) {
                filterAllBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterAllBtn.classList.add('bg-blue-500', 'text-white');
            }
        }

        // Apply status filter
        function applyFilters() {
            if (!reportTable) return;

            const rows = reportTable.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const statusCell = row.querySelector('td:nth-child(5)');
                const statusText = statusCell ? statusCell.textContent.toLowerCase().trim() : '';

                let showByStatus = true;

                // Apply status filter
                if (currentFilter === 'open') {
                    showByStatus = statusText.includes('pending');
                } else if (currentFilter === 'resolved') {
                    showByStatus = statusText.includes('resolved') || statusText.includes('refunded') || statusText.includes('rejected');
                }

                // Show/hide row based on filter
                row.style.display = showByStatus ? '' : 'none';
            });
        }

        // Update URL with filter parameter
        function updateURL(filter) {
            const url = new URL(window.location);
            url.searchParams.set('status', filter);
            window.history.pushState({}, '', url);
        }
    });
</script>
{% endblock %}
