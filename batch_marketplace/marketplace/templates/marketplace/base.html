<!DOCTYPE html>
{% load static %}
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Oleer Market - Buy and sell digital items with cryptocurrency">
    <title>{% block title %}Oleer Market{% endblock %}</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a2a3a',
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        sidebar: {
                            bg: '#1e293b',
                            color: '#cbd5e1',
                            hover: '#3b82f6',
                            active: '#3b82f6',
                        },
                    },
                    width: {
                        'sidebar': '260px',
                        'sidebar-collapsed': '70px',
                    },
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">

    {% block extra_css %}{% endblock %}

    <style>
        /* Custom styles to fix sidebar overlap */
        @media (min-width: 1024px) {
            .lg\:ml-sidebar {
                margin-left: 260px; /* Match the sidebar width */
            }
        }

        /* Ensure content doesn't overlap with sidebar on mobile */
        @media (max-width: 1023px) {
            #sidebar {
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
                width: 260px; /* Ensure consistent width on mobile */
            }

            /* Fix for mobile sidebar toggle */
            #sidebar.translate-x-0 {
                transform: translateX(0);
            }

            #sidebar.-translate-x-full {
                transform: translateX(-100%);
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans antialiased text-gray-800">
    {% if user.is_authenticated %}
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-30 w-sidebar bg-gradient-to-b from-sidebar-bg to-gray-900 text-sidebar-color transform transition-transform duration-300 ease-in-out lg:translate-x-0 shadow-xl -translate-x-full lg:translate-x-0"
         id="sidebar" x-data>
        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-20 px-6 border-b border-gray-700/50 bg-gray-900/30">
            <a href="{% url 'index' %}" class="flex items-center space-x-3 text-white font-bold">
                <i class="fas fa-store text-2xl text-blue-400"></i>
                <span class="text-xl tracking-wide">Oleer Market</span>
            </a>
            <button class="text-gray-400 hover:text-white focus:outline-none focus:text-white lg:hidden"
                    @click="$store.sidebar.toggle()">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- User Info -->
        <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-900/20">
            <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-400">
                    <i class="fas fa-user-circle text-xl"></i>
                </div>
                <div class="ml-3 flex flex-col">
                    <span class="text-sm font-medium text-white">{{ user.username }}</span>
                    <div class="flex items-center gap-2 mt-1">
                        <span class="text-xs px-2 py-0.5 rounded-full inline-flex items-center justify-center
                            {% if user.profile.role == 'buyer' %}bg-blue-500 text-white
                            {% elif user.profile.role == 'seller' %}bg-green-500 text-white
                            {% else %}bg-purple-500 text-white{% endif %}">
                            {{ user.profile.role|title }}
                        </span>
                        {% if user.profile.role == 'buyer' %}
                            <span class="text-xs px-2 py-0.5 rounded-full bg-emerald-500/30 text-emerald-300 inline-flex items-center">
                                <i class="fas fa-wallet text-xs mr-1"></i>${{ user.profile.balance|floatformat:2 }}
                            </span>
                        {% endif %}
                        {% if user.profile.role == 'seller' %}
                            <span class="text-xs px-3 py-1 rounded-full border border-emerald-500/30 bg-emerald-500/10 text-emerald-300 inline-flex items-center shadow-sm ml-2">
                                <i class="fas fa-coins text-xs mr-1 text-emerald-400"></i>${{ total_gained_balance|default:"0.00"|floatformat:2 }}
                            </span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar Navigation -->
        <div class="py-4 overflow-y-auto">
            <div class="px-6 mb-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">General</div>

            <a href="{% url 'index' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if request.path == '/' %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-home w-5 h-5 mr-3 {% if request.path == '/' %}text-blue-300{% endif %}"></i>
                <span>Dashboard</span>
            </a>

            <a href="{% url 'vps_list' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'products' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-list w-5 h-5 mr-3 {% if 'products' in request.path %}text-blue-300{% endif %}"></i>
                <span>Browse Items</span>
            </a>

            <a href="{% url 'ticket_list' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'tickets' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-ticket-alt w-5 h-5 mr-3 {% if 'tickets' in request.path %}text-blue-300{% endif %}"></i>
                <span>Support Tickets</span>
            </a>

            {% if user.profile.role == 'support' or user.profile.role == 'admin' %}
            <a href="{% url 'user_reports' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'reports' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-flag w-5 h-5 mr-3 {% if 'reports' in request.path %}text-blue-300{% endif %}"></i>
                <span>Reports</span>
            </a>
            {% endif %}

            {% if user.profile.role == 'buyer' %}
            <div class="px-6 mt-6 mb-3 text-xs font-semibold text-blue-400 uppercase tracking-wider">Buyer Tools</div>

            <a href="{% url 'buyer_dashboard' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'buyer' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-shopping-cart w-5 h-5 mr-3 {% if 'buyer' in request.path %}text-blue-300{% endif %}"></i>
                <span>My Purchases</span>
            </a>

            <a href="{% url 'user_reports' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'reports' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-flag w-5 h-5 mr-3 {% if 'reports' in request.path %}text-blue-300{% endif %}"></i>
                <span>My Reports</span>
            </a>

            {% if user.profile.role == 'buyer' %}
            <a href="{% url 'topup_balance' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'topup-balance' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-wallet w-5 h-5 mr-3 {% if 'topup-balance' in request.path %}text-blue-300{% endif %}"></i>
                <span>Top Up Balance</span>
            </a>
            {% endif %}

            {% elif user.profile.role == 'seller' %}
            <div class="px-6 mt-6 mb-3 text-xs font-semibold text-green-400 uppercase tracking-wider">Seller Tools</div>

            <a href="{% url 'seller_dashboard' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'seller' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-store w-5 h-5 mr-3 {% if 'seller' in request.path %}text-blue-300{% endif %}"></i>
                <span>My Items</span>
            </a>

            <a href="{% url 'user_reports' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'reports' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-flag w-5 h-5 mr-3 {% if 'reports' in request.path %}text-blue-300{% endif %}"></i>
                <span>My Reports</span>
            </a>

            <a href="{% url 'add_account' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'add-account' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-plus-circle w-5 h-5 mr-3 {% if 'add-account' in request.path %}text-blue-300{% endif %}"></i>
                <span>Add Item</span>
            </a>

            <a href="{% url 'withdrawal_request' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'withdrawals' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-wallet w-5 h-5 mr-3 {% if 'withdrawals' in request.path %}text-blue-300{% endif %}"></i>
                <span>Withdraw Funds</span>
            </a>

            {% elif user.profile.role == 'admin' %}
            <div class="px-6 mt-6 mb-3 text-xs font-semibold text-purple-400 uppercase tracking-wider">Administration</div>

            <a href="{% url 'admin_dashboard' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 {% if 'admin-dashboard' in request.path %}text-white bg-sidebar-active shadow-md{% else %}text-sidebar-color hover:text-white hover:bg-gray-800/50 hover:pl-7{% endif %}">
                <i class="fas fa-shield-alt w-5 h-5 mr-3 {% if 'admin-dashboard' in request.path %}text-blue-300{% endif %}"></i>
                <span>Admin Panel</span>
            </a>
            {% endif %}

            <div class="px-6 mt-6 mb-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">Account</div>

            <a href="{% url 'logout' %}" class="flex items-center px-6 py-3 text-sm font-medium rounded-r-lg transition-all duration-200 text-red-400 hover:text-red-300 hover:bg-gray-800/50 hover:pl-7">
                <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Sidebar Overlay -->
    <div class="fixed inset-0 z-25 bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out lg:hidden opacity-0 pointer-events-none"
         id="sidebarOverlay"
         x-data
         @click="$store.sidebar.toggle()"></div>

    <!-- Main Content Wrapper -->
    <div class="{% if user.is_authenticated %}lg:ml-sidebar{% endif %} min-h-screen flex flex-col transition-all duration-300 relative z-20">
        <!-- Top Navbar -->
        <nav class="bg-white border-b border-gray-200 sticky top-0 z-40 shadow-sm">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        {% if user.is_authenticated %}
                        <!-- Mobile menu button -->
                        <button type="button"
                                class="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-secondary lg:hidden"
                                @click="$store.sidebar.toggle()"
                                onclick="toggleSidebar()"
                                id="sidebarToggle">
                            <span class="sr-only">Open sidebar</span>
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        {% endif %}

                        <!-- Logo -->
                        <a href="{% url 'index' %}" class="flex items-center space-x-2 {% if user.is_authenticated %}ml-2 lg:ml-0{% endif %}">
                            <i class="fas fa-store text-blue-500 text-xl"></i>
                            <span class="font-bold text-gray-900 text-lg">Oleer Market</span>
                        </a>
                    </div>

                    <!-- Right side navigation -->
                    <div class="flex items-center">
                        {% if not user.is_authenticated %}
                        <div class="hidden md:flex md:items-center md:space-x-4">
                            <a href="{% url 'vps_list' %}" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-secondary {% if 'products' in request.path %}text-secondary{% endif %}">
                                <i class="fas fa-server mr-1"></i> Browse Items
                            </a>
                            <a href="{% url 'login' %}" class="px-3 py-2 text-sm font-medium text-gray-700 hover:text-secondary {% if 'login' in request.path %}text-secondary{% endif %}">
                                <i class="fas fa-sign-in-alt mr-1"></i> Login
                            </a>
                            <a href="{% url 'register' %}" class="ml-4 px-4 py-2 text-sm font-medium text-white bg-secondary hover:bg-blue-600 rounded-md shadow-sm transition-colors">
                                <i class="fas fa-user-plus mr-1"></i> Register
                            </a>
                        </div>

                        <!-- Mobile menu dropdown -->
                        <div class="flex md:hidden">
                            <button type="button" class="p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-secondary"
                                    @click="mobileMenuOpen = !mobileMenuOpen"
                                    x-data="{ mobileMenuOpen: false }" x-dropdown>
                                <span class="sr-only">Open menu</span>
                                <i class="fas fa-bars text-xl"></i>

                                <!-- Mobile menu panel -->
                                <div x-show="mobileMenuOpen"
                                     class="absolute top-16 right-0 left-0 bg-white shadow-md z-50 border-t border-gray-200 hidden"
                                     x-transition:enter="transition ease-out duration-200"
                                     x-transition:enter-start="opacity-0 -translate-y-1"
                                     x-transition:enter-end="opacity-100 translate-y-0"
                                     x-transition:leave="transition ease-in duration-150"
                                     x-transition:leave-start="opacity-100 translate-y-0"
                                     x-transition:leave-end="opacity-0 -translate-y-1"
                                     @click.away="mobileMenuOpen = false">
                                    <div class="pt-2 pb-3 space-y-1">
                                        <a href="{% url 'vps_list' %}" class="block px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 {% if 'products' in request.path %}text-secondary{% endif %}">
                                            <i class="fas fa-server mr-2"></i> Browse Items
                                        </a>
                                        <a href="{% url 'login' %}" class="block px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 {% if 'login' in request.path %}text-secondary{% endif %}">
                                            <i class="fas fa-sign-in-alt mr-2"></i> Login
                                        </a>
                                        <a href="{% url 'register' %}" class="block px-4 py-2 text-base font-medium text-gray-700 hover:bg-gray-100 {% if 'register' in request.path %}text-secondary{% endif %}">
                                            <i class="fas fa-user-plus mr-2"></i> Register
                                        </a>
                                    </div>
                                </div>
                            </button>
                        </div>
                        {% else %}
                        <!-- Notifications dropdown -->
                        <div class="ml-4 relative flex-shrink-0" x-data="{ open: false }" x-dropdown>
                            <button type="button" id="mainNotificationButton" class="p-1 rounded-full text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary relative notification-button">
                                <span class="sr-only">View notifications</span>
                                <i class="fas fa-bell text-xl"></i>
                                {% if unread_notification_count > 0 %}
                                <span class="absolute top-0 right-0 block h-5 w-5 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center transform -translate-y-1/4 translate-x-1/4">
                                    {{ unread_notification_count }}
                                </span>
                                {% endif %}
                            </button>

                            <!-- Notifications panel -->
                            <div id="mainNotificationMenu"
                                 class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden dropdown-menu notification-dropdown"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95">
                                <div class="py-1">
                                    <div class="px-4 py-2 border-b border-gray-200">
                                        <p class="text-sm font-medium text-gray-700">
                                            {% if unread_notification_count > 0 %}
                                            <i class="fas fa-bell mr-2 text-secondary"></i>{{ unread_notification_count }} New Notifications
                                            {% else %}
                                            <i class="fas fa-bell mr-2"></i>No New Notifications
                                            {% endif %}
                                        </p>
                                    </div>

                                    <div class="max-h-60 overflow-y-auto">
                                        {% for notification in recent_notifications|slice:":3" %}
                                        <div class="px-4 py-3 hover:bg-gray-50 relative group">
                                            <a href="{{ notification.link|default:'#' }}" class="block">
                                                <div class="flex">
                                                    <div class="flex-shrink-0 mr-3">
                                                        <div class="h-8 w-8 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center">
                                                            <i class="fas fa-bell"></i>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-gray-900 truncate">{{ notification.title }}</p>
                                                        <p class="text-sm text-gray-500 truncate">{{ notification.message|truncatewords:10 }}</p>
                                                        <p class="text-xs text-gray-400 mt-1">{{ notification.created_at|timesince }} ago</p>
                                                    </div>
                                                </div>
                                            </a>
                                            <button type="button"
                                                    class="absolute top-3 right-4 text-gray-400 hover:text-gray-600 notification-delete"
                                                    data-notification-id="{{ notification.id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        {% empty %}
                                        <div class="px-4 py-6 text-center text-sm text-gray-500">
                                            You have no new notifications.
                                        </div>
                                        {% endfor %}
                                    </div>

                                    <div class="border-t border-gray-200 py-2 px-4">
                                        <a href="{% url 'notifications' %}" class="block text-center text-sm font-medium text-blue-600 hover:text-blue-800">
                                            View All Notifications
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile dropdown -->
                        <div class="ml-4 relative flex-shrink-0">
                            <button type="button" id="mainProfileButton" class="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary profile-button">
                                <span class="sr-only">Open user menu</span>
                                <i class="fas fa-user-circle text-2xl text-gray-500"></i>
                                <span class="hidden md:block font-medium text-gray-700">{{ user.username }}</span>
                                <i class="fas fa-chevron-down text-xs text-gray-500"></i>
                            </button>

                            <!-- Profile dropdown panel -->
                            <div id="mainProfileMenu"
                                 class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden dropdown-menu profile-dropdown"
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95">
                                <div class="py-1">
                                    <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i> My Profile
                                    </a>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>

        <!-- Messages -->
        {% if messages %}
        <div class="px-4 sm:px-6 lg:px-8 py-4">
            {% for message in messages %}
            <div class="rounded-md p-4 mb-4 {% if message.tags == 'success' %}bg-green-50 text-green-800{% elif message.tags == 'info' %}bg-blue-50 text-blue-800{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800{% elif message.tags == 'error' %}bg-red-50 text-red-800{% else %}bg-gray-50 text-gray-800{% endif %} shadow-sm relative"
                 x-data="{ show: true }"
                 x-show="show"
                 x-transition:leave="transition ease-in duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% elif message.tags == 'info' %}fa-info-circle text-blue-500{% elif message.tags == 'warning' %}fa-exclamation-triangle text-yellow-500{% elif message.tags == 'error' %}fa-times-circle text-red-500{% else %}fa-bell text-gray-500{% endif %}"></i>
                    </div>
                    <div class="ml-3 flex-1">
                        <p class="text-sm">{{ message }}</p>
                    </div>
                    <div class="ml-auto pl-3">
                        <div class="-mx-1.5 -my-1.5">
                            <button type="button"
                                    class="inline-flex rounded-md p-1.5 {% if message.tags == 'success' %}text-green-500 hover:bg-green-100{% elif message.tags == 'info' %}text-blue-500 hover:bg-blue-100{% elif message.tags == 'warning' %}text-yellow-500 hover:bg-yellow-100{% elif message.tags == 'error' %}text-red-500 hover:bg-red-100{% else %}text-gray-500 hover:bg-gray-100{% endif %} focus:outline-none"
                                    @click="show = false">
                                <span class="sr-only">Dismiss</span>
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Main Content -->
        <main class="flex-grow px-4 sm:px-6 lg:px-8 py-6">
            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 py-6">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                    <div>
                        <p class="text-sm text-gray-500">
                            <a href="{% url 'index' %}" class="font-semibold text-gray-700 hover:text-blue-500">Oleer Market</a> &copy; 2025
                        </p>
                    </div>
                    <div>
                        <ul class="flex space-x-6">
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Support</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Help Center</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Privacy</a>
                            </li>
                            <li>
                                <a href="#" class="text-sm text-gray-500 hover:text-gray-700">Terms</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Back to top button -->
    <button type="button"
            class="fixed bottom-6 right-6 p-3 bg-secondary hover:bg-blue-600 text-white rounded-full shadow-lg transition-all duration-300 opacity-0 pointer-events-none transform translate-y-10"
            id="btn-back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.12.3/dist/cdn.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- Ensure Alpine.js is loaded -->
    <script>
        // Vanilla JS fallback function for toggling sidebar
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (sidebar) {
                if (sidebar.classList.contains('-translate-x-full')) {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.remove('hidden', 'opacity-0', 'pointer-events-none');
                        sidebarOverlay.classList.add('opacity-100', 'pointer-events-auto');
                    }
                } else {
                    sidebar.classList.add('-translate-x-full');
                    sidebar.classList.remove('translate-x-0');
                    if (sidebarOverlay) {
                        sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                        sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                    }
                }
            }
        }

        // Check if Alpine.js is loaded after a short delay
        setTimeout(function() {
            if (typeof Alpine === 'undefined') {
                console.error('Alpine.js failed to load. Using fallback.');
                // Add fallback for sidebar toggle
                const sidebarToggleBtn = document.getElementById('sidebarToggle');

                // We don't need to add event listener here since we added onclick handler directly
                // But we'll keep this as a backup
                if (sidebarToggleBtn) {
                    sidebarToggleBtn.addEventListener('click', toggleSidebar);
                }
            }
        }, 1000);
    </script>

    <!-- Additional scripts -->
    <script>
        // Initialize Alpine.js store for sidebar state
        document.addEventListener('alpine:init', () => {
            // Global variable to track open Alpine dropdowns
            window.openAlpineDropdown = null;

            // Ensure all dropdowns are hidden by default and manage interactions
            Alpine.directive('dropdown', (el) => {
                el._x_dropdown = {
                    init() {
                        // Add hidden class to dropdown elements
                        const dropdown = el.querySelector('[x-show="open"]');
                        if (dropdown && !dropdown.classList.contains('hidden')) {
                            dropdown.classList.add('hidden');
                        }

                        // Add click handler to toggle button
                        const button = el.querySelector('[x-data]');
                        if (button) {
                            button.addEventListener('click', () => {
                                // Close any vanilla JS dropdowns when Alpine dropdown is opened
                                if (window.currentOpenDropdown) {
                                    window.currentOpenDropdown.classList.add('hidden');
                                    window.currentOpenDropdown = null;
                                }

                                // Close any other Alpine dropdowns
                                if (window.openAlpineDropdown && window.openAlpineDropdown !== el) {
                                    const openDropdown = window.openAlpineDropdown.querySelector('[x-show="open"]');
                                    if (openDropdown && !openDropdown.classList.contains('hidden')) {
                                        window.openAlpineDropdown.__x.setData('open', false);
                                    }
                                }

                                // Set this as the currently open Alpine dropdown
                                window.openAlpineDropdown = el;

                                // Close sidebar on mobile when opening dropdown
                                if (window.innerWidth < 1024) {
                                    const sidebar = document.getElementById('sidebar');
                                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                                    if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                                        sidebar.classList.add('-translate-x-full');
                                        sidebar.classList.remove('translate-x-0');
                                        if (sidebarOverlay) {
                                            sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                                            sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                                        }
                                        Alpine.store('sidebar').open = false;
                                    }
                                }
                            });
                        }
                    }
                };
                el._x_dropdown.init();
            });

            Alpine.store('sidebar', {
                open: false,
                toggle() {
                    this.open = !this.open;

                    // Close any open dropdowns when toggling sidebar
                    if (this.open) {
                        // Close any vanilla JS dropdowns
                        if (window.currentOpenDropdown) {
                            window.currentOpenDropdown.classList.add('hidden');
                            window.currentOpenDropdown = null;
                        }

                        // Close any Alpine.js dropdowns
                        if (window.openAlpineDropdown) {
                            const openDropdown = window.openAlpineDropdown.querySelector('[x-show="open"]');
                            if (openDropdown && !openDropdown.classList.contains('hidden')) {
                                window.openAlpineDropdown.__x.setData('open', false);
                            }
                            window.openAlpineDropdown = null;
                        }

                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown-menu').forEach(dropdown => {
                            dropdown.classList.add('hidden');
                        });
                    }

                    // Update sidebar and overlay classes
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');

                    if (sidebar && sidebarOverlay) {
                        if (this.open) {
                            sidebar.classList.remove('-translate-x-full');
                            sidebar.classList.add('translate-x-0');
                            sidebarOverlay.classList.remove('opacity-0', 'pointer-events-none');
                            sidebarOverlay.classList.add('opacity-100', 'pointer-events-auto');
                        } else {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');
                            sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                            sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                        }
                    }
                }
            });
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Back to top button functionality
            const backToTopButton = document.getElementById('btn-back-to-top');

            if (backToTopButton) {
                window.addEventListener('scroll', function() {
                    if (window.pageYOffset > 300) {
                        backToTopButton.classList.remove('opacity-0', 'pointer-events-none', 'translate-y-10');
                        backToTopButton.classList.add('opacity-100', 'pointer-events-auto', 'translate-y-0');
                    } else {
                        backToTopButton.classList.add('opacity-0', 'pointer-events-none', 'translate-y-10');
                        backToTopButton.classList.remove('opacity-100', 'pointer-events-auto', 'translate-y-0');
                    }
                });

                backToTopButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // Function to handle dropdown toggles
            function setupDropdown(buttonId, menuId) {
                const button = document.getElementById(buttonId);
                const menu = document.getElementById(menuId);

                if (!button || !menu) return;

                // Remove Alpine.js attributes from parent div to avoid conflicts
                const parentDiv = button.closest('[x-data]');
                if (parentDiv) {
                    parentDiv.removeAttribute('x-data');
                    parentDiv.removeAttribute('x-dropdown');
                }

                // Add click event listener
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close any other open dropdowns
                    if (window.currentOpenDropdown && window.currentOpenDropdown !== menu) {
                        window.currentOpenDropdown.classList.add('hidden');
                    }

                    // Close any Alpine.js dropdowns
                    if (window.openAlpineDropdown) {
                        try {
                            window.openAlpineDropdown.__x.setData('open', false);
                        } catch (err) {
                            // Ignore errors if Alpine data is not available
                        }
                        window.openAlpineDropdown = null;
                    }

                    // Close sidebar on mobile
                    if (window.innerWidth < 1024) {
                        const sidebar = document.getElementById('sidebar');
                        const sidebarOverlay = document.getElementById('sidebarOverlay');
                        if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                            sidebar.classList.add('-translate-x-full');
                            sidebar.classList.remove('translate-x-0');
                            if (sidebarOverlay) {
                                sidebarOverlay.classList.add('opacity-0', 'pointer-events-none');
                                sidebarOverlay.classList.remove('opacity-100', 'pointer-events-auto');
                            }
                            if (window.Alpine && Alpine.store('sidebar')) {
                                Alpine.store('sidebar').open = false;
                            }
                        }
                    }

                    // Toggle menu
                    menu.classList.toggle('hidden');

                    // Update global tracking variables
                    if (menu.classList.contains('hidden')) {
                        window.currentOpenDropdown = null;
                    } else {
                        window.currentOpenDropdown = menu;

                        // Add click outside listener
                        setTimeout(() => {
                            const closeDropdown = (event) => {
                                if (!menu.contains(event.target) && !button.contains(event.target)) {
                                    menu.classList.add('hidden');
                                    window.currentOpenDropdown = null;
                                    document.removeEventListener('click', closeDropdown);
                                }
                            };
                            document.addEventListener('click', closeDropdown);
                        }, 10); // Increased timeout to ensure event binding happens after other events
                    }
                });
            }

            // Setup notification dropdown
            setupDropdown('mainNotificationButton', 'mainNotificationMenu');

            // Setup profile dropdown
            setupDropdown('mainProfileButton', 'mainProfileMenu');

            // Mobile sidebar toggle is now handled by Alpine.js
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
