{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create An Account - Oleer Market</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a2a3a',
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">
</head>
<body class="bg-gray-50 font-sans antialiased text-gray-800">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
        <div class="w-full max-w-md">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <!-- Logo and Title -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-3">
                        <img src="{% static 'img/logo.ico' %}" alt="Oleer Market Logo" class="h-32 transform hover:scale-105 transition-transform duration-300">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">Create An Account</h1>
                    <p class="text-sm text-gray-600 mt-2">Fill in the form below to create your account</p>
                </div>

                <!-- Registration Form -->
                <form method="post" novalidate class="space-y-6">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                        <div class="p-4 mb-4 rounded-md bg-red-50 text-red-800">
                            {% for error in form.non_field_errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <!-- Email Field -->
                    <div>
                        <label for="id_email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" name="email" id="id_email" placeholder="Email address" required
                                class="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        {% if form.email.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="id_password1" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" name="password1" id="id_password1" placeholder="Password" required
                                class="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        {% if form.password1.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.password1.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Confirm Password Field -->
                    <div>
                        <label for="id_password2" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                        <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" name="password2" id="id_password2" placeholder="Confirm password" required
                                class="appearance-none block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        {% if form.password2.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {% for error in form.password2.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Hidden role field - default to buyer -->
                    <div class="hidden">
                        <input type="radio" name="role" value="buyer" checked>
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Create Account
                        </button>
                    </div>
                </form>

                <!-- Login Link -->
                <div class="mt-6 text-center text-sm">
                    <p class="text-gray-600">
                        Already have an account?
                        <a href="{% url 'login' %}" class="font-medium text-blue-600 hover:text-blue-500 transition duration-150">
                            Login
                        </a>
                    </p>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center text-xs text-gray-500">
                <p>&copy; 2025 Oleer Market. All rights reserved.</p>
            </div>
        </div>
    </div>


</body>
</html>
