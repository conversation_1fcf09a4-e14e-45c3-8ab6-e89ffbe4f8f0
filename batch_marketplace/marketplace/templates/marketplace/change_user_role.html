{% extends 'marketplace/base.html' %}

{% block title %}Change User Role - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-user-edit mr-3 text-purple-500"></i>
                Change User Role
            </h1>
            <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
                Modify role for user: <span class="font-medium text-gray-900 dark:text-white">{{ target_user.username }}</span>
            </p>
        </div>
        <div>
            <a href="{% url 'manage_user_roles' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 dark:bg-gray-700 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i> Back to User List
            </a>
        </div>
    </div>
</div>

<!-- Change Role Form -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-user-tag mr-2 text-purple-500"></i> Role Information
        </h3>
    </div>
    <div class="p-6">
        <div class="mb-6">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-300">
                    <i class="fas fa-user text-xl"></i>
                </div>
                <div>
                    <h4 class="text-lg font-medium text-gray-900 dark:text-white">{{ target_user.username }}</h4>
                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ target_user.email }}</p>
                </div>
            </div>
            
            <div class="mb-4">
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    <span class="font-medium">Current Role:</span> 
                    {% if target_user.profile.role == 'buyer' %}
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Buyer</span>
                    {% elif target_user.profile.role == 'seller' %}
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-200">Seller</span>
                    {% elif target_user.profile.role == 'admin' %}
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">Admin</span>
                    {% elif target_user.profile.role == 'support' %}
                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200">Support</span>
                    {% endif %}
                </p>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    <span class="font-medium">Joined:</span> {{ target_user.date_joined|date:"F d, Y" }}
                </p>
            </div>
        </div>

        <form method="post" class="space-y-6">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg dark:bg-red-900 dark:text-red-200">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
            
            <div class="space-y-4">
                <div>
                    <label for="{{ form.role.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        New Role
                    </label>
                    <select name="{{ form.role.name }}" id="{{ form.role.id_for_label }}" class="w-full rounded-lg border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white py-2 px-3">
                        {% for value, text in form.role.field.choices %}
                            <option value="{{ value }}" {% if form.role.value == value %}selected{% endif %}>{{ text }}</option>
                        {% endfor %}
                    </select>
                    {% if form.role.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.role.errors.0 }}</p>
                    {% endif %}
                </div>
                
                <div>
                    <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Notes (Optional)
                    </label>
                    <textarea name="{{ form.notes.name }}" id="{{ form.notes.id_for_label }}" rows="3" class="w-full rounded-lg border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white py-2 px-3" placeholder="Add notes about this role change...">{{ form.notes.value|default:'' }}</textarea>
                    {% if form.notes.errors %}
                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.notes.errors.0 }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="flex items-center justify-end space-x-4 pt-4">
                <a href="{% url 'manage_user_roles' %}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-800">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-offset-gray-800">
                    Change Role
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
