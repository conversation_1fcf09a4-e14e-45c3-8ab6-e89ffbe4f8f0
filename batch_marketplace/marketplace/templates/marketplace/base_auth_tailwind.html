{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Oleer Market{% endblock %}</title>

    <!-- CSRF Token -->
    {% csrf_token %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a2a3a',
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 font-sans antialiased text-gray-800">
    {% if user.is_authenticated %}
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out" id="sidebar">
        <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200">
            <a href="{% url 'index' %}" class="flex items-center space-x-2 text-gray-900">
                <i class="fas fa-store text-blue-500 text-xl"></i>
                <span class="font-bold">Oleer Market</span>
            </a>
            <button class="text-gray-500 hover:text-gray-700 lg:hidden" id="closeSidebar">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="py-4 px-4">
            <ul class="space-y-2">
                {% if user.profile.role == 'buyer' %}
                <!-- Buyer Navigation -->
                <li>
                    <a href="{% url 'index' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if request.path == '/' %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-home w-5"></i>
                        <span class="ml-3">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'vps_list' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'products' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-list w-5"></i>
                        <span class="ml-3">Browse Items</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'ticket_list' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'tickets' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-ticket-alt w-5"></i>
                        <span class="ml-3">Support Tickets</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'buyer_dashboard' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'buyer' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-shopping-cart w-5"></i>
                        <span class="ml-3">My Purchases</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'user_reports' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'reports' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-flag w-5"></i>
                        <span class="ml-3">My Reports</span>
                    </a>
                </li>
                {% if user.profile.role == 'buyer' %}
                <li>
                    <a href="{% url 'topup_balance' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'topup-balance' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-wallet w-5"></i>
                        <span class="ml-3">Top Up Balance</span>
                    </a>
                </li>
                {% endif %}
                {% elif user.profile.role == 'seller' %}
                <!-- Seller Navigation -->
                <li class="mt-4 mb-2">
                    <span class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">General</span>
                </li>
                <li>
                    <a href="{% url 'index' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if request.path == '/' %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-home w-5"></i>
                        <span class="ml-3">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'vps_list' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'products' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-list w-5"></i>
                        <span class="ml-3">Browse Items</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'ticket_list' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'tickets' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-ticket-alt w-5"></i>
                        <span class="ml-3">Support Tickets</span>
                    </a>
                </li>
                <li class="mt-4 mb-2">
                    <span class="px-4 text-xs font-semibold text-gray-500 uppercase tracking-wider">Seller Tools</span>
                </li>
                <li>
                    <a href="{% url 'seller_dashboard' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'seller' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-store w-5"></i>
                        <span class="ml-3">My Items</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'user_reports' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'reports' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-flag w-5"></i>
                        <span class="ml-3">My Reports</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'add_account' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'add-account' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-plus-circle w-5"></i>
                        <span class="ml-3">Add Item</span>
                    </a>
                </li>
                <li>
                    <a href="{% url 'withdrawal_request' %}" class="flex items-center px-4 py-2 text-gray-700 rounded-md hover:bg-gray-100 {% if 'withdrawals' in request.path %}bg-blue-50 text-blue-700{% endif %}">
                        <i class="fas fa-wallet w-5"></i>
                        <span class="ml-3">Withdraw Funds</span>
                    </a>
                </li>
                {% endif %}
                <li class="mt-4">
                    <a href="{% url 'logout' %}" class="flex items-center px-4 py-2 text-red-600 rounded-md hover:bg-red-50">
                        <i class="fas fa-sign-out-alt w-5"></i>
                        <span class="ml-3">Logout</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    {% endif %}

    <!-- Sidebar Overlay -->
    <div class="fixed inset-0 z-40 bg-black bg-opacity-50 hidden" id="sidebarOverlay"></div>

    <!-- Main Content -->
    <div class="{% if user.is_authenticated %}ml-0 lg:ml-64{% endif %} transition-all duration-300">
        <!-- Top Navbar -->
        <nav class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        {% if user.is_authenticated %}
                        <button class="text-gray-500 hover:text-gray-700 lg:hidden" id="openSidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        {% endif %}
                        <a href="{% url 'index' %}" class="flex items-center space-x-2 text-gray-900 ml-2">
                            <i class="fas fa-store text-blue-500"></i>
                            <span class="font-bold">Oleer Market</span>
                        </a>
                    </div>

                    {% if user.is_authenticated %}
                    <div class="flex items-center">
                        <!-- Notifications dropdown -->
                        <div class="relative flex-shrink-0">
                            <button type="button" class="p-1 rounded-full text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 relative notification-button" id="notificationButton">
                                <span class="sr-only">View notifications</span>
                                <i class="fas fa-bell text-xl"></i>
                                {% if unread_notification_count > 0 %}
                                <span class="absolute top-0 right-0 block h-5 w-5 rounded-full bg-red-500 text-white text-xs font-medium flex items-center justify-center transform -translate-y-1/4 translate-x-1/4">
                                    {{ unread_notification_count }}
                                </span>
                                {% endif %}
                            </button>

                            <!-- Notifications panel -->
                            <div class="hidden origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none dropdown-menu notification-dropdown" id="notificationMenu">
                                <div class="py-1">
                                    <div class="px-4 py-2 border-b border-gray-200">
                                        <p class="text-sm font-medium text-gray-700">
                                            {% if unread_notification_count > 0 %}
                                            <i class="fas fa-bell mr-2 text-blue-500"></i>{{ unread_notification_count }} New Notifications
                                            {% else %}
                                            <i class="fas fa-bell mr-2"></i>No New Notifications
                                            {% endif %}
                                        </p>
                                    </div>

                                    <div class="max-h-60 overflow-y-auto">
                                        {% for notification in recent_notifications|slice:":3" %}
                                        <div class="px-4 py-3 hover:bg-gray-50 relative group">
                                            <a href="{{ notification.link|default:'#' }}" class="block">
                                                <div class="flex">
                                                    <div class="flex-shrink-0 mr-3">
                                                        <div class="h-8 w-8 rounded-full bg-blue-100 text-blue-500 flex items-center justify-center">
                                                            <i class="fas fa-bell"></i>
                                                        </div>
                                                    </div>
                                                    <div class="flex-1 min-w-0">
                                                        <p class="text-sm font-medium text-gray-900 truncate">{{ notification.title }}</p>
                                                        <p class="text-sm text-gray-500 truncate">{{ notification.message|truncatewords:10 }}</p>
                                                        <p class="text-xs text-gray-400 mt-1">{{ notification.created_at|timesince }} ago</p>
                                                    </div>
                                                </div>
                                            </a>
                                            <button type="button"
                                                    class="absolute top-3 right-4 text-gray-400 hover:text-gray-600 notification-delete"
                                                    data-notification-id="{{ notification.id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        {% empty %}
                                        <div class="px-4 py-6 text-center text-sm text-gray-500">
                                            You have no new notifications.
                                        </div>
                                        {% endfor %}
                                    </div>

                                    <div class="border-t border-gray-200 py-2 px-4">
                                        <a href="{% url 'notifications' %}" class="block text-center text-sm font-medium text-blue-600 hover:text-blue-800">
                                            View All Notifications
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Menu -->
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="flex items-center text-sm rounded-full focus:outline-none" id="userMenuButton">
                                    <span class="mr-2 text-gray-700">{{ user.username }}</span>
                                    <i class="fas fa-user-circle text-gray-700 text-xl"></i>
                                </button>
                            </div>
                            <div class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5" id="userMenu">
                                <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user mr-2"></i> My Profile
                                </a>
                                <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt mr-2"></i> Logout
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="py-6 px-4 sm:px-6 lg:px-8">
            {% if messages %}
                <div class="max-w-7xl mx-auto mb-6">
                    {% for message in messages %}
                        <div class="p-4 mb-4 rounded-md {% if message.tags == 'success' %}bg-green-50 text-green-800{% elif message.tags == 'info' %}bg-blue-50 text-blue-800{% elif message.tags == 'warning' %}bg-yellow-50 text-yellow-800{% elif message.tags == 'error' %}bg-red-50 text-red-800{% else %}bg-gray-50 text-gray-800{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            {% block content %}{% endblock %}
        </main>
    </div>

    <!-- Back to top button -->
    <button type="button" class="fixed bottom-4 right-4 p-2 rounded-full bg-blue-600 text-white shadow-lg hover:bg-blue-700 focus:outline-none" id="btn-back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // Ensure all dropdowns are hidden by default
        function ensureDropdownsHidden() {
            // Add hidden class to dropdown menus
            const dropdowns = document.querySelectorAll('#userMenu, #notificationMenu, .dropdown-menu');
            dropdowns.forEach(dropdown => {
                if (!dropdown.classList.contains('hidden')) {
                    dropdown.classList.add('hidden');
                }
            });
        }

        // Run on page load
        document.addEventListener('DOMContentLoaded', function() {
            ensureDropdownsHidden();
        });

        // Also run after a short delay to catch any late-initializing elements
        setTimeout(ensureDropdownsHidden, 100);

        // Sidebar toggle
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebarOverlay');
        const openSidebarBtn = document.getElementById('openSidebar');
        const closeSidebarBtn = document.getElementById('closeSidebar');

        if (openSidebarBtn) {
            openSidebarBtn.addEventListener('click', () => {
                // Close any open dropdowns when opening sidebar
                if (window.currentOpenDropdown) {
                    window.currentOpenDropdown.classList.add('hidden');
                    window.currentOpenDropdown = null;
                }

                sidebar.classList.remove('-translate-x-full');
                sidebarOverlay.classList.remove('hidden');
            });
        }

        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', () => {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            });
        }

        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => {
                sidebar.classList.add('-translate-x-full');
                sidebarOverlay.classList.add('hidden');
            });
        }

        // Global variable to track which dropdown is currently open
        window.currentOpenDropdown = null;

        // Function to handle dropdown toggles
        function setupDropdown(buttonId, menuId) {
            const button = document.getElementById(buttonId);
            const menu = document.getElementById(menuId);

            if (!button || !menu) return;

            // Remove any existing event listeners by cloning the button
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // Add click event listener
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // If this dropdown is already open, just close it
                if (window.currentOpenDropdown === menu) {
                    menu.classList.add('hidden');
                    window.currentOpenDropdown = null;
                    return;
                }

                // Close any other open dropdowns
                if (window.currentOpenDropdown) {
                    window.currentOpenDropdown.classList.add('hidden');
                    window.currentOpenDropdown = null;
                }

                // Close sidebar on mobile when opening dropdown
                if (window.innerWidth < 1024) {
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                    if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                        sidebar.classList.add('-translate-x-full');
                        sidebarOverlay.classList.add('hidden');
                    }
                }

                // Toggle menu
                menu.classList.toggle('hidden');

                // Update global tracking variables
                if (menu.classList.contains('hidden')) {
                    window.currentOpenDropdown = null;
                } else {
                    window.currentOpenDropdown = menu;

                    // Add click outside listener
                    setTimeout(() => {
                        const closeDropdown = (event) => {
                            if (!menu.contains(event.target) && !newButton.contains(event.target)) {
                                menu.classList.add('hidden');
                                window.currentOpenDropdown = null;
                                document.removeEventListener('click', closeDropdown);
                            }
                        };
                        document.addEventListener('click', closeDropdown);
                    }, 20); // Increased timeout to ensure event binding happens after other events
                }
            });
        }

        // Setup notification dropdown
        setupDropdown('notificationButton', 'notificationMenu');

        // Setup user menu dropdown
        setupDropdown('userMenuButton', 'userMenu');

        // Handle notification delete buttons
        document.addEventListener('click', function(e) {
            const deleteButton = e.target.closest('.notification-delete');
            if (deleteButton) {
                e.preventDefault();
                e.stopPropagation();

                const notificationId = deleteButton.getAttribute('data-notification-id');
                const notificationItem = deleteButton.closest('div');

                if (notificationId && notificationItem) {
                    // Send AJAX request to delete the notification
                    fetch(`/notifications/${notificationId}/delete/`, {
                        method: 'GET',
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Add fade-out animation
                            notificationItem.style.transition = 'all 0.3s ease';
                            notificationItem.style.opacity = '0';
                            notificationItem.style.height = '0';
                            notificationItem.style.padding = '0';
                            notificationItem.style.margin = '0';
                            notificationItem.style.overflow = 'hidden';

                            // Remove the notification from the UI after animation
                            setTimeout(() => {
                                notificationItem.remove();

                                // Update the notification counter
                                const counter = document.querySelector('.fa-bell').nextElementSibling;
                                if (counter && counter.classList.contains('absolute')) {
                                    const currentCount = parseInt(counter.textContent.trim());
                                    if (currentCount > 1) {
                                        counter.textContent = currentCount - 1;
                                    } else {
                                        counter.remove();
                                    }
                                }
                            }, 300);
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting notification:', error);
                    });
                }
            }
        });

        // User menu toggle is now handled in main.js

        // Back to top button
        const backToTopButton = document.getElementById('btn-back-to-top');

        if (backToTopButton) {
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('hidden');
                } else {
                    backToTopButton.classList.add('hidden');
                }
            });

            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
