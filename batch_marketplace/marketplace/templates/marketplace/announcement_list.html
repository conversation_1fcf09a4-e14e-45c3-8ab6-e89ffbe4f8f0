{% extends 'marketplace/base.html' %}

{% block title %}Announcements - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-bullhorn mr-3 text-purple-500"></i>
                Announcements
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Manage news and announcements for all users
            </p>
        </div>
        <div>
            <a href="{% url 'create_announcement' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 focus:ring-4 focus:ring-purple-200 shadow-md transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Create Announcement
            </a>
        </div>
    </div>
</div>

<!-- Announcements List -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-list mr-2 text-purple-500"></i> All Announcements
        </h3>
    </div>
    
    {% if announcements %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for announcement in announcements %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ announcement.title }}</div>
                                <div class="text-sm text-gray-500 truncate max-w-xs">{{ announcement.content|truncatechars:50 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ announcement.created_by.username }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ announcement.created_at|date:"M d, Y" }}</div>
                                <div class="text-sm text-gray-500">{{ announcement.created_at|time:"H:i" }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {% if announcement.priority == 'high' %}bg-red-100 text-red-800
                                    {% elif announcement.priority == 'medium' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                                    {{ announcement.get_priority_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {% if announcement.is_active %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ announcement.is_active|yesno:"Active,Inactive" }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="{% url 'edit_announcement' announcement.id %}" class="text-indigo-600 hover:text-indigo-900">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{% url 'delete_announcement' announcement.id %}" class="text-red-600 hover:text-red-900">
                                        <i class="fas fa-trash"></i> Delete
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="mb-4 text-gray-400">
                <i class="fas fa-bullhorn text-5xl"></i>
            </div>
            <h5 class="text-xl font-semibold text-gray-900 mb-2">No announcements yet</h5>
            <p class="text-gray-500 mb-6">Create your first announcement to inform users about important updates</p>
            <a href="{% url 'create_announcement' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 shadow-md transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Create Announcement
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
