{% extends 'marketplace/base.html' %}

{% block title %}Support Dashboard - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-headset mr-3 text-blue-500"></i>
                Support Dashboard
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Manage tickets and reports from users
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'ticket_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                <i class="fas fa-ticket-alt mr-2"></i> All Tickets
            </a>
            <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-700 bg-purple-100 rounded-lg hover:bg-purple-200 transition-colors">
                <i class="fas fa-bullhorn mr-2"></i> Announcements
            </a>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl shadow-md text-white p-6">
        <div class="flex items-center">
            <div class="mr-4 text-blue-100">
                <i class="fas fa-ticket-alt text-3xl"></i>
            </div>
            <div>
                <h3 class="text-sm font-medium text-blue-100">Open Tickets</h3>
                <p class="text-2xl font-bold">{{ open_tickets.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-xl shadow-md text-white p-6">
        <div class="flex items-center">
            <div class="mr-4 text-yellow-100">
                <i class="fas fa-exclamation-triangle text-3xl"></i>
            </div>
            <div>
                <h3 class="text-sm font-medium text-yellow-100">Open Reports</h3>
                <p class="text-2xl font-bold">{{ open_reports.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-xl shadow-md text-white p-6">
        <div class="flex items-center">
            <div class="mr-4 text-green-100">
                <i class="fas fa-tasks text-3xl"></i>
            </div>
            <div>
                <h3 class="text-sm font-medium text-green-100">Assigned to You</h3>
                <p class="text-2xl font-bold">{{ assigned_tickets.count|add:assigned_reports.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-xl shadow-md text-white p-6">
        <div class="flex items-center">
            <div class="mr-4 text-cyan-100">
                <i class="fas fa-users text-3xl"></i>
            </div>
            <div>
                <h3 class="text-sm font-medium text-cyan-100">Response Rate</h3>
                <p class="text-2xl font-bold">100%</p>
            </div>
        </div>
    </div>
</div>

<!-- Open Tickets and Reports Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Open Tickets -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
        <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-ticket-alt mr-2 text-blue-500"></i> Open Tickets
            </h3>
            <a href="{% url 'ticket_list' %}" class="text-sm font-medium text-blue-600 hover:text-blue-800">
                View All
            </a>
        </div>
        <div>
            {% if open_tickets %}
                <div class="divide-y divide-gray-200">
                    {% for ticket in open_tickets|slice:":5" %}
                        <a href="{% url 'ticket_detail' ticket.id %}" class="block hover:bg-gray-50 transition-colors">
                            <div class="p-4">
                                <div class="flex justify-between items-start mb-1">
                                    <h4 class="text-sm font-medium text-gray-900">{{ ticket.subject }}</h4>
                                    <span class="text-xs text-gray-500">{{ ticket.created_at|date:"M d, Y" }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2 truncate">{{ ticket.description|truncatechars:100 }}</p>
                                <div class="flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                            {{ ticket.get_category_display }}
                                        </span>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full
                                            {% if ticket.priority == 'urgent' %}bg-red-100 text-red-800
                                            {% elif ticket.priority == 'high' %}bg-yellow-100 text-yellow-800
                                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                                            {{ ticket.get_priority_display }}
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500">From: {{ ticket.user.username }}</span>
                                </div>
                            </div>
                        </a>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="mb-4 text-green-500">
                        <i class="fas fa-check-circle text-5xl"></i>
                    </div>
                    <h5 class="text-xl font-semibold text-gray-900 mb-2">No open tickets</h5>
                    <p class="text-gray-500">All tickets have been addressed</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Open Reports -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
        <div class="border-b border-gray-200 px-6 py-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i> Open Reports
            </h3>
        </div>
        <div>
            {% if open_reports %}
                <div class="divide-y divide-gray-200">
                    {% for report in open_reports|slice:":5" %}
                        <a href="{% url 'order_report' report.order_item.id %}" class="block hover:bg-gray-50 transition-colors">
                            <div class="p-4">
                                <div class="flex justify-between items-start mb-1">
                                    <h4 class="text-sm font-medium text-gray-900">Report #{{ report.id }} - {{ report.order_item.product.title }}</h4>
                                    <span class="text-xs text-gray-500">{{ report.created_at|date:"M d, Y" }}</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2 truncate">{{ report.issue|truncatechars:100 }}</p>
                                <div class="flex justify-between items-center">
                                    <div class="flex space-x-2">
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                            Unresolved
                                        </span>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                            Order #{{ report.order_item.order.id }}
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500">
                                        Buyer: {{ report.order_item.order.buyer.username }} |
                                        Seller: {{ report.order_item.product.seller.username }}
                                    </span>
                                </div>
                            </div>
                        </a>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="mb-4 text-green-500">
                        <i class="fas fa-check-circle text-5xl"></i>
                    </div>
                    <h5 class="text-xl font-semibold text-gray-900 mb-2">No open reports</h5>
                    <p class="text-gray-500">All reports have been resolved</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Assigned to You -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-tasks mr-2 text-green-500"></i> Assigned to You
        </h3>
    </div>
    <div>
        {% if assigned_tickets or assigned_reports %}
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject/Product</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for ticket in assigned_tickets %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ ticket.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Ticket</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ ticket.subject }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ticket.user.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {% if ticket.status == 'open' %}bg-yellow-100 text-yellow-800
                                        {% elif ticket.status == 'in_progress' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ ticket.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ticket.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'ticket_detail' ticket.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                                        View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                        {% for report in assigned_reports %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ report.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Report</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ report.order_item.product.title }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ report.created_by.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                        Unresolved
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ report.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'order_report' report.order_item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                                        View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="mb-4 text-green-500">
                    <i class="fas fa-check-circle text-5xl"></i>
                </div>
                <h5 class="text-xl font-semibold text-gray-900 mb-2">Nothing assigned to you</h5>
                <p class="text-gray-500">You have no pending tickets or reports</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
