{% extends 'marketplace/base.html' %}
{% load custom_filters %}

{% block title %}Items - Oleer Market{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-list mr-3 text-emerald-500"></i>
                Browse Items
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Find the perfect item for your needs
            </p>
        </div>
        <div class="w-full md:w-64">
            <div class="relative">
                <input type="text" id="searchProducts" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" placeholder="Search products...">
                <button class="absolute right-2 top-2 text-gray-500 hover:text-emerald-500">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-filter mr-2 text-emerald-500"></i> Filters
        </h3>
        <button class="md:hidden px-3 py-1.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                type="button"
                onclick="document.getElementById('filterCollapse').classList.toggle('hidden')">
            <i class="fas fa-filter mr-1"></i> Show Filters
        </button>
    </div>
    <div class="p-6 md:block" id="filterCollapse">
        <!-- Item Type Card Selection -->
        <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-3">Select Item Type:</label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                <!-- VPS/RDP Card -->
                <div class="item-type-card cursor-pointer rounded-lg border-2 border-blue-500 transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md" data-type="vps">
                    <div class="p-4 flex flex-col items-center text-center">
                        <div class="w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-blue-400 to-indigo-500 text-white mb-3">
                            <i class="fas fa-server text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-1">VPS/RDP</h3>
                        <p class="text-xs text-gray-500">Virtual Private Servers & Remote Desktops</p>
                    </div>
                    <div class="h-1 w-full bg-gradient-to-r from-blue-400 to-indigo-500 type-indicator"></div>
                </div>

                <!-- Accounts Card -->
                <div class="item-type-card cursor-pointer rounded-lg border-2 border-gray-200 hover:border-purple-500 transition-all duration-300 overflow-hidden shadow-sm hover:shadow-md" data-type="account">
                    <div class="p-4 flex flex-col items-center text-center">
                        <div class="w-16 h-16 flex items-center justify-center rounded-full bg-gradient-to-r from-purple-400 to-pink-500 text-white mb-3">
                            <i class="fas fa-user-circle text-2xl"></i>
                        </div>
                        <h3 class="font-semibold text-gray-800 mb-1">Accounts</h3>
                        <p class="text-xs text-gray-500">Premium service accounts</p>
                    </div>
                    <div class="h-1 w-full bg-gradient-to-r from-purple-400 to-pink-500 type-indicator hidden"></div>
                </div>
            </div>
            <!-- Hidden select for compatibility with existing JS -->
            <select id="itemType" class="hidden">
                <option value="vps" selected>VPS/RDP</option>
                <option value="account">Accounts</option>
            </select>
        </div>

        <!-- VPS Filters -->
        <div id="vpsFilters" class="space-y-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Filters:</label>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="sortBy" class="block text-sm font-medium text-gray-700 mb-1">Sort by</label>
                    <select id="sortBy" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="price_asc">Price: Low to High</option>
                        <option value="price_desc">Price: High to Low</option>
                        <option value="ram_asc">RAM: Low to High</option>
                        <option value="ram_desc">RAM: High to Low</option>
                        <option value="newest">Newest First</option>
                    </select>
                </div>
                <div>
                    <label for="ramFilter" class="block text-sm font-medium text-gray-700 mb-1">RAM</label>
                    <select id="ramFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">Any RAM</option>
                        <option value="4GB">4GB or more</option>
                        <option value="8GB">8GB or more</option>
                        <option value="16GB">16GB or more</option>
                        <option value="32GB">32GB or more</option>
                    </select>
                </div>
                <div>
                    <label for="cpuFilter" class="block text-sm font-medium text-gray-700 mb-1">CPU Cores</label>
                    <select id="cpuFilter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="">Any CPU</option>
                        <option value="2">2 cores or more</option>
                        <option value="4">4 cores or more</option>
                        <option value="8">8 cores or more</option>
                    </select>
                </div>
                <div>
                    <label for="priceRangeVPS" class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" id="minPriceVPS" placeholder="Min" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <span>-</span>
                        <input type="number" id="maxPriceVPS" placeholder="Max" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Filters -->
        <div id="accountFilters" class="space-y-4 hidden">
            <label class="block text-sm font-medium text-gray-700 mb-2">Filters:</label>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="sortByAccount" class="block text-sm font-medium text-gray-700 mb-1">Sort by</label>
                    <select id="sortByAccount" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <option value="price_asc">Price: Low to High</option>
                        <option value="price_desc">Price: High to Low</option>
                        <option value="newest">Newest First</option>
                    </select>
                </div>
                <div>
                    <label for="keywordFilter" class="block text-sm font-medium text-gray-700 mb-1">Keywords</label>
                    <input type="text" id="keywordFilter" placeholder="Search in description" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                </div>
                <div>
                    <label for="priceRangeAccount" class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" id="minPriceAccount" placeholder="Min" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                        <span>-</span>
                        <input type="number" id="maxPriceAccount" placeholder="Max" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Marketplace Items -->
{% if products %}
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
        <div class="overflow-x-auto">
            <!-- VPS/RDP Table -->
            <table id="vpsTable" class="w-full">
                <thead class="bg-blue-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">RAM</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Company</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Seller</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-blue-700 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for product in products %}
                        {% if product.item_type == 'vps' %}
                            <tr class="hover:bg-gray-50 vps-row">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ product.title }}
                                    <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800">
                                        VPS/RDP
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <!-- For VPS/RDP, only show RAM specification -->
                                    {% if product.ram %}
                                        <span class="font-medium">{{ product.ram }}</span>
                                    {% else %}
                                        {% with specs=product.specifications.split %}
                                            {% with ram_found=False %}
                                                {% for spec in specs %}
                                                    {% if 'ram' in spec|lower %}
                                                        <span class="font-medium">{{ spec|cut:"RAM:" }}</span>
                                                        {% with ram_found=True %}{% endwith %}
                                                    {% endif %}
                                                {% endfor %}
                                                {% if not ram_found %}
                                                    <span class="text-gray-400">N/A</span>
                                                {% endif %}
                                            {% endwith %}
                                        {% endwith %}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {% with specs=product.specifications.split %}
                                        {% with company_found=False %}
                                            {% for spec in specs %}
                                                {% if 'company' in spec|lower %}
                                                    <span class="font-medium">{{ spec|cut:"Company:" }}</span>
                                                    {% with company_found=True %}{% endwith %}
                                                {% endif %}
                                            {% endfor %}
                                            {% if not company_found %}
                                                <span class="text-gray-400">N/A</span>
                                            {% endif %}
                                        {% endwith %}
                                    {% endwith %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${{ product.price }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.seller.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    {% if user.is_authenticated and user.profile.role == 'buyer' %}
                                        <a href="{% url 'create_order' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                            <i class="fas fa-shopping-cart mr-1"></i> Buy
                                        </a>
                                    {% elif user.is_authenticated and user.profile.role == 'seller' %}
                                        {% if product.is_own %}
                                            <a href="{% url 'edit_vps_product' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg hover:from-gray-600 hover:to-gray-700 shadow-sm transition-all duration-300">
                                                <i class="fas fa-edit mr-1"></i> Edit
                                            </a>
                                        {% else %}
                                            <a href="{% url 'create_order' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                                <i class="fas fa-shopping-cart mr-1"></i> Buy
                                            </a>
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                </tbody>
            </table>

            <!-- Account Table -->
            <table id="accountTable" class="w-full hidden">
                <thead class="bg-purple-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Title</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Description</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Proof</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Seller</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-purple-700 uppercase tracking-wider">Action</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for product in products %}
                        {% if product.item_type == 'account' %}
                            <tr class="hover:bg-gray-50 account-row">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ product.title }}
                                    <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-purple-100 text-purple-800">
                                        Account
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="text-gray-500">
                                        <i class="fas fa-info-circle mr-1"></i>
                                        {% if product.proof|length > 30 %}
                                            {{ product.proof|truncatechars:30 }}
                                            <span class="text-xs text-purple-500 hover:underline cursor-pointer"
                                                  onclick="showDescriptionModal('{{ product.title|escapejs }}', '{{ product.proof|escapejs }}')">Read more</span>
                                        {% else %}
                                            {{ product.proof }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <!-- Check for proof link in the specifications -->
                                    {% if 'Proof Link:' in product.specifications %}
                                        <!-- Extract and display proof link URL -->
                                        {% with proof_url=product.specifications|extract_url %}
                                            <button
                                                type="button"
                                                class="inline-flex items-center px-2 py-1 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-lg hover:bg-indigo-100 hover:text-indigo-700 shadow-sm transition-all duration-300"
                                                onclick="window.open('{{ proof_url }}', '_blank', 'width=800,height=600')">
                                                <i class="fas fa-image"></i>
                                            </button>
                                        {% endwith %}
                                    {% else %}
                                        <!-- No proof available -->
                                        <span class="text-gray-400 text-xs">-</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-purple-600">${{ product.price }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.seller.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    {% if user.is_authenticated and user.profile.role == 'buyer' %}
                                        <a href="{% url 'create_order' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg hover:from-purple-600 hover:to-pink-700 shadow-sm transition-all duration-300">
                                            <i class="fas fa-shopping-cart mr-1"></i> Buy
                                        </a>
                                    {% elif user.is_authenticated and user.profile.role == 'seller' %}
                                        {% if product.is_own %}
                                            <a href="{% url 'edit_vps_product' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-gray-500 to-gray-600 rounded-lg hover:from-gray-600 hover:to-gray-700 shadow-sm transition-all duration-300">
                                                <i class="fas fa-edit mr-1"></i> Edit
                                            </a>
                                        {% else %}
                                            <a href="{% url 'create_order' product.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg hover:from-purple-600 hover:to-pink-700 shadow-sm transition-all duration-300">
                                                <i class="fas fa-shopping-cart mr-1"></i> Buy
                                            </a>
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% else %}
    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-10 text-center mb-6">
        <h4 class="text-xl font-semibold text-gray-900 mb-2">No items available at the moment</h4>
        <p class="text-gray-500 mb-6">Please check back later for new listings</p>

        {% if user.is_authenticated and user.profile.role == 'seller' %}
            <a href="{% url 'add_vps_product' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-md transition-all duration-300">
                <i class="fas fa-plus-circle mr-2"></i> Add Your First Item
            </a>
        {% endif %}
    </div>
{% endif %}

<!-- Pagination -->
{% if products and products.paginator.num_pages > 1 %}
    <div class="flex justify-center mt-6">
        <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            {% if products.has_previous %}
                <a href="?page=1" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">First</span>
                    <i class="fas fa-angle-double-left"></i>
                </a>
                <a href="?page={{ products.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Previous</span>
                    <i class="fas fa-angle-left"></i>
                </a>
            {% else %}
                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                    <span class="sr-only">First</span>
                    <i class="fas fa-angle-double-left"></i>
                </span>
                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                    <span class="sr-only">Previous</span>
                    <i class="fas fa-angle-left"></i>
                </span>
            {% endif %}

            {% for num in products.paginator.page_range %}
                {% if products.number == num %}
                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-emerald-50 text-sm font-medium text-emerald-600">
                        {{ num }}
                    </span>
                {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                    <a href="?page={{ num }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                        {{ num }}
                    </a>
                {% endif %}
            {% endfor %}

            {% if products.has_next %}
                <a href="?page={{ products.next_page_number }}" class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Next</span>
                    <i class="fas fa-angle-right"></i>
                </a>
                <a href="?page={{ products.paginator.num_pages }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Last</span>
                    <i class="fas fa-angle-double-right"></i>
                </a>
            {% else %}
                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                    <span class="sr-only">Next</span>
                    <i class="fas fa-angle-right"></i>
                </span>
                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                    <span class="sr-only">Last</span>
                    <i class="fas fa-angle-double-right"></i>
                </span>
            {% endif %}
        </nav>
    </div>
{% endif %}

<!-- Product Search and Table JS -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Item type card selection functionality
        const itemTypeCards = document.querySelectorAll('.item-type-card');
        const itemTypeSelect = document.getElementById('itemType');
        const vpsTable = document.getElementById('vpsTable');
        const accountTable = document.getElementById('accountTable');

        // Get filter elements
        const vpsFilters = document.getElementById('vpsFilters');
        const accountFilters = document.getElementById('accountFilters');

        // Function to update tables based on selected item type
        function updateTableVisibility(selectedType) {
            // Update hidden select value for compatibility with existing code
            itemTypeSelect.value = selectedType;

            // Show/hide tables based on selected type
            if (selectedType === 'vps') {
                vpsTable.classList.remove('hidden');
                accountTable.classList.add('hidden');
                vpsFilters.classList.remove('hidden');
                accountFilters.classList.add('hidden');
            } else if (selectedType === 'account') {
                vpsTable.classList.add('hidden');
                accountTable.classList.remove('hidden');
                vpsFilters.classList.add('hidden');
                accountFilters.classList.remove('hidden');
            }

            // Update card styling
            itemTypeCards.forEach(card => {
                const cardType = card.getAttribute('data-type');
                const indicator = card.querySelector('.type-indicator');

                if (cardType === selectedType) {
                    // Selected card
                    if (cardType === 'vps') {
                        card.classList.add('border-blue-500');
                        card.classList.remove('border-gray-200', 'border-purple-500');
                    } else if (cardType === 'account') {
                        card.classList.add('border-purple-500');
                        card.classList.remove('border-gray-200', 'border-blue-500');
                    }

                    // Show indicator
                    indicator.classList.remove('hidden');
                } else {
                    // Unselected card
                    card.classList.add('border-gray-200');
                    card.classList.remove('border-blue-500', 'border-purple-500');

                    // Hide indicator
                    indicator.classList.add('hidden');
                }
            });
        }

        // Set initial state - show VPS table by default
        updateTableVisibility('vps');

        // Add click event listeners to item type cards
        itemTypeCards.forEach(card => {
            card.addEventListener('click', function() {
                const selectedType = this.getAttribute('data-type');
                updateTableVisibility(selectedType);
            });
        });

        // Get filter elements
        const searchInput = document.getElementById('searchProducts');
        const ramFilter = document.getElementById('ramFilter');
        const cpuFilter = document.getElementById('cpuFilter');
        const minPriceVPS = document.getElementById('minPriceVPS');
        const maxPriceVPS = document.getElementById('maxPriceVPS');
        const keywordFilter = document.getElementById('keywordFilter');
        const minPriceAccount = document.getElementById('minPriceAccount');
        const maxPriceAccount = document.getElementById('maxPriceAccount');
        const sortByVPS = document.getElementById('sortBy');
        const sortByAccount = document.getElementById('sortByAccount');

        // Function to extract numeric value from RAM string (e.g., "8GB", "8 GB", "8G" -> 8)
        function extractRAMValue(ramString) {
            if (!ramString || ramString.trim() === 'N/A') {
                return 0;
            }

            // Handle different RAM formats
            const cleanedString = ramString.trim().toLowerCase();

            // Try to match patterns like "8GB", "8 GB", "8G"
            const match = cleanedString.match(/(\d+)\s*g[b]?/i);
            if (match) {
                return parseInt(match[1]);
            }

            // Fallback to any number in the string
            const numericMatch = cleanedString.match(/(\d+)/);
            return numericMatch ? parseInt(numericMatch[1]) : 0;
        }

        // Function to extract CPU cores from string (e.g., "4 cores" -> 4)
        function extractCPUValue(cpuString) {
            const match = cpuString.match(/(\d+)/);
            return match ? parseInt(match[1]) : 0;
        }

        // Function to filter VPS rows
        function filterVPSRows() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            const ramValue = ramFilter.value;
            const cpuValue = cpuFilter.value;
            const minPrice = minPriceVPS.value ? parseFloat(minPriceVPS.value) : 0;
            const maxPrice = maxPriceVPS.value ? parseFloat(maxPriceVPS.value) : Infinity;

            const vpsRows = vpsTable.querySelectorAll('tbody tr');

            vpsRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const matchesSearch = searchTerm === '' || text.includes(searchTerm);

                // Get RAM value from the row (second column)
                const ramCell = row.querySelector('td:nth-child(2)');
                const ramText = ramCell ? ramCell.textContent.trim() : '';
                const rowRamValue = extractRAMValue(ramText);

                // Extract the minimum RAM value from the filter (e.g., "4GB" -> 4)
                let minRamValue = 0;
                if (ramValue) {
                    minRamValue = extractRAMValue(ramValue);
                }

                const matchesRAM = ramValue === '' || (rowRamValue >= minRamValue);

                // Get CPU value from specifications (not directly visible in the table)
                const matchesCPU = cpuValue === '' || text.includes(`${cpuValue} core`) || text.includes(`${cpuValue}core`);

                // Get price from the row (fourth column)
                const priceCell = row.querySelector('td:nth-child(4)');
                const priceText = priceCell ? priceCell.textContent.replace('$', '').trim() : '0';
                const rowPrice = parseFloat(priceText);
                const matchesPrice = (rowPrice >= minPrice && rowPrice <= maxPrice);

                if (matchesSearch && matchesRAM && matchesCPU && matchesPrice) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Function to filter Account rows
        function filterAccountRows() {
            const searchTerm = searchInput.value.toLowerCase().trim();
            const keyword = keywordFilter.value.toLowerCase().trim();
            const minPrice = minPriceAccount.value ? parseFloat(minPriceAccount.value) : 0;
            const maxPrice = maxPriceAccount.value ? parseFloat(maxPriceAccount.value) : Infinity;

            const accountRows = accountTable.querySelectorAll('tbody tr');

            accountRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const matchesSearch = searchTerm === '' || text.includes(searchTerm);

                // Check if description contains keyword
                const descriptionCell = row.querySelector('td:nth-child(2)');
                const descriptionText = descriptionCell ? descriptionCell.textContent.toLowerCase() : '';
                const matchesKeyword = keyword === '' || descriptionText.includes(keyword);

                // Get price from the row (fourth column)
                const priceCell = row.querySelector('td:nth-child(4)');
                const priceText = priceCell ? priceCell.textContent.replace('$', '').trim() : '0';
                const rowPrice = parseFloat(priceText);
                const matchesPrice = (rowPrice >= minPrice && rowPrice <= maxPrice);

                if (matchesSearch && matchesKeyword && matchesPrice) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Add event listeners to all filter elements
        searchInput.addEventListener('keyup', function() {
            const selectedType = itemTypeSelect.value;
            if (selectedType === 'vps') {
                filterVPSRows();
            } else if (selectedType === 'account') {
                filterAccountRows();
            }
        });

        // Function to sort table rows
        function sortTableRows(table, columnIndex, ascending, customSortFn = null) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            // Sort rows based on the content of the specified column
            rows.sort((a, b) => {
                const aValue = a.querySelector(`td:nth-child(${columnIndex})`).textContent.trim();
                const bValue = b.querySelector(`td:nth-child(${columnIndex})`).textContent.trim();

                // Use custom sort function if provided
                if (customSortFn) {
                    return customSortFn(aValue, bValue, ascending);
                }

                // Handle price values (remove $ sign)
                if (aValue.includes('$') && bValue.includes('$')) {
                    const aPrice = parseFloat(aValue.replace('$', ''));
                    const bPrice = parseFloat(bValue.replace('$', ''));
                    return ascending ? aPrice - bPrice : bPrice - aPrice;
                }

                // Regular string comparison
                return ascending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            });

            // Re-append rows in the new order
            rows.forEach(row => tbody.appendChild(row));
        }

        // Function to sort by RAM values
        function sortByRAM(aValue, bValue, ascending) {
            const aRAM = extractRAMValue(aValue);
            const bRAM = extractRAMValue(bValue);
            return ascending ? aRAM - bRAM : bRAM - aRAM;
        }

        // Function to handle sorting for VPS table
        function handleVPSSort() {
            const sortValue = sortByVPS.value;

            if (sortValue === 'price_asc') {
                sortTableRows(vpsTable, 4, true); // 4th column is price
            } else if (sortValue === 'price_desc') {
                sortTableRows(vpsTable, 4, false);
            } else if (sortValue === 'ram_asc') {
                sortTableRows(vpsTable, 2, true, sortByRAM); // 2nd column is RAM, use custom sort function
            } else if (sortValue === 'ram_desc') {
                sortTableRows(vpsTable, 2, false, sortByRAM);
            } else if (sortValue === 'newest') {
                // For newest, we don't have a direct column, so we'll just refresh the page with a query parameter
                window.location.href = '?sort=newest';
            }
        }

        // Function to handle sorting for Account table
        function handleAccountSort() {
            const sortValue = sortByAccount.value;

            if (sortValue === 'price_asc') {
                sortTableRows(accountTable, 4, true); // 4th column is price
            } else if (sortValue === 'price_desc') {
                sortTableRows(accountTable, 4, false);
            } else if (sortValue === 'newest') {
                // For newest, we don't have a direct column, so we'll just refresh the page with a query parameter
                window.location.href = '?sort=newest';
            }
        }

        // VPS filter event listeners
        ramFilter.addEventListener('change', filterVPSRows);
        cpuFilter.addEventListener('change', filterVPSRows);
        minPriceVPS.addEventListener('input', filterVPSRows);
        maxPriceVPS.addEventListener('input', filterVPSRows);
        sortByVPS.addEventListener('change', handleVPSSort);

        // Account filter event listeners
        keywordFilter.addEventListener('keyup', filterAccountRows);
        minPriceAccount.addEventListener('input', filterAccountRows);
        maxPriceAccount.addEventListener('input', filterAccountRows);
        sortByAccount.addEventListener('change', handleAccountSort);

        // Password toggle functionality
        const toggleButtons = document.querySelectorAll('.toggle-password');

        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const passwordField = this.closest('div').querySelector('.password-field');
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);

                // Toggle the eye icon
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        });

        // Description modal functionality
        window.showDescriptionModal = function(title, description) {
            const modal = document.getElementById('descriptionModal');
            const modalTitle = document.getElementById('descriptionModalTitle');
            const modalContent = document.getElementById('descriptionModalContent');

            modalTitle.textContent = title;
            modalContent.textContent = description;

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        // Image modal functionality
        window.showImageModal = function(title, imageUrl) {
            console.log("Opening image modal with URL:", imageUrl); // Debug log

            const modal = document.getElementById('imageModal');
            const modalTitle = document.getElementById('imageModalTitle');
            const modalContent = document.getElementById('imageModalContent');
            const modalIframe = document.getElementById('imageModalIframe');
            const modalImage = document.getElementById('imageModalImage');

            // Clean up the URL if needed
            imageUrl = imageUrl.trim();
            if (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://')) {
                imageUrl = 'https://' + imageUrl;
            }

            modalTitle.textContent = title + ' - Proof Screenshot';

            // Determine if we should use iframe or img based on URL
            const isImageUrl = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(imageUrl);
            const isImageHostingService = /(imgur|prnt\.sc|prntscr|lightshot|gyazo|ibb\.co)/i.test(imageUrl);

            if (isImageUrl) {
                // Direct image URL - use img tag
                modalImage.src = imageUrl;
                modalImage.alt = title + ' Proof';
                modalImage.classList.remove('hidden');
                modalIframe.classList.add('hidden');

                // Show loading indicator
                modalImage.classList.add('opacity-50');

                // When image loads, remove loading indicator
                modalImage.onload = function() {
                    modalImage.classList.remove('opacity-50');
                };

                // If image fails to load
                modalImage.onerror = function() {
                    modalImage.src = 'https://via.placeholder.com/800x600?text=Image+Not+Found';
                    modalImage.classList.remove('opacity-50');
                };
            } else {
                // Not a direct image URL - use iframe
                modalIframe.src = imageUrl;
                modalIframe.classList.remove('hidden');
                modalImage.classList.add('hidden');
            }

            modal.classList.remove('hidden');
            document.body.classList.add('overflow-hidden');
        }

        // Close description modal when clicking the close button
        document.getElementById('closeDescriptionModal').addEventListener('click', function() {
            document.getElementById('descriptionModal').classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });

        // Close description modal when clicking outside of it
        document.getElementById('descriptionModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                document.getElementById('descriptionModal').classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        });

        // Close image modal when clicking the close button
        document.getElementById('closeImageModal').addEventListener('click', function() {
            document.getElementById('imageModal').classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });

        // Close image modal when clicking outside of it
        document.getElementById('imageModalOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                document.getElementById('imageModal').classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        });

        // Close modals with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (!document.getElementById('descriptionModal').classList.contains('hidden')) {
                    document.getElementById('descriptionModal').classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
                if (!document.getElementById('imageModal').classList.contains('hidden')) {
                    document.getElementById('imageModal').classList.add('hidden');
                    document.body.classList.remove('overflow-hidden');
                }
            }
        });
    });
</script>

<!-- Description Modal -->
<div id="descriptionModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div id="descriptionModalOverlay" class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-auto z-10 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="descriptionModalTitle" class="text-lg font-semibold text-gray-900"></h3>
                <button id="closeDescriptionModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="text-gray-700">
                    <h4 class="font-medium text-gray-900 mb-2">Account Description</h4>
                    <p id="descriptionModalContent" class="whitespace-pre-line"></p>
                </div>
            </div>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                <button id="closeDescriptionModalBtn" onclick="document.getElementById('descriptionModal').classList.add('hidden'); document.body.classList.remove('overflow-hidden');"
                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div id="imageModalOverlay" class="fixed inset-0 bg-black bg-opacity-75 transition-opacity"></div>
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-auto z-10 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="imageModalTitle" class="text-lg font-semibold text-gray-900"></h3>
                <button id="closeImageModal" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6 flex justify-center">
                <div class="text-gray-700 max-w-full">
                    <div class="bg-gray-100 p-2 rounded-lg">
                        <!-- Image for direct image URLs -->
                        <img id="imageModalImage" class="max-w-full h-auto rounded shadow-lg" src="" alt="Proof Screenshot">

                        <!-- iFrame for non-image URLs (like prnt.sc, imgur, etc.) -->
                        <iframe id="imageModalIframe" class="w-full h-[600px] rounded shadow-lg hidden" src="" frameborder="0" allowfullscreen></iframe>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                <button id="closeImageModalBtn" onclick="document.getElementById('imageModal').classList.add('hidden'); document.body.classList.remove('overflow-hidden');"
                    class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
