{% extends 'marketplace/base.html' %}

{% block title %}Create Support Ticket - Batch VPS Marketplace{% endblock %}

{% block content %}
<div class="p-4 sm:p-6 lg:p-8 bg-gray-50">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-emerald-600 hover:text-emerald-700">
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <a href="{% url 'ticket_list' %}" class="text-emerald-600 hover:text-emerald-700">Support Tickets</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="text-gray-500">Create Ticket</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto">
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- Form Section -->
            <div class="w-full lg:w-2/3">
                <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                    <!-- Header -->
                    <div class="border-b border-gray-200 p-4 bg-gradient-to-r from-blue-500 to-blue-600">
                        <h1 class="text-xl font-semibold text-white flex items-center">
                            <i class="fas fa-plus-circle mr-2"></i>
                            Create New Support Ticket
                        </h1>
                    </div>

                    <!-- Form -->
                    <div class="p-6">
                        <form method="post">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="mb-6 p-4 rounded-lg bg-red-50 text-red-800 border border-red-200">
                                    {% for error in form.non_field_errors %}
                                        <p>{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            {% if is_support %}
                                <!-- User Selection Field (Only for Support Staff) -->
                                <div class="mb-4">
                                    <label for="{{ form.user.id_for_label }}" class="block text-gray-700 font-medium mb-2">Create Ticket For User</label>
                                    <div class="relative">
                                        {{ form.user.as_widget|safe }}
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                                            <i class="fas fa-chevron-down"></i>
                                        </div>
                                    </div>
                                    {% if form.user.errors %}
                                        <div class="mt-1 text-sm text-red-600">
                                            {% for error in form.user.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <p class="mt-1 text-sm text-gray-500">
                                        Select the user you want to create this ticket for
                                    </p>
                                </div>
                            {% endif %}

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <!-- Subject Field -->
                                <div>
                                    <label for="{{ form.subject.id_for_label }}" class="block text-gray-700 font-medium mb-2">Subject</label>
                                    {{ form.subject.as_widget|safe }}
                                    {% if form.subject.errors %}
                                        <div class="mt-1 text-sm text-red-600">
                                            {% for error in form.subject.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Category Field -->
                                <div>
                                    <label for="{{ form.category.id_for_label }}" class="block text-gray-700 font-medium mb-2">Category</label>
                                    <div class="relative">
                                        {{ form.category.as_widget|safe }}
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                                            <i class="fas fa-chevron-down"></i>
                                        </div>
                                    </div>
                                    {% if form.category.errors %}
                                        <div class="mt-1 text-sm text-red-600">
                                            {% for error in form.category.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Description Field -->
                            <div class="mb-4">
                                <label for="{{ form.description.id_for_label }}" class="block text-gray-700 font-medium mb-2">Description</label>
                                {{ form.description.as_widget|safe }}
                                {% if form.description.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {% for error in form.description.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <p class="mt-2 text-sm text-gray-500">
                                    Please provide as much detail as possible to help us assist you better.
                                </p>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex justify-end space-x-4 mt-6">
                                <a href="{% url 'ticket_list' %}" class="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-200 transition-colors duration-200">
                                    Cancel
                                </a>
                                <button type="submit" class="px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300 shadow-sm transition-all duration-200">
                                    <i class="fas fa-paper-plane mr-2"></i>Submit Ticket
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Guide Section -->
            <div class="w-full lg:w-1/3">
                <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                    <div class="border-b border-gray-200 p-4 bg-gradient-to-r from-blue-500 to-blue-600">
                        <h2 class="text-lg font-semibold text-white flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            Ticket Guidelines
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mt-0.5">
                                    <span class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">1</span>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-gray-900">Choose a clear subject</h3>
                                    <p class="mt-1 text-sm text-gray-600">Be specific about your issue to help us route your ticket to the right team.</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mt-0.5">
                                    <span class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">2</span>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-gray-900">Select the right category</h3>
                                    <p class="mt-1 text-sm text-gray-600">This helps us prioritize and assign your ticket appropriately.</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mt-0.5">
                                    <span class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">3</span>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-gray-900">Provide detailed information</h3>
                                    <p class="mt-1 text-sm text-gray-600">Include any relevant details such as order numbers, error messages, or steps to reproduce the issue.</p>
                                </div>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mt-0.5">
                                    <span class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">4</span>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-gray-900">Response time</h3>
                                    <p class="mt-1 text-sm text-gray-600">Our support team typically responds within 24 hours during business days.</p>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-100">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-lightbulb text-blue-600"></i>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-blue-800">Tip</h3>
                                    <p class="mt-1 text-sm text-blue-700">Check our <a href="#" class="font-medium underline">FAQ section</a> first to see if your question has already been answered.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
