{% extends 'marketplace/base.html' %}

{% block title %}My Profile - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-user-circle mr-3 text-blue-500"></i>
                My Profile
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                View and manage your account information
            </p>
        </div>
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{% url 'index' %}" class="text-gray-700 hover:text-blue-600">
                        <i class="fas fa-home mr-1"></i> Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                        <span class="text-gray-500">My Profile</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
    <!-- Profile Information -->
    <div class="lg:col-span-1">
        <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden h-full">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-user-circle mr-2 text-blue-500"></i> Profile Information
                </h3>
                <a href="{% url 'edit_profile' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                    <i class="fas fa-edit mr-1"></i> Edit
                </a>
            </div>
            <div class="p-6">
                <div class="text-center mb-6">
                    <div class="mx-auto mb-4 text-blue-500">
                        <i class="fas fa-user-circle text-6xl"></i>
                    </div>
                    <h4 class="text-xl font-medium text-gray-900 mb-1">{{ user.username }}</h4>
                    <p class="text-gray-500">{{ profile.get_role_display }}</p>
                </div>

                <div class="mb-5">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Email</h5>
                    <p class="text-gray-900">{{ user.email }}</p>
                </div>

                <div class="mb-5">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Password</h5>
                    <p class="text-gray-900 mb-2">••••••••••</p>
                    <a href="{% url 'edit_profile' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                        <i class="fas fa-key mr-1"></i> Change Password
                    </a>
                </div>

                <div class="mb-5">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Account Created</h5>
                    <p class="text-gray-900">{{ user.date_joined|date:"F j, Y" }}</p>
                </div>

                {% if profile.role == 'buyer' or profile.role == 'seller' %}
                <div class="mb-5">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Wallet Address</h5>
                    <p class="text-gray-900 break-all">{{ profile.wallet_address|default:"Not provided" }}</p>
                </div>
                {% endif %}

                {% if profile.role == 'buyer' %}
                <div class="mb-5">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Current Balance</h5>
                    <p class="text-emerald-600 font-bold text-lg mb-2">${{ profile.balance }}</p>
                    <a href="{% url 'topup_balance' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-sm transition-all duration-300">
                        <i class="fas fa-wallet mr-1"></i> Top Up Balance
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="lg:col-span-3">
        <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
            <div class="border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-history mr-2 text-blue-500"></i> Recent Activity
                </h3>
            </div>
            <div class="p-6">
                <div class="border-b border-gray-200">
                    <nav class="flex flex-wrap -mb-px" aria-label="Tabs">
                        {% if profile.role == 'buyer' %}
                        <button class="tab-button text-blue-600 border-b-2 border-blue-600 py-3 px-4 text-sm font-medium active"
                                id="orders-tab"
                                data-tab="orders"
                                aria-selected="true">
                            <i class="fas fa-shopping-cart mr-1"></i> Orders
                        </button>
                        {% endif %}

                        {% if profile.role == 'seller' %}
                        <button class="tab-button {% if profile.role == 'seller' %}text-blue-600 border-b-2 border-blue-600 active{% else %}text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300{% endif %} py-3 px-4 text-sm font-medium"
                                id="products-tab"
                                data-tab="products"
                                aria-selected="{% if profile.role == 'seller' %}true{% else %}false{% endif %}">
                            <i class="fas fa-list mr-1"></i> Items
                        </button>
                        {% endif %}

                        <button class="tab-button {% if profile.role != 'buyer' and profile.role != 'seller' %}text-blue-600 border-b-2 border-blue-600 active{% else %}text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300{% endif %} py-3 px-4 text-sm font-medium"
                                id="tickets-tab"
                                data-tab="tickets"
                                aria-selected="{% if profile.role != 'buyer' and profile.role != 'seller' %}true{% else %}false{% endif %}">
                            <i class="fas fa-ticket-alt mr-1"></i> Tickets
                        </button>

                        <button class="tab-button text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300 py-3 px-4 text-sm font-medium"
                                id="reports-tab"
                                data-tab="reports"
                                aria-selected="false">
                            <i class="fas fa-flag mr-1"></i> Reports
                        </button>
                    </nav>
                </div>

                <div class="tab-content mt-6">
                    <!-- Orders Tab -->
                    {% if profile.role == 'buyer' %}
                    <div class="tab-pane active" id="orders-content" style="display: block;">
                        {% if recent_orders %}
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        {% for order in recent_orders %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ order.created_at|date:"M d, Y" }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${{ order.total_amount }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                {% if order.order_status == 'paid' %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Paid</span>
                                                {% else %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">{{ order.order_status }}</span>
                                                {% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <button class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors view-order-details" data-order-id="{{ order.id }}">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="flex justify-end mt-6">
                                <a href="{% url 'buyer_dashboard' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                    <i class="fas fa-list mr-2"></i> View All Orders
                                </a>
                            </div>
                        {% else %}
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            You haven't placed any orders yet.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Products Tab (for sellers) -->
                    {% if profile.role == 'seller' %}
                    <div class="tab-pane {% if profile.role == 'seller' %}active{% endif %}" id="products-content" style="display: {% if profile.role == 'seller' %}block{% else %}none{% endif %};">
                        {% if products %}
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Added</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        {% for product in products %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <a href="{% url 'vps_detail' product.id %}" class="text-blue-600 hover:text-blue-800">{{ product.title }}</a>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${{ product.price }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.get_category_display }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.created_at|date:"M d, Y" }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                {% if product.is_sold %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Sold</span>
                                                {% else %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">Available</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="flex justify-end mt-6">
                                <a href="{% url 'seller_dashboard' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                    <i class="fas fa-list mr-2"></i> View All Items
                                </a>
                            </div>
                        {% else %}
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            You haven't added any items yet.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Tickets Tab -->
                    <div class="tab-pane {% if profile.role != 'buyer' and profile.role != 'seller' %}active{% endif %}" id="tickets-content" style="display: {% if profile.role != 'buyer' and profile.role != 'seller' %}block{% else %}none{% endif %};">
                        {% if recent_tickets %}
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ticket ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        {% for ticket in recent_tickets %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ ticket.id }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ ticket.subject }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ticket.created_at|date:"M d, Y" }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                {% if ticket.status == 'open' %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">{{ ticket.get_status_display }}</span>
                                                {% elif ticket.status == 'in_progress' %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{{ ticket.get_status_display }}</span>
                                                {% else %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">{{ ticket.get_status_display }}</span>
                                                {% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <a href="{% url 'ticket_detail' ticket.id %}" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="flex justify-end mt-6">
                                <a href="{% url 'ticket_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                    <i class="fas fa-list mr-2"></i> View All Tickets
                                </a>
                            </div>
                        {% else %}
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            You haven't created any support tickets yet.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Reports Tab -->
                    <div class="tab-pane" id="reports-content" style="display: none;">
                        {% if recent_reports %}
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report ID</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        {% for report in recent_reports %}
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ report.id }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ report.order_item.product.title }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ report.created_at|date:"M d, Y" }}</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                {% if report.resolved %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">Resolved</span>
                                                {% else %}
                                                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">Open</span>
                                                {% endif %}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                                <a href="{% url 'order_report' report.order_item.id %}" class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <div class="flex justify-end mt-6">
                                <a href="{% url 'user_reports' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300">
                                    <i class="fas fa-list mr-2"></i> View All Reports
                                </a>
                            </div>
                        {% else %}
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            You haven't created any reports yet.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Tab Functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', 'text-blue-600', 'border-blue-600');
                    btn.classList.add('text-gray-500', 'hover:text-gray-700', 'border-transparent', 'hover:border-gray-300');
                });

                // Add active class to clicked button
                this.classList.add('active', 'text-blue-600', 'border-blue-600');
                this.classList.remove('text-gray-500', 'hover:text-gray-700', 'border-transparent', 'hover:border-gray-300');

                // Hide all tab panes
                tabPanes.forEach(pane => {
                    pane.classList.remove('active');
                    pane.style.display = 'none';
                });

                // Show the selected tab pane
                const tabId = this.getAttribute('data-tab');
                const tabPane = document.getElementById(tabId + '-content');
                if (tabPane) {
                    tabPane.classList.add('active');
                    tabPane.style.display = 'block';
                }
            });
        });

        // Tab panes are already initialized with inline styles
    });
</script>
{% endblock %}
