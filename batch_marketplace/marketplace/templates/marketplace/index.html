{% extends 'marketplace/base.html' %}

{% block title %}Oleer Market - Buy and Sell Services{% endblock %}

{% block content %}
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row items-center">
                <div class="lg:w-1/2 mb-8 lg:mb-0">
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">Buy and Sell Services with Cryptocurrency</h1>
                    <p class="text-xl mb-8 opacity-90">Oleer Market is a secure marketplace connecting buyers and sellers with USDT TRC-20 payments.</p>
                    <div class="flex flex-wrap gap-4">
                        <a href="{% url 'vps_list' %}" class="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-lg shadow-md hover:bg-gray-100 transition duration-300">
                            <i class="fas fa-shopping-cart mr-2"></i>Browse Items
                        </a>
                        {% if not user.is_authenticated %}
                            <a href="{% url 'register' %}" class="inline-flex items-center px-6 py-3 border-2 border-white text-white rounded-lg hover:bg-white hover:text-blue-700 transition duration-300">
                                <i class="fas fa-user-plus mr-2"></i>Join Now
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="lg:w-1/2 hidden lg:block">
                    <img src="https://pixabay.com/get/g3432c4ea859eeab96c0cb306ceb388207eb275826d6b55c2dd5b621f3ae32f72810582b361e42f33c103682def6a77385f3cab08a10202c47003417c5836c52e_1280.jpg" alt="Server Rack" class="rounded-lg shadow-xl w-full">
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Products -->
    <section class="py-16 px-4">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-10">Featured Products</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% if products %}
                    {% for product in products %}
                        <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
                            <img src="{{ product.image_url|default:'https://pixabay.com/get/g0a7458aaeacad968aa01c6d9a4d35f0a9a27b215ec74b29b787e34e23943bce66b891b182bcc76a198973a60483c28fc3a4be6afeda35b4ccd117dfad5a3f55d_1280.jpg' }}"
                                class="w-full h-48 object-cover" alt="{{ product.title }}">
                            <div class="p-6 flex flex-col h-56">
                                <h3 class="text-xl font-semibold mb-2">{{ product.title }}</h3>
                                <p class="text-gray-600 flex-grow">{{ product.description|truncatewords:15 }}</p>
                                <div class="flex justify-between items-center mt-4">
                                    <span class="text-xl font-bold text-blue-600">${{ product.price }}</span>
                                    <a href="{% url 'vps_detail' product.id %}" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="col-span-full text-center py-10">
                        <p class="text-gray-600 mb-4">No products available at the moment.</p>
                        {% if user.is_authenticated and user.profile.role == 'seller' %}
                            <a href="{% url 'add_vps_product' %}" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300">
                                Add Your First Product
                            </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
            <div class="text-center mt-10">
                <a href="{% url 'vps_list' %}" class="px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg hover:bg-blue-600 hover:text-white transition duration-300">
                    View All Products
                </a>
            </div>
        </div>
    </section>

    <!-- How It Works -->
    <section class="bg-gray-50 py-16">
        <div class="container mx-auto px-4">
            <h2 class="text-3xl font-bold text-center mb-12">How Oleer Market Works</h2>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-search text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Find the Right Product</h3>
                    <p class="text-gray-600">Browse our marketplace to find solutions that match your requirements and budget.</p>
                </div>

                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-wallet text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Pay with Crypto</h3>
                    <p class="text-gray-600">Secure payments using USDT TRC-20 cryptocurrency with automatic verification.</p>
                </div>

                <div class="bg-white rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300">
                    <div class="mb-6">
                        <i class="fas fa-server text-5xl text-blue-500"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3">Get Your Product</h3>
                    <p class="text-gray-600">After verification, the seller delivers your product with all necessary access credentials.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Features -->
    <section class="py-16 px-4">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold text-center mb-12">Why Choose Oleer Market?</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <i class="fas fa-lock text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Secure Payments</h3>
                    <p class="text-gray-600">All transactions use USDT TRC-20 with verification through TronScan.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-comment text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Direct Communication</h3>
                    <p class="text-gray-600">Built-in chat system for direct communication with sellers.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-shield-alt text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Dispute Resolution</h3>
                    <p class="text-gray-600">Admin support for resolving issues and handling refund requests.</p>
                </div>

                <div class="text-center">
                    <i class="fas fa-clipboard-list text-5xl text-blue-500 mb-4"></i>
                    <h3 class="text-xl font-semibold mb-3">Transparent History</h3>
                    <p class="text-gray-600">Complete order history and status tracking for all transactions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Join Now CTA -->
    <section class="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p class="text-xl mb-8 max-w-3xl mx-auto">Join our community of buyers and sellers today.</p>

            {% if not user.is_authenticated %}
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{% url 'register' %}?role=buyer" class="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-lg shadow-md hover:bg-gray-100 transition duration-300">
                        <i class="fas fa-user mr-2"></i>Join as Buyer
                    </a>
                    <a href="{% url 'register' %}?role=seller" class="inline-flex items-center px-6 py-3 border-2 border-white text-white rounded-lg hover:bg-white hover:text-blue-700 transition duration-300">
                        <i class="fas fa-store mr-2"></i>Join as Seller
                    </a>
                </div>
            {% else %}
                <a href="{% url 'vps_list' %}" class="inline-flex items-center px-6 py-3 bg-white text-blue-700 rounded-lg shadow-md hover:bg-gray-100 transition duration-300">
                    <i class="fas fa-shopping-cart mr-2"></i>Browse Products
                </a>
            {% endif %}
        </div>
    </section>
{% endblock %}
