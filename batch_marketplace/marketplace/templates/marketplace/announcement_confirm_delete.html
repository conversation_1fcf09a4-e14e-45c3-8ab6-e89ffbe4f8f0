{% extends 'marketplace/base.html' %}

{% block title %}Delete Announcement - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-trash mr-3 text-red-500"></i>
                Delete Announcement
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                Confirm deletion of this announcement
            </p>
        </div>
        <div>
            <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 shadow-sm transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i> Back to List
            </a>
        </div>
    </div>
</div>

<!-- Delete Confirmation -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-exclamation-triangle mr-2 text-red-500"></i> Confirm Deletion
        </h3>
    </div>
    <div class="p-6">
        <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-red-700">
                        Warning: This action cannot be undone. The announcement will be permanently deleted.
                    </p>
                </div>
            </div>
        </div>
        
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ announcement.title }}</h4>
            <p class="text-gray-700 mb-2">{{ announcement.content }}</p>
            <div class="flex flex-wrap gap-2 mt-3">
                <span class="px-2 py-1 text-xs font-medium rounded-full 
                    {% if announcement.priority == 'high' %}bg-red-100 text-red-800
                    {% elif announcement.priority == 'medium' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-blue-100 text-blue-800{% endif %}">
                    {{ announcement.get_priority_display }}
                </span>
                <span class="px-2 py-1 text-xs font-medium rounded-full 
                    {% if announcement.is_active %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ announcement.is_active|yesno:"Active,Inactive" }}
                </span>
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                    Created: {{ announcement.created_at|date:"M d, Y" }}
                </span>
                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                    By: {{ announcement.created_by.username }}
                </span>
            </div>
        </div>
        
        <form method="post" class="flex justify-end space-x-4">
            {% csrf_token %}
            <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 shadow-sm transition-all duration-300">
                <i class="fas fa-times mr-2"></i> Cancel
            </a>
            <button type="submit" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-red-500 to-pink-600 rounded-lg hover:from-red-600 hover:to-pink-700 focus:ring-4 focus:ring-red-200 shadow-md transition-all duration-300">
                <i class="fas fa-trash mr-2"></i> Delete Permanently
            </button>
        </form>
    </div>
</div>
{% endblock %}
