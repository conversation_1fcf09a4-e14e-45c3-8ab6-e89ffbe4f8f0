{% extends 'marketplace/base.html' %}

{% block title %}{{ product.title }} - Batch Marketplace{% endblock %}

{% block content %}
<div class="p-6 space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex mb-4" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>
                    Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <a href="{% url 'vps_list' %}" class="text-sm font-medium text-gray-700 hover:text-blue-600">Items</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <span class="text-sm font-medium text-gray-500">{{ product.title }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <!-- Product Image -->
        <div class="lg:col-span-5">
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                <img src="{{ product.image_url|default:'https://pixabay.com/get/g46d7558c4c23a6eac3cc0fa1dbe57745accf0dad1a022e3f6665a0c584fab0c5f2b82fb886e13fff2a3c21dc3881a85611859537df70883325ce36e889ca5c48_1280.jpg' }}"
                    alt="{{ product.title }}" class="w-full h-64 object-cover">

                <div class="p-6">
                    <h6 class="text-sm font-medium text-gray-500 mb-3">Provided by</h6>
                    <div class="flex items-center">
                        <div class="mr-3">
                            <i class="fas fa-user-circle text-2xl text-gray-400"></i>
                        </div>
                        <div>
                            <h5 class="text-base font-semibold text-gray-900">{{ product.seller.username }}</h5>
                            <span class="text-sm text-gray-500">Seller since {{ product.seller.date_joined|date:"M Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="lg:col-span-7 space-y-6">
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                <div class="p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">{{ product.title }}</h2>

                    <div class="mb-6">
                        <span class="text-3xl font-bold text-emerald-600">${{ product.price }}</span>
                        <span class="text-gray-500 ml-2">One-time payment</span>
                    </div>

                    <div class="mb-6">
                        <h5 class="text-lg font-semibold text-gray-900 mb-2">Description</h5>
                        <p class="text-gray-700">{{ product.description }}</p>
                    </div>

                    <div class="mb-6">
                        <h5 class="text-lg font-semibold text-gray-900 mb-2">Specifications</h5>
                        <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {% with specs=product.specifications.split %}
                                    {% for spec in specs %}
                                        {% if 'High RAM' not in spec and 'Multiple CPU Cores' not in spec and 'Large Storage' not in spec and 'High Bandwidth' not in spec %}
                                            {% if 'RAM:' in spec or 'CPU:' in spec or 'Storage:' in spec or 'Bandwidth:' in spec or 'Price:' in spec or 'Company:' in spec or 'Type:' in spec %}
                                                <div class="flex items-center">
                                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                                    <span class="text-gray-700">{{ spec }}</span>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    {% endfor %}

                                    <!-- Display RAM from dedicated field if available -->
                                    {% if product.ram and not specs|join:" "|lower|contains:"ram:" %}
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-gray-700">RAM: {{ product.ram }}</span>
                                        </div>
                                    {% endif %}

                                    <!-- Always display price -->
                                    {% if not specs|join:" "|lower|contains:"price:" %}
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                            <span class="text-gray-700">Price: ${{ product.price }}</span>
                                        </div>
                                    {% endif %}
                                {% endwith %}
                            </div>
                        </div>
                    </div>

                    <div>
                        {% if user.is_authenticated and user.profile.role == 'buyer' %}
                            <a href="{% url 'create_order' product.id %}" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 focus:ring-4 focus:ring-emerald-200 shadow-md transition-all duration-300">
                                <i class="fas fa-shopping-cart mr-2"></i>Buy Now
                            </a>
                        {% elif user.is_authenticated and user.profile.role == 'seller' %}
                            {% if product.is_own %}
                                <button onclick="showDeleteModal()" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium text-red-700 bg-red-100 border border-red-200 rounded-lg hover:bg-red-200 focus:ring-4 focus:ring-red-100 shadow-md transition-all duration-300">
                                    <i class="fas fa-trash-alt mr-2"></i>Delete Product
                                </button>
                            {% else %}
                                <a href="{% url 'create_order' product.id %}" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 focus:ring-4 focus:ring-emerald-200 shadow-md transition-all duration-300">
                                    <i class="fas fa-shopping-cart mr-2"></i>Buy Now
                                </a>
                            {% endif %}
                        {% else %}
                            <a href="{% url 'login' %}" class="w-full inline-flex justify-center items-center px-6 py-3 text-base font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 focus:ring-4 focus:ring-emerald-200 shadow-md transition-all duration-300">
                                <i class="fas fa-sign-in-alt mr-2"></i>Login to Purchase
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Payment & Delivery Information -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                <div class="border-b border-gray-200 px-6 py-4">
                    <h5 class="text-lg font-semibold text-gray-900">Payment & Delivery</h5>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h6 class="flex items-center text-base font-medium text-gray-900 mb-2">
                                <i class="fas fa-credit-card text-emerald-500 mr-2"></i>Payment Method
                            </h6>
                            <p class="text-gray-700 mb-1">USDT TRC-20 only</p>
                            <span class="text-sm text-gray-500">Secure payment with blockchain verification</span>
                        </div>
                        <div>
                            <h6 class="flex items-center text-base font-medium text-gray-900 mb-2">
                                <i class="fas fa-truck text-emerald-500 mr-2"></i>Delivery
                            </h6>
                            <p class="text-gray-700 mb-1">Instant after payment verification</p>
                            <span class="text-sm text-gray-500">Login credentials will be provided via order chat</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Support & Guarantees -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
                <div class="border-b border-gray-200 px-6 py-4">
                    <h5 class="text-lg font-semibold text-gray-900">Support & Guarantees</h5>
                </div>
                <div class="p-6">
                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <i class="fas fa-comments text-emerald-500 mt-1 mr-3"></i>
                            <span class="text-gray-700">Direct communication with seller via order chat</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-shield-alt text-emerald-500 mt-1 mr-3"></i>
                            <span class="text-gray-700">Admin-backed dispute resolution</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-undo text-emerald-500 mt-1 mr-3"></i>
                            <span class="text-gray-700">Refund available if service not delivered as described</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Similar Products -->
    <div class="mt-8">
        <h3 class="text-xl font-bold text-gray-900 mb-6">Similar Items</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- We'd use a query to get similar products in real implementation -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300">
                <img src="https://pixabay.com/get/g5835301d169e8338732d24945c68a3af52f31ac573616c2dc78dc14c82f8963113765a4e0be0afd2ebd79b0f00daa8e3d9cd2a19deb140be502f47566d8b214b_1280.jpg"
                     class="w-full h-48 object-cover" alt="Similar VPS">
                <div class="p-6">
                    <h5 class="text-lg font-semibold text-gray-900 mb-2">High Performance VPS</h5>
                    <p class="text-gray-700 mb-4 h-20 overflow-hidden">8-core CPU, 16GB RAM, 500GB SSD storage with unlimited bandwidth.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-emerald-600">$49.99</span>
                        <a href="#" class="inline-flex items-center px-3 py-2 text-sm font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors">
                            View Details
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300">
                <img src="https://pixabay.com/get/gc1719cfb7c5c1db707b021b5a2f683a438f66e5b28aa8b68e89f29066344b836c4c1b2d2dd03b598bea1783816b347d8c006f4e1667a0ad34665315f36ad7667_1280.jpg"
                     class="w-full h-48 object-cover" alt="Similar VPS">
                <div class="p-6">
                    <h5 class="text-lg font-semibold text-gray-900 mb-2">Balanced VPS Package</h5>
                    <p class="text-gray-700 mb-4 h-20 overflow-hidden">4-core CPU, 8GB RAM, 250GB SSD storage with 5TB bandwidth.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-emerald-600">$29.99</span>
                        <a href="#" class="inline-flex items-center px-3 py-2 text-sm font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors">
                            View Details
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden hover:shadow-lg transition-all duration-300">
                <img src="https://pixabay.com/get/gd12040adc427d19fbfa328eef8d5039f0da72fab034ff5df590c055037f0e3a4044e01fd2c159feeb8e71dda2193b71f0eeba9957dde638c9b7b76903387fe65_1280.jpg"
                     class="w-full h-48 object-cover" alt="Similar VPS">
                <div class="p-6">
                    <h5 class="text-lg font-semibold text-gray-900 mb-2">Budget VPS Solution</h5>
                    <p class="text-gray-700 mb-4 h-20 overflow-hidden">2-core CPU, 4GB RAM, 100GB SSD storage with 2TB bandwidth.</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold text-emerald-600">$19.99</span>
                        <a href="#" class="inline-flex items-center px-3 py-2 text-sm font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Product Modal -->
{% if user.is_authenticated and user.profile.role == 'seller' and product.is_own %}
<div id="deleteProductModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeDeleteModal()"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            Confirm Deletion
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                Are you sure you want to delete this product?
                            </p>
                            <p class="text-sm text-red-600 font-medium mt-2">
                                This action cannot be undone.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form method="post" action="{% url 'delete_vps_product' product.id %}">
                    {% csrf_token %}
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        Delete Product
                    </button>
                </form>
                <button type="button" onclick="closeDeleteModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    function showDeleteModal() {
        document.getElementById('deleteProductModal').classList.remove('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('deleteProductModal').classList.add('hidden');
    }
</script>
{% endif %}
{% endblock %}
