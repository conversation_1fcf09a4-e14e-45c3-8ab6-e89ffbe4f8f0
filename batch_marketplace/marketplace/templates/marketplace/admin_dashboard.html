{% extends 'marketplace/base.html' %}

{% block title %}Admin Dashboard - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-shield-alt mr-3 text-purple-500"></i>
                Admin Dashboard
            </h1>
            <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
                Welcome back, {{ user.username }}! Manage platform operations here.
            </p>
        </div>
        <div class="flex space-x-3">
            <a href="{% url 'ticket_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-700 bg-purple-100 dark:bg-purple-900 dark:text-purple-200 rounded-lg hover:bg-purple-200 dark:hover:bg-purple-800 transition-colors">
                <i class="fas fa-ticket-alt mr-2"></i> Support Tickets
            </a>
            <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-700 bg-indigo-100 dark:bg-indigo-900 dark:text-indigo-200 rounded-lg hover:bg-indigo-200 dark:hover:bg-indigo-800 transition-colors">
                <i class="fas fa-bullhorn mr-2"></i> Announcements
            </a>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
    <div class="bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl shadow-md text-white p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                <i class="fas fa-credit-card text-2xl text-white"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-purple-100">Pending Payments</p>
                <p class="text-2xl font-bold text-white">{{ pending_payments.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl shadow-md text-white p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                <i class="fas fa-wallet text-2xl text-white"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-blue-100">Pending Top-ups</p>
                <p class="text-2xl font-bold text-white">{{ pending_topups.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl shadow-md text-white p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                <i class="fas fa-exclamation-triangle text-2xl text-white"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-amber-100">Open Reports</p>
                <p class="text-2xl font-bold text-white">{{ open_reports.count }}</p>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-md text-white p-6 hover:shadow-lg transition-all duration-300">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                <i class="fas fa-users text-2xl text-white"></i>
            </div>
            <div>
                <p class="text-sm font-medium text-emerald-100">Active Users</p>
                <p class="text-2xl font-bold text-white">{{ user_count|default:"--" }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Pending Payment Verifications -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-credit-card mr-2 text-purple-500"></i> Pending Payment Verifications
        </h3>
    </div>
    <div class="p-6">
        {% if pending_payments %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Order #</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Buyer</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">TXID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {% for payment in pending_payments %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#{{ payment.order.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ payment.order.buyer.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">${{ payment.amount }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                    <span class="font-mono">{{ payment.txid|truncatechars:20 }}</span>
                                    <a href="https://tronscan.org/#/transaction/{{ payment.txid }}" target="_blank" class="ml-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ payment.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'verify_payment' payment.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors">
                                        <i class="fas fa-check-circle mr-1"></i> Verify
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-10">
                <img src="https://pixabay.com/get/gfb3657208945ef219759b140d8deebd4b7fc6a06cdbba905a97cbcd0f9d1461a9489a47b61d789db05f3efa308af372782490ef4eea5f11940ad6171f5569d78_1280.jpg" alt="No Pending Payments" class="mx-auto h-40 mb-4 rounded-lg">
                <h5 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No pending payments to verify</h5>
                <p class="text-gray-500 dark:text-gray-400">All payments have been verified</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Pending Balance Top-ups -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-wallet mr-2 text-blue-500"></i> Pending Balance Top-ups
        </h3>
    </div>
    <div class="p-6">
        {% if pending_topups %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">ID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Amount</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">TXID</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Submitted</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {% for topup in pending_topups %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#{{ topup.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ topup.user.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">${{ topup.amount }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                    <span class="font-mono">{{ topup.txid|truncatechars:20 }}</span>
                                    <a href="https://tronscan.org/#/transaction/{{ topup.txid }}" target="_blank" class="ml-2 text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ topup.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'verify_topup' topup.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors">
                                        <i class="fas fa-check-circle mr-1"></i> Verify
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-10">
                <img src="https://pixabay.com/get/g5a85dc0f7a27858e509e3402d88839eecf65da2c0d1c8048184cb069d5d1b38c9a651b73633bc87340c881d1aa4ca92ef64e2d450452b40de8aaf702ebc9ff73_1280.jpg" alt="No Pending Top-ups" class="mx-auto h-40 mb-4 rounded-lg">
                <h5 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No pending balance top-ups to verify</h5>
                <p class="text-gray-500 dark:text-gray-400">All top-ups have been verified</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Open Reports/Disputes -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-exclamation-triangle mr-2 text-amber-500"></i> Open Reports & Disputes
        </h3>
    </div>
    <div class="p-6">
        {% if open_reports %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Report #</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Order Item</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Reported By</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Seller</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {% for report in open_reports %}
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">#{{ report.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                    <div>{{ report.order_item.product.title }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Order #{{ report.order_item.order.id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ report.created_by.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ report.order_item.product.seller.username }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ report.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'order_report' report.order_item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-amber-600 hover:bg-amber-700 rounded-lg transition-colors">
                                        <i class="fas fa-gavel mr-1"></i> Resolve
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-10">
                <img src="https://pixabay.com/get/g5a85dc0f7a27858e509e3402d88839eecf65da2c0d1c8048184cb069d5d1b38c9a651b73633bc87340c881d1aa4ca92ef64e2d450452b40de8aaf702ebc9ff73_1280.jpg" alt="No Open Reports" class="mx-auto h-40 mb-4 rounded-lg">
                <h5 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No open reports or disputes</h5>
                <p class="text-gray-500 dark:text-gray-400">All reports have been resolved</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Recent Status Changes -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-history mr-2 text-gray-500"></i> Recent Status Changes
        </h3>
    </div>
    <div class="p-6">
        {% if recent_status_changes %}
            <div class="space-y-6">
                {% for status in recent_status_changes %}
                    <div class="flex flex-col md:flex-row gap-4 pb-6 border-b border-gray-100 dark:border-gray-700 last:border-0 last:pb-0">
                        <div class="flex-shrink-0 w-full md:w-48">
                            <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ status.changed_at|date:"M d, Y, h:i A" }}</div>
                        </div>
                        <div class="flex-grow">
                            <p class="text-sm text-gray-700 dark:text-gray-300 mb-3">
                                <span class="font-medium text-gray-900 dark:text-white">{{ status.order_item.product.title }}</span>
                                <span class="text-gray-500 dark:text-gray-400">(Order #{{ status.order_item.order.id }})</span>
                                status changed from
                                <span class="font-medium">{{ status.old_status|default:"New"|title }}</span> to
                                <span class="font-medium">{{ status.new_status|title }}</span>
                                {% if status.changed_by %}
                                    by <span class="text-blue-600 dark:text-blue-400">{{ status.changed_by.username }}</span>
                                {% endif %}
                            </p>
                            <div>
                                <a href="{% url 'order_report' status.order_item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                                    <i class="fas fa-file-alt mr-1"></i> View Report
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-10">
                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                    <i class="fas fa-history text-2xl text-gray-400 dark:text-gray-500"></i>
                </div>
                <h5 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No recent status changes</h5>
                <p class="text-gray-500 dark:text-gray-400">Status changes will appear here</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
