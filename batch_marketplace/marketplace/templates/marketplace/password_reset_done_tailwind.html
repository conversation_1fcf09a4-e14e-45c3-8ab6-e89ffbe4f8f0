{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Email Sent - Oleer Market</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a2a3a',
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">
</head>
<body class="bg-gray-50 font-sans antialiased text-gray-800">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <!-- Logo and Title -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-3">
                        <img src="{% static 'img/logo.ico' %}" alt="Oleer Market Logo" class="h-32 transform hover:scale-105 transition-transform duration-300">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">Password Reset Email Sent</h1>
                </div>

                <!-- Message -->
                <div class="p-4 mb-6 rounded-md bg-blue-50 text-blue-800">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-info-circle text-blue-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">
                                We've emailed you instructions for setting your password, if an account exists with the email you entered. You should receive them shortly.
                            </p>
                        </div>
                    </div>
                </div>

                <div class="text-sm text-gray-600 mb-6">
                    <p class="mb-4">
                        If you don't receive an email, please make sure you've entered the address you registered with, and check your spam folder.
                    </p>
                </div>

                <!-- Back to Login Link -->
                <div class="mt-6 text-center">
                    <a href="{% url 'login' %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150">
                        <i class="fas fa-arrow-left mr-2"></i> Back to Login
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center text-xs text-gray-500">
                <p>&copy; 2025 Oleer Market. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
