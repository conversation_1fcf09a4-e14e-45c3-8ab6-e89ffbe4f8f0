{% extends 'marketplace/base.html' %}

{% block title %}My Purchases - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-shopping-cart mr-3 text-blue-500"></i>
                My Purchases
            </h1>
            <p class="mt-2 text-lg text-gray-600">
                View and manage your purchased items
            </p>
        </div>
        <div class="flex items-center space-x-4">
            <div class="text-gray-700">
                <span class="text-sm">Balance:</span>
                <span class="font-bold text-emerald-600 ml-1">${{ request.user.profile.balance|floatformat:2 }}</span>
            </div>
            <a href="{% url 'topup_balance' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-sm transition-all duration-300">
                <i class="fas fa-wallet mr-2"></i> Top Up Balance
            </a>
        </div>
    </div>
</div>

<!-- My Purchases -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <div class="border-b border-gray-200 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-shopping-cart mr-2 text-blue-500"></i> My Purchased Products
        </h3>
    </div>

    <div class="border-b border-gray-200">
        <div class="flex px-6 py-3 space-x-6">
            <button class="text-sm font-medium text-blue-600 border-b-2 border-blue-600 pb-3 px-1 tab-button active" data-tab="all">
                All Orders
            </button>
            <button class="text-sm font-medium text-gray-500 hover:text-gray-700 pb-3 px-1 tab-button" data-tab="paid">
                Paid
            </button>
            <button class="text-sm font-medium text-gray-500 hover:text-gray-700 pb-3 px-1 tab-button" data-tab="has_rejected">
                Rejected
            </button>
            <button class="text-sm font-medium text-gray-500 hover:text-gray-700 pb-3 px-1 tab-button" data-tab="refunded">
                Refunded
            </button>
        </div>
    </div>

    {% if orders %}
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 border-b border-gray-200">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">Order ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">Price</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36">Date Purchased</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for order in orders %}
                        <tr class="hover:bg-gray-50 order-item" data-status="{{ order.order_status }}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ order.id }}</td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {% for item in order.items.all %}
                                    <div class="flex items-center">
                                        <div class="mr-3 text-blue-500">
                                            <i class="fas fa-server text-2xl"></i>
                                        </div>
                                        <div>
                                            <h6 class="text-sm font-medium text-gray-900">{{ item.product.title }}</h6>
                                            <p class="text-xs text-gray-500">{{ item.product.description|truncatechars:50 }}</p>
                                        </div>
                                    </div>
                                {% empty %}
                                    <span class="text-gray-400">Product details not available</span>
                                {% endfor %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">${{ order.total_amount }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                {% if order.order_status == 'paid' %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-md border border-teal-100 bg-teal-50 text-teal-600">Paid</span>
                                {% elif order.order_status == 'has_rejected' %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-md border border-rose-100 bg-rose-50 text-rose-600">Rejected</span>
                                {% elif order.order_status == 'refunded' %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-md border border-orange-100 bg-orange-50 text-orange-600">Refunded</span>
                                {% else %}
                                    <span class="px-2 py-1 text-xs font-medium rounded-md border border-slate-100 bg-slate-50 text-slate-600">{{ order.order_status|title }}</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ order.created_at|date:"Y-m-d H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <div class="flex space-x-2">
                                    <button class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-lg hover:bg-indigo-100 hover:text-indigo-700 shadow-sm transition-all duration-300" onclick="showOrderDetails({{ order.id }})">
                                        <i class="fas fa-info-circle mr-1"></i> Details
                                    </button>

                                    {% if order.order_status == 'paid' %}
                                        {% for item in order.items.all %}
                                            {% if item.report %}
                                                <a href="{% url 'order_report' item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:text-blue-700 shadow-sm transition-all duration-300">
                                                    <i class="fas fa-eye mr-1"></i> View Report
                                                </a>
                                            {% else %}
                                                <a href="{% url 'order_report' item.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-lg hover:bg-red-100 hover:text-red-700 shadow-sm transition-all duration-300">
                                                    <i class="fas fa-flag mr-1"></i> Report Issue
                                                </a>
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="text-center py-12">
            <div class="mb-4 text-gray-400">
                <i class="fas fa-shopping-cart text-5xl"></i>
            </div>
            <h5 class="text-xl font-semibold text-gray-900 mb-2">You haven't purchased any products yet</h5>
            <p class="text-gray-500 mb-6">Browse our marketplace to find digital products</p>
            <a href="{% url 'vps_list' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-md transition-all duration-300">
                <i class="fas fa-list mr-2"></i> Browse Products
            </a>
        </div>
    {% endif %}
</div>

<!-- Order Details Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto hidden" id="orderDetailsModal">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 flex items-center" id="orderDetailsModalLabel">
                            <i class="fas fa-info-circle mr-2 text-blue-500"></i> Order Details
                        </h3>
                        <div class="mt-4" id="orderDetailsContent">
                            <div class="flex flex-col items-center justify-center py-6">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                                <p class="mt-2 text-gray-500">Loading order details...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:ml-3 sm:w-auto sm:text-sm" id="closeModalBtn">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Tab and Order Details Functionality -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality
        const tabs = document.querySelectorAll('.tab-button');
        const orderItems = document.querySelectorAll('.order-item');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                tabs.forEach(t => {
                    t.classList.remove('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
                    t.classList.add('text-gray-500', 'hover:text-gray-700');
                });

                // Add active class to clicked tab
                this.classList.add('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
                this.classList.remove('text-gray-500', 'hover:text-gray-700');

                const tabStatus = this.getAttribute('data-tab');

                // Show/hide order items based on tab
                orderItems.forEach(item => {
                    const itemStatus = item.getAttribute('data-status');

                    if (tabStatus === 'all' || tabStatus === itemStatus) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Modal functionality
        const modal = document.getElementById('orderDetailsModal');
        const closeBtn = document.getElementById('closeModalBtn');

        closeBtn.addEventListener('click', function() {
            modal.classList.add('hidden');
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        });
    });

    // Order details modal functionality
    function showOrderDetails(orderId) {
        // Get the modal
        const modal = document.getElementById('orderDetailsModal');

        // Show the modal
        modal.classList.remove('hidden');

        // Set the content to loading state
        const content = document.getElementById('orderDetailsContent');
        content.innerHTML = `
            <div class="flex flex-col items-center justify-center py-6">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-500">Loading order details...</p>
            </div>
        `;

        // Fetch real order details from the server
        fetch(`/order/${orderId}/details/`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Format the status badge
                let statusBadgeClass = 'border border-slate-100 bg-slate-50 text-slate-600';
                if (data.status === 'paid') statusBadgeClass = 'border border-teal-100 bg-teal-50 text-teal-600';
                if (data.status === 'has_rejected') statusBadgeClass = 'border border-rose-100 bg-rose-50 text-rose-600';
                if (data.status === 'refunded') statusBadgeClass = 'border border-orange-100 bg-orange-50 text-orange-600';

                // Build the HTML for order details
                let itemsHtml = '';
                data.items.forEach(item => {
                    let statusClass = 'border border-slate-100 bg-slate-50 text-slate-600';
                    if (item.status === 'paid') statusClass = 'border border-teal-100 bg-teal-50 text-teal-600';
                    if (item.status === 'rejected') statusClass = 'border border-rose-100 bg-rose-50 text-rose-600';
                    if (item.status === 'refunded') statusClass = 'border border-orange-100 bg-orange-50 text-orange-600';

                    itemsHtml += `
                        <div class="bg-white rounded-lg shadow-md border border-gray-200 mb-4 overflow-hidden">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-sm font-medium text-gray-900">${item.product_title}</h3>
                                <span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">${item.status_display}</span>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <p class="text-sm text-gray-700"><span class="font-medium">Price:</span> $${item.price.toFixed(2)}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-700"><span class="font-medium">Item ID:</span> #${item.id}</p>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Access Details</h4>
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                                            <tbody class="divide-y divide-gray-200 bg-white">
                                                <!-- Common fields for both types -->
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 w-1/4">
                                                        ${item.item_type === 'account' ? 'Login URL' : 'IP Address'}
                                                    </th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.item_type === 'account' ? item.url : item.ip}</span>
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.item_type === 'account' ? item.url : item.ip}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Username</th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.username}</span>
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.username}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Password</th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.password}</span>
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.password}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>

                                                <!-- Type-specific fields -->
                                                ${item.item_type === 'vps' ? `
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Company</th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.company}</span>
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.company}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                ${item.ram && item.ram !== 'Not available' ? `
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">RAM</th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.ram}</span>
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.ram}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                ` : ''}
                                                ` : `
                                                <tr>
                                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50">Description</th>
                                                    <td class="px-4 py-2 text-sm text-gray-900 flex justify-between items-center">
                                                        <span>${item.description && item.description !== 'Not available' ?
                                                            (item.description.length > 50 ? item.description.substring(0, 50) + '...' : item.description) :
                                                            'No description available'}</span>
                                                        ${item.description && item.description !== 'Not available' ? `
                                                        <button type="button" class="copy-btn ml-2 p-1.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                                                                data-value="${item.description}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        ` : ''}
                                                    </td>
                                                </tr>
                                                `}
                                            </tbody>
                                        </table>
                                    </div>

                                    <!-- Proof link for account items -->
                                    ${item.item_type === 'account' && item.proof_link ? `
                                    <div class="mt-4">
                                        <h4 class="text-sm font-medium text-gray-900 mb-2">Screenshot Proof</h4>
                                        <button class="w-full px-3 py-1.5 text-sm border border-indigo-200 rounded-md bg-indigo-50 text-indigo-600 hover:bg-indigo-100 hover:text-indigo-700 transition-colors flex items-center justify-center"
                                                onclick="window.open('${item.proof_link}', '_blank', 'width=800,height=600')">
                                            <i class="fas fa-image mr-2"></i> View Screenshot
                                        </button>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    `;
                });

                // Build payment info if available
                let paymentHtml = '';
                if (data.payment) {
                    let verifiedBadge = data.payment.verified ?
                        '<span class="px-2 py-1 text-xs font-medium rounded-md border border-emerald-100 bg-emerald-50 text-emerald-600">Verified</span>' :
                        '<span class="px-2 py-1 text-xs font-medium rounded-md border border-amber-100 bg-amber-50 text-amber-600">Pending</span>';

                    paymentHtml = `
                        <div class="bg-white rounded-lg shadow-md border border-gray-200 mb-4 overflow-hidden">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                                <h3 class="text-sm font-medium text-gray-900">Payment Information</h3>
                            </div>
                            <div class="p-4">
                                <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Transaction ID:</span> ${data.payment.txid}</p>
                                <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Status:</span> ${verifiedBadge}</p>
                                <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Date:</span> ${data.payment.created_at}</p>
                            </div>
                        </div>
                    `;
                }

                // Build order history if available
                let historyHtml = '';
                if (data.history && data.history.length > 0) {
                    let historyItems = '';
                    data.history.forEach(item => {
                        historyItems += `
                            <li class="px-4 py-3 border-b border-gray-200 last:border-b-0">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-700">${item.old_status || 'New'} → ${item.new_status}</span>
                                    <span class="text-xs text-gray-500">${item.changed_at}</span>
                                </div>
                            </li>
                        `;
                    });

                    historyHtml = `
                        <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                            <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                                <h3 class="text-sm font-medium text-gray-900">Order History</h3>
                            </div>
                            <ul>
                                ${historyItems}
                            </ul>
                        </div>
                    `;
                }

                // Update the modal content with real data
                content.innerHTML = `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-3">Order Information</h3>
                            <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Order ID:</span> #${data.id}</p>
                            <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Status:</span> <span class="px-2 py-1 text-xs font-medium rounded-full ${statusBadgeClass}">${data.status}</span></p>
                            <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Date:</span> ${data.created_at}</p>
                            <p class="text-sm text-gray-700 mb-2"><span class="font-medium">Total Amount:</span> $${data.total_amount.toFixed(2)}</p>
                        </div>
                        <div>
                            ${paymentHtml}
                        </div>
                    </div>

                    <h3 class="text-lg font-medium text-gray-900 mb-3">Products</h3>
                    ${itemsHtml}

                    <h3 class="text-lg font-medium text-gray-900 mb-3">History</h3>
                    ${historyHtml}
                `;

                // Set up copy buttons after content is loaded
                setTimeout(() => {
                    setupCopyButtons();
                }, 100);
            })
            .catch(error => {
                console.error('Error fetching order details:', error);
                content.innerHTML = `
                    <div class="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-red-700">
                                    Error loading order details. Please try again later.
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            });
    }

    // Function to set up copy buttons
    function setupCopyButtons() {
        const copyButtons = document.querySelectorAll('.copy-btn');

        copyButtons.forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-value');
                const originalIcon = this.innerHTML;

                // Copy text to clipboard
                navigator.clipboard.writeText(textToCopy)
                    .then(() => {
                        // Show success state
                        this.innerHTML = '<i class="fas fa-check text-green-500"></i>';
                        this.classList.add('bg-green-50');

                        // Reset after 2 seconds
                        setTimeout(() => {
                            this.innerHTML = originalIcon;
                            this.classList.remove('bg-green-50');
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Failed to copy text: ', err);
                        // Show error state
                        this.innerHTML = '<i class="fas fa-times text-red-500"></i>';
                        this.classList.add('bg-red-50');

                        // Reset after 2 seconds
                        setTimeout(() => {
                            this.innerHTML = originalIcon;
                            this.classList.remove('bg-red-50');
                        }, 2000);
                    });
            });
        });
    }
</script>

{% endblock %}