{% extends 'marketplace/base.html' %}

{% block title %}{% if product %}Edit{% else %}Add{% endif %} Item - Oleer Market{% endblock %}

{% block content %}
<div class="w-full">
    <!-- Head<PERSON> and Breadcrumb -->
    <div class="mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                <i class="fas fa-{% if product %}edit{% else %}plus-circle{% endif %} mr-2 text-emerald-500"></i>
                {% if product %}Edit{% else %}Add{% endif %} Item
            </h1>
            <nav class="flex mt-2" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{% url 'index' %}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-emerald-600">
                            <svg class="w-3 h-3 mr-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
                            </svg>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <a href="{% url 'seller_dashboard' %}" class="ml-1 text-sm font-medium text-gray-700 hover:text-emerald-600 md:ml-2">Seller Dashboard</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <svg class="w-3 h-3 mx-1 text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                            </svg>
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{% if product %}Edit{% else %}Add{% endif %} Item</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="flex flex-col lg:flex-row gap-6">
        <div class="w-full lg:w-2/3">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-{% if product %}edit{% else %}plus-circle{% endif %} mr-2 text-emerald-500"></i>
                        {% if product %}Edit{% else %}Add{% endif %} Item
                    </h2>
                </div>
                <div class="p-6">
                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- Hidden fields with default values -->
                        {{ form.title.as_hidden }}
                        {{ form.description.as_hidden }}
                        {{ form.specifications.as_hidden }}
                        {{ form.image_url.as_hidden }}
                        {{ form.category.as_hidden }}

                        <!-- Essential Information Section -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 overflow-hidden">
                            <div class="px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white">
                                <h3 class="text-base font-semibold">Item Information</h3>
                            </div>
                            <div class="p-5">
                                <!-- Item Type Selection -->
                                <div class="mb-6">
                                    <label for="{{ form.item_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Item Type <span class="text-red-600">*</span>
                                    </label>
                                    <div class="grid grid-cols-2 gap-4">
                                        {% for value, text in form.fields.item_type.choices %}
                                            <div class="relative">
                                                <input type="radio" id="item_type_{{ value }}" name="{{ form.item_type.name }}" value="{{ value }}"
                                                    class="peer absolute h-0 w-0 opacity-0"
                                                    {% if form.item_type.value == value or forloop.first and not form.item_type.value %}checked{% endif %}>
                                                <label for="item_type_{{ value }}"
                                                    class="flex flex-col items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                                                    peer-checked:border-emerald-500 peer-checked:bg-emerald-50 hover:bg-gray-50
                                                    {% if value == 'vps' %}border-blue-200 bg-blue-50{% else %}border-purple-200 bg-purple-50{% endif %}">
                                                    <i class="text-2xl mb-2 {% if value == 'vps' %}fas fa-server text-blue-500{% else %}fas fa-user-circle text-purple-500{% endif %}"></i>
                                                    <span class="font-medium">{{ text }}</span>
                                                    <span class="text-xs text-gray-500 mt-1">
                                                        {% if value == 'vps' %}IP, Username, Password, RAM{% else %}URL, Username, Password, Proof{% endif %}
                                                    </span>
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                    {% if form.item_type.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.item_type.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                    <p class="mt-2 text-sm text-gray-500">Select the type of item you're selling - this will determine which fields are required</p>
                                </div>

                                <!-- Common Fields -->
                                <div class="mb-4">
                                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Username <span class="text-red-600">*</span>
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                        <input type="text" name="{{ form.username.name }}" id="{{ form.username.id_for_label }}"
                                            class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                            {% if form.username.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                            value="{{ form.username.value|default:'' }}"
                                            placeholder="Username for login">
                                    </div>
                                    {% if form.username.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.username.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                </div>

                                <div class="mb-4">
                                    <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Password <span class="text-red-600">*</span>
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                        <input type="password" name="{{ form.password.name }}" id="{{ form.password.id_for_label }}"
                                            class="block w-full pl-10 pr-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                            {% if form.password.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                            value="{{ form.password.value|default:'' }}"
                                            placeholder="Password for login">
                                        <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-emerald-500">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    {% if form.password.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                </div>

                                <!-- VPS/RDP Fields -->
                                <div id="vps-fields">
                                    <div class="mb-4">
                                        <label for="{{ form.login_url.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            IP Address <span class="text-red-600">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-server text-gray-400"></i>
                                            </div>
                                            <input type="text" name="{{ form.login_url.name }}" id="{{ form.login_url.id_for_label }}"
                                                class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                                {% if form.login_url.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                                value="{{ form.login_url.value|default:'' }}"
                                                placeholder="IP Address (e.g., ***********)">
                                        </div>
                                        {% if form.login_url.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.login_url.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ form.ram.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            RAM <span class="text-red-600">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-memory text-gray-400"></i>
                                            </div>
                                            <input type="text" name="{{ form.ram.name }}" id="{{ form.ram.id_for_label }}"
                                                class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                                {% if form.ram.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                                value="{{ form.ram.value|default:'' }}"
                                                placeholder="RAM Amount (e.g., 8GB, 16GB)">
                                        </div>
                                        {% if form.ram.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.ram.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Account Fields -->
                                <div id="account-fields" style="display: none;">
                                    <div class="mb-4">
                                        <label for="{{ form.url.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Login URL <span class="text-red-600">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-link text-gray-400"></i>
                                            </div>
                                            <input type="url" name="{{ form.url.name }}" id="{{ form.url.id_for_label }}"
                                                class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                                {% if form.url.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                                value="{{ form.url.value|default:'' }}"
                                                placeholder="Login URL (https://example.com/login)">
                                        </div>
                                        {% if form.url.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.url.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ form.proof.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Description <span class="text-red-600">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute top-3 left-3 flex items-start pointer-events-none">
                                                <i class="fas fa-file-alt text-gray-400"></i>
                                            </div>
                                            <textarea name="{{ form.proof.name }}" id="{{ form.proof.id_for_label }}"
                                                class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                                {% if form.proof.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                                rows="4"
                                                placeholder="Describe the account: features, subscription details, account age, etc.">{{ form.proof.value|default:'' }}</textarea>
                                        </div>
                                        {% if form.proof.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.proof.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ form.proof_link.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                            Screenshot Proof <span class="text-red-600">*</span>
                                        </label>
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-image text-gray-400"></i>
                                            </div>
                                            <input type="url" name="{{ form.proof_link.name }}" id="{{ form.proof_link.id_for_label }}"
                                                class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                                {% if form.proof_link.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                                value="{{ form.proof_link.value|default:'' }}"
                                                placeholder="Link to screenshot proof (e.g., https://imgur.com/abcd123)">
                                        </div>
                                        {% if form.proof_link.errors %}
                                            <p class="mt-2 text-sm text-red-600">
                                                {% for error in form.proof_link.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </p>
                                        {% endif %}
                                        <p class="mt-1 text-xs text-blue-600"><i class="fas fa-info-circle mr-1"></i> Upload screenshots to an image hosting site and paste the link here</p>
                                    </div>
                                </div>

                                <!-- Company field - only shown for VPS/RDP -->
                                <div id="company-field" class="mb-4">
                                    <label for="{{ form.company.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                                    <div class="flex">
                                        <input type="text" name="{{ form.company.name }}" id="{{ form.company.id_for_label }}"
                                            class="block w-full rounded-l-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                            {% if form.company.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                            value="{{ form.company.value|default:'' }}"
                                            placeholder="Company name (e.g., Amazon, Microsoft)">
                                        <button type="button" id="detectCompany" class="inline-flex items-center px-3 py-2 border-2 border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-700 hover:bg-emerald-50 hover:text-emerald-600 hover:border-emerald-500 transition-colors">
                                            <i class="fas fa-search mr-1"></i> Detect
                                        </button>
                                    </div>
                                    {% if form.company.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.company.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                    <div id="company-loading" class="mt-2 text-sm text-blue-600 hidden">
                                        <i class="fas fa-spinner fa-spin mr-1"></i> Detecting company from IP...
                                    </div>
                                    <div id="company-result" class="mt-2 text-sm text-green-600 hidden"></div>
                                    <div id="company-error" class="mt-2 text-sm text-red-600 hidden"></div>
                                </div>

                                <div class="mb-4">
                                    <label for="{{ form.price.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        Price (USD) <span class="text-red-600">*</span>
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-dollar-sign text-gray-400"></i>
                                        </div>
                                        <input type="number" step="0.01" name="{{ form.price.name }}" id="{{ form.price.id_for_label }}"
                                            class="block w-full pl-10 rounded-md border-2 border-gray-300 shadow-sm focus:border-emerald-500 focus:ring-emerald-500 sm:text-sm
                                            {% if form.price.errors %}border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500{% endif %}"
                                            value="{{ form.price.value|default:'' }}"
                                            placeholder="Enter price in USD (e.g., 19.99) - payments in USDT">
                                    </div>
                                    {% if form.price.errors %}
                                        <p class="mt-2 text-sm text-red-600">
                                            {% for error in form.price.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between mt-8">
                            <a href="{% url 'seller_dashboard' %}" class="inline-flex items-center px-5 py-2.5 border-2 border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                            </a>
                            <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent shadow-md text-sm font-medium rounded-md text-white bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transform hover:scale-105 transition-all duration-200">
                                {% if product %}
                                    <i class="fas fa-save mr-2"></i>Update Product
                                {% else %}
                                    <i class="fas fa-plus mr-2"></i>Add Product
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {% if product %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mt-4">
                    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                        <h3 class="text-lg font-semibold text-gray-800">Product Preview</h3>
                    </div>
                    <div class="p-6">
                        <div class="flex flex-col md:flex-row gap-6">
                            <div class="md:w-1/3">
                                <img src="{{ product.image_url|default:'https://pixabay.com/get/g0a7458aaeacad968aa01c6d9a4d35f0a9a27b215ec74b29b787e34e23943bce66b891b182bcc76a198973a60483c28fc3a4be6afeda35b4ccd117dfad5a3f55d_1280.jpg' }}"
                                    class="w-full h-auto rounded-lg object-cover" alt="{{ product.title }}">
                            </div>
                            <div class="md:w-2/3">
                                <h4 class="text-xl font-semibold text-gray-900 mb-2">{{ product.title }}</h4>
                                <p class="text-gray-700 mb-2"><span class="font-medium">Price:</span> <span class="text-emerald-600 font-semibold">${{ product.price }}</span></p>
                                <p class="text-gray-700 mb-4"><span class="font-medium">Status:</span>
                                    {% if product.is_active %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 ml-1">Active</span>
                                    {% else %}
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800 ml-1">Inactive</span>
                                    {% endif %}
                                </p>
                                <button onclick="window.location.href='{% url 'seller_dashboard' %}'" class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-emerald-700 bg-emerald-100 rounded-lg hover:bg-emerald-200 transition-colors">
                                    <i class="fas fa-list mr-1.5"></i>Back to Product List
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>

        <div class="w-full lg:w-1/3">
            <div class="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-6">
                <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-500 to-teal-600">
                    <h3 class="text-lg font-semibold text-white">Tips for Great VPS Listings</h3>
                </div>
                <div class="p-6">
                    <div class="mb-4">
                        <h6 class="text-base font-medium text-gray-800 flex items-center mb-1">
                            <i class="fas fa-heading mr-2 text-emerald-500"></i>Title
                        </h6>
                        <p class="text-sm text-gray-600">Be descriptive but concise. Include key specs like CPU cores and RAM amount.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-base font-medium text-gray-800 flex items-center mb-1">
                            <i class="fas fa-align-left mr-2 text-emerald-500"></i>Description
                        </h6>
                        <p class="text-sm text-gray-600">Explain the benefits and uses of your VPS. Include information about the datacenter, uptime guarantees, and any additional services.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-base font-medium text-gray-800 flex items-center mb-1">
                            <i class="fas fa-server mr-2 text-emerald-500"></i>Specifications
                        </h6>
                        <p class="text-sm text-gray-600">List each specification on a new line. Include CPU, RAM, storage, bandwidth, OS options, and any other relevant details.</p>
                    </div>

                    <div class="mb-4">
                        <h6 class="text-base font-medium text-gray-800 flex items-center mb-1">
                            <i class="fas fa-dollar-sign mr-2 text-emerald-500"></i>Price
                        </h6>
                        <p class="text-sm text-gray-600">Set competitive prices. Be clear about what's included in the price and any additional costs.</p>
                    </div>

                    <div>
                        <h6 class="text-base font-medium text-gray-800 flex items-center mb-1">
                            <i class="fas fa-image mr-2 text-emerald-500"></i>Image
                        </h6>
                        <p class="text-sm text-gray-600">Use high-quality, relevant images. You can use server rack photos, hosting diagrams, or your own branding.</p>
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle fields based on item type
        const itemTypeRadios = document.querySelectorAll('input[name="{{ form.item_type.name }}"]');
        const vpsFields = document.getElementById('vps-fields');
        const accountFields = document.getElementById('account-fields');
        const companyField = document.getElementById('company-field');

        function toggleFields() {
            // Find the selected radio button
            let selectedType = '';
            itemTypeRadios.forEach(radio => {
                if (radio.checked) {
                    selectedType = radio.value;
                }
            });

            if (selectedType === 'vps') {
                vpsFields.style.display = 'block';
                accountFields.style.display = 'none';
                companyField.style.display = 'block';
            } else if (selectedType === 'account') {
                vpsFields.style.display = 'none';
                accountFields.style.display = 'block';
                companyField.style.display = 'none';
            }
        }

        // Initial toggle based on selected value
        toggleFields();

        // Add event listener for changes to each radio button
        itemTypeRadios.forEach(radio => {
            radio.addEventListener('change', toggleFields);
        });

        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function(e) {
            let isValid = true;

            // Get common form fields
            const usernameInput = document.getElementById('id_username');
            const passwordInput = document.getElementById('id_password');
            const priceInput = document.getElementById('id_price');
            const itemType = itemTypeSelect.value;

            // Reset validation
            const invalidInputs = form.querySelectorAll('.border-red-300');
            invalidInputs.forEach(input => {
                input.classList.remove('border-red-300', 'text-red-900', 'placeholder-red-300', 'focus:border-red-500', 'focus:ring-red-500');
                input.classList.add('border-gray-300', 'focus:border-emerald-500', 'focus:ring-emerald-500');
            });

            const errorMessages = form.querySelectorAll('.text-red-600');
            errorMessages.forEach(div => div.remove());

            // Common field validation
            if (!usernameInput.value.trim()) {
                showError(usernameInput, 'Username is required');
                isValid = false;
            }

            if (!passwordInput.value.trim()) {
                showError(passwordInput, 'Password is required');
                isValid = false;
            }

            if (!priceInput.value.trim()) {
                showError(priceInput, 'Price is required');
                isValid = false;
            } else if (isNaN(parseFloat(priceInput.value)) || parseFloat(priceInput.value) <= 0) {
                showError(priceInput, 'Price must be a positive number');
                isValid = false;
            }

            // Type-specific validation
            if (itemType === 'vps') {
                const loginUrlInput = document.getElementById('id_login_url');
                const ramInput = document.getElementById('id_ram');

                if (!loginUrlInput.value.trim()) {
                    showError(loginUrlInput, 'IP Address is required');
                    isValid = false;
                }

                if (!ramInput.value.trim()) {
                    showError(ramInput, 'RAM amount is required');
                    isValid = false;
                }
            } else if (itemType === 'account') {
                const urlInput = document.getElementById('id_url');
                const proofInput = document.getElementById('id_proof');

                if (!urlInput.value.trim()) {
                    showError(urlInput, 'Login URL is required');
                    isValid = false;
                }

                if (!proofInput.value.trim()) {
                    showError(proofInput, 'Proof of ownership is required');
                    isValid = false;
                }
            }

            if (!isValid) {
                e.preventDefault();
            }
        });

        function showError(element, message) {
            // Add Tailwind error classes
            element.classList.remove('border-gray-300', 'focus:border-emerald-500', 'focus:ring-emerald-500');
            element.classList.add('border-red-300', 'text-red-900', 'placeholder-red-300', 'focus:border-red-500', 'focus:ring-red-500');

            // Add error message if it doesn't exist
            let parent = element.parentNode;
            let errorElement = parent.querySelector('.text-red-600');

            if (!errorElement) {
                const errorDiv = document.createElement('p');
                errorDiv.className = 'mt-2 text-sm text-red-600';
                errorDiv.textContent = message;

                // Find the right place to insert the error message
                let helpText = parent.querySelector('.text-gray-500');
                if (helpText) {
                    parent.insertBefore(errorDiv, helpText);
                } else {
                    parent.appendChild(errorDiv);
                }
            } else {
                errorElement.textContent = message;
            }
        }

        // Company auto-detection functionality
        const detectCompanyBtn = document.getElementById('detectCompany');
        const ipAddressField = document.getElementById('id_login_url');
        const companyInputField = document.getElementById('id_company');
        const companyLoading = document.getElementById('company-loading');
        const companyResult = document.getElementById('company-result');
        const companyError = document.getElementById('company-error');

        if (detectCompanyBtn && ipAddressField) {
            detectCompanyBtn.addEventListener('click', function() {
                const ipAddress = ipAddressField.value.trim();

                // Reset previous messages
                companyLoading.classList.remove('hidden');
                companyResult.classList.add('hidden');
                companyError.classList.add('hidden');

                if (!ipAddress) {
                    companyLoading.classList.add('hidden');
                    companyError.textContent = 'Please enter an IP address first';
                    companyError.classList.remove('hidden');
                    return;
                }

                // Validate IP address format
                const ipRegex = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
                if (!ipRegex.test(ipAddress)) {
                    companyLoading.classList.add('hidden');
                    companyError.textContent = 'Please enter a valid IP address (e.g., ***********)';
                    companyError.classList.remove('hidden');
                    return;
                }

                // Simulate API call to get company info (in a real app, you'd call an actual IP lookup API)
                setTimeout(() => {
                    try {
                        // This is a simulation - in a real app, you'd make an API call to a service like ipinfo.io
                        // For demo purposes, we'll generate a fake company name based on the IP
                        const ipParts = ipAddress.split('.');
                        const fakeCompanies = [
                            'Amazon Web Services',
                            'Microsoft Azure',
                            'Google Cloud',
                            'DigitalOcean',
                            'Linode',
                            'Vultr',
                            'OVH',
                            'Hetzner',
                            'Contabo',
                            'Scaleway'
                        ];

                        // Use the last octet to select a company
                        const companyIndex = parseInt(ipParts[3]) % fakeCompanies.length;
                        const detectedCompany = fakeCompanies[companyIndex];

                        // Update the company field
                        companyInputField.value = detectedCompany;

                        // Show success message
                        companyLoading.classList.add('hidden');
                        companyResult.textContent = `Company detected: ${detectedCompany}`;
                        companyResult.classList.remove('hidden');
                    } catch (error) {
                        companyLoading.classList.add('hidden');
                        companyError.textContent = 'Error detecting company. Please enter manually.';
                        companyError.classList.remove('hidden');
                    }
                }, 1000); // Simulate network delay
            });
        }

        // Password toggle functionality
        const togglePassword = document.getElementById('togglePassword');
        const passwordField = document.getElementById('id_password');

        if (togglePassword && passwordField) {
            togglePassword.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);

                // Toggle the eye icon
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
        }
    });
</script>
{% endblock %}
