{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Complete - Oleer Market</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1a2a3a',
                        secondary: '#2563eb',
                        accent: '#f43f5e',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'img/favicon.png' %}">
</head>
<body class="bg-gray-50 font-sans antialiased text-gray-800">
    <div class="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="w-full max-w-md">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <!-- Logo and Title -->
                <div class="text-center mb-8">
                    <div class="flex justify-center mb-3">
                        <img src="{% static 'img/logo.ico' %}" alt="Oleer Market Logo" class="h-32 transform hover:scale-105 transition-transform duration-300">
                    </div>
                    <h1 class="text-2xl font-bold text-gray-900">Password Reset Complete</h1>
                </div>

                <!-- Success Message -->
                <div class="p-4 mb-6 rounded-md bg-green-50 text-green-800">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium">
                                Your password has been set. You may go ahead and log in now.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Login Button -->
                <div class="mt-6 text-center">
                    <a href="{% url 'login' %}" class="inline-flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 w-full">
                        <i class="fas fa-sign-in-alt mr-2"></i> Log In
                    </a>
                </div>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center text-xs text-gray-500">
                <p>&copy; 2025 Oleer Market. All rights reserved.</p>
            </div>
        </div>
    </div>
</body>
</html>
