{% extends 'marketplace/base.html' %}

{% block title %}Notifications - Oleer Market{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 p-6 mb-6">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-bell mr-3 text-blue-500"></i>
                Notifications
            </h1>
            <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
                View and manage your notifications
            </p>
        </div>
        <div>
            <a href="{% url 'mark_all_notifications_read' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 dark:bg-blue-900 dark:text-blue-200 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                <i class="fas fa-check-double mr-2"></i> Mark All as Read
            </a>
        </div>
    </div>
</div>

<!-- Unread Notifications -->
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden mb-6">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-bell mr-2 text-blue-500"></i> Unread Notifications
            {% if notifications %}
                <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">{{ notifications|length }}</span>
            {% endif %}
        </h3>
    </div>
    <div>
        {% if notifications %}
            <ul class="divide-y divide-gray-200 dark:divide-gray-700">
                {% for notification in notifications %}
                    <li class="relative hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" id="notification-{{ notification.id }}">
                        <div class="px-6 py-5 flex items-start">
                            <div class="flex-shrink-0 mr-4">
                                {% if notification.type == 'order' %}
                                    <div class="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-500 dark:text-blue-300">
                                        <i class="fas fa-shopping-cart"></i>
                                    </div>
                                {% elif notification.type == 'payment' %}
                                    <div class="w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-500 dark:text-green-300">
                                        <i class="fas fa-dollar-sign"></i>
                                    </div>
                                {% elif notification.type == 'report' %}
                                    <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-500 dark:text-yellow-300">
                                        <i class="fas fa-flag"></i>
                                    </div>
                                {% elif notification.type == 'ticket' %}
                                    <div class="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-500 dark:text-purple-300">
                                        <i class="fas fa-ticket-alt"></i>
                                    </div>
                                {% else %}
                                    <div class="w-10 h-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-300">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow">
                                <div class="flex justify-between">
                                    <h4 class="text-base font-medium text-gray-900 dark:text-white">{{ notification.title }}</h4>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">{{ notification.created_at|date:"M d, Y, h:i A" }}</span>
                                </div>
                                <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">{{ notification.message }}</p>
                                <div class="mt-2 flex space-x-3">
                                    {% if notification.link %}
                                        <a href="{% url 'mark_notification_read' notification.id %}" class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 dark:bg-blue-900 dark:text-blue-200 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors">
                                            <i class="fas fa-eye mr-1"></i> View
                                        </a>
                                    {% endif %}
                                    <button onclick="deleteNotification({{ notification.id }})" class="inline-flex items-center px-3 py-1 text-xs font-medium text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-200 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                                        <i class="fas fa-trash-alt mr-1"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            <div class="text-center py-10">
                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-700">
                    <i class="fas fa-check-circle text-2xl text-gray-400 dark:text-gray-500"></i>
                </div>
                <h5 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No unread notifications</h5>
                <p class="text-gray-500 dark:text-gray-400">You're all caught up!</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Read Notifications -->
{% if read_notifications %}
<div class="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-100 dark:border-gray-700 overflow-hidden">
    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-history mr-2 text-gray-500"></i> Previously Read
            <span class="ml-2 px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">{{ read_notifications|length }}</span>
        </h3>
    </div>
    <div>
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
            {% for notification in read_notifications %}
                <li class="relative hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors opacity-75" id="notification-{{ notification.id }}">
                    <div class="px-6 py-4 flex items-start">
                        <div class="flex-shrink-0 mr-4">
                            {% if notification.type == 'order' %}
                                <div class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-500 dark:text-blue-300">
                                    <i class="fas fa-shopping-cart text-sm"></i>
                                </div>
                            {% elif notification.type == 'payment' %}
                                <div class="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center text-green-500 dark:text-green-300">
                                    <i class="fas fa-dollar-sign text-sm"></i>
                                </div>
                            {% elif notification.type == 'report' %}
                                <div class="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-500 dark:text-yellow-300">
                                    <i class="fas fa-flag text-sm"></i>
                                </div>
                            {% elif notification.type == 'ticket' %}
                                <div class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-500 dark:text-purple-300">
                                    <i class="fas fa-ticket-alt text-sm"></i>
                                </div>
                            {% else %}
                                <div class="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-300">
                                    <i class="fas fa-bell text-sm"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between">
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">{{ notification.title }}</h4>
                                <span class="text-xs text-gray-500 dark:text-gray-400">{{ notification.created_at|date:"M d, Y" }}</span>
                            </div>
                            <p class="mt-1 text-xs text-gray-600 dark:text-gray-300">{{ notification.message }}</p>
                            <div class="mt-2">
                                <button onclick="deleteNotification({{ notification.id }})" class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 dark:bg-red-900 dark:text-red-200 rounded-lg hover:bg-red-200 dark:hover:bg-red-800 transition-colors">
                                    <i class="fas fa-trash-alt mr-1"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </li>
            {% endfor %}
        </ul>
    </div>
</div>
{% endif %}

<script>
    function deleteNotification(notificationId) {
        if (confirm('Are you sure you want to delete this notification?')) {
            fetch(`/notifications/${notificationId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const notificationElement = document.getElementById(`notification-${notificationId}`);
                    if (notificationElement) {
                        notificationElement.remove();
                    }
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the notification.');
            });
        }
    }
</script>
{% endblock %}
