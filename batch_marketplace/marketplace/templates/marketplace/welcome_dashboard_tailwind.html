{% extends 'marketplace/base.html' %}

{% block title %}Welcome to Oleer Market{% endblock %}

{% block content %}
<div class="p-4 sm:p-6 lg:p-8 mt-2">
    <!-- Welcome Header -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-home mr-3 text-blue-500"></i>
                    Welcome, {{ user.username }}!
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                    {% if user.profile.role == 'buyer' %}
                        Browse and purchase digital products with ease
                    {% elif user.profile.role == 'seller' %}
                        Manage your digital products and sales
                    {% elif user.profile.role == 'support' %}
                        Handle support tickets and user reports
                    {% elif user.profile.role == 'admin' %}
                        Manage the marketplace and user accounts
                    {% endif %}
                </p>
            </div>
            <div class="flex flex-wrap gap-2">
                {% if user.profile.role == 'buyer' %}
                    <a href="{% url 'vps_list' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-md transition-all duration-300">
                        <i class="fas fa-shopping-cart mr-2"></i> Shop Now
                    </a>
                {% elif user.profile.role == 'seller' %}
                    <a href="{% url 'add_vps_product' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 focus:ring-4 focus:ring-emerald-200 shadow-md transition-all duration-300">
                        <i class="fas fa-plus-circle mr-2"></i> Add Product
                    </a>
                    <a href="{% url 'seller_dashboard' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-md transition-all duration-300">
                        <i class="fas fa-store mr-2"></i> My Store
                    </a>
                {% elif user.profile.role == 'support' %}
                    <a href="{% url 'ticket_list' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-md transition-all duration-300">
                        <i class="fas fa-ticket-alt mr-2"></i> View Tickets
                    </a>
                    <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 focus:ring-4 focus:ring-purple-200 shadow-md transition-all duration-300">
                        <i class="fas fa-bullhorn mr-2"></i> Announcements
                    </a>
                {% elif user.profile.role == 'admin' %}
                    <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-md transition-all duration-300">
                        <i class="fas fa-shield-alt mr-2"></i> Admin Panel
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mb-8">
        {% if user.profile.role == 'buyer' %}
            <!-- Buyer Stats -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-blue-400 to-indigo-600 group-hover:from-blue-500 group-hover:to-indigo-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-shopping-cart text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-blue-100">Total Orders</p>
                            <p class="text-2xl font-bold text-white">{{ total_orders|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'buyer_dashboard' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300 group">
                        View Details <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-emerald-400 to-teal-600 group-hover:from-emerald-500 group-hover:to-teal-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-wallet text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-emerald-100">Balance</p>
                            <p class="text-2xl font-bold text-white">${{ request.user.profile.balance|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    {% if user.profile.role == 'buyer' %}
                    <a href="{% url 'topup_balance' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-sm transition-all duration-300 group">
                        Manage Balance <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                    {% endif %}
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-rose-400 to-pink-600 group-hover:from-rose-500 group-hover:to-pink-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-money-bill-wave text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-rose-100">Total Paid</p>
                            <p class="text-2xl font-bold text-white">${{ total_paid|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-200 rounded-lg">
                        Lifetime Spending
                    </span>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-amber-400 to-orange-600 group-hover:from-amber-500 group-hover:to-orange-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-flag text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-amber-100">Total Reports</p>
                            <p class="text-2xl font-bold text-white">{{ total_reports|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'user_reports' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:from-amber-600 hover:to-orange-700 shadow-sm transition-all duration-300 group">
                        View Reports <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        {% elif user.profile.role == 'seller' %}
            <!-- Seller Stats -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-blue-400 to-indigo-600 group-hover:from-blue-500 group-hover:to-indigo-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-shopping-cart text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-blue-100">Total Paid Orders</p>
                            <p class="text-2xl font-bold text-white">{{ total_paid_orders|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'seller_dashboard' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-sm transition-all duration-300 group">
                        View Details <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-emerald-400 to-teal-600 group-hover:from-emerald-500 group-hover:to-teal-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-money-bill-wave text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-emerald-100">Total Gained Balance</p>
                            <p class="text-2xl font-bold text-white">${{ total_gained_balance|floatformat:2 }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'withdrawal_request' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-sm transition-all duration-300 group">
                        Withdraw Funds <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-amber-400 to-orange-600 group-hover:from-amber-500 group-hover:to-orange-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-flag text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-amber-100">Total Reports</p>
                            <p class="text-2xl font-bold text-white">{{ total_reports|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'user_reports' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:from-amber-600 hover:to-orange-700 shadow-sm transition-all duration-300 group">
                        View Reports <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-rose-400 to-pink-600 group-hover:from-rose-500 group-hover:to-pink-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-exclamation-circle text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-rose-100">Open Reports</p>
                            <p class="text-2xl font-bold text-white">{{ open_reports_count|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-200 rounded-lg">
                        Pending Resolution
                    </span>
                </div>
            </div>
        {% else %}
            <!-- Admin/Support Stats -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-red-400 to-red-600 group-hover:from-red-500 group-hover:to-red-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-user-circle text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-red-100">Account Type</p>
                            <p class="text-2xl font-bold text-white">{{ user.profile.role|title }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <span class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 rounded-lg">
                        Staff Account
                    </span>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-purple-400 to-indigo-600 group-hover:from-purple-500 group-hover:to-indigo-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-bullhorn text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-purple-100">Announcements</p>
                            <p class="text-xl font-bold text-white">Manage News</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'announcement_list' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 shadow-sm transition-all duration-300 group">
                        View Announcements <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-cyan-400 to-sky-600 group-hover:from-cyan-500 group-hover:to-sky-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-ticket-alt text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-cyan-100">Support Tickets</p>
                            <p class="text-2xl font-bold text-white">{{ open_tickets.count|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'ticket_list' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-cyan-500 to-sky-600 rounded-lg hover:from-cyan-600 hover:to-sky-700 shadow-sm transition-all duration-300 group">
                        View Tickets <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-amber-400 to-orange-600 group-hover:from-amber-500 group-hover:to-orange-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-flag text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-amber-100">Open Reports</p>
                            <p class="text-2xl font-bold text-white">{{ total_reports|default:"0" }}</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'user_reports' %}?status=open" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:from-amber-600 hover:to-orange-700 shadow-sm transition-all duration-300 group">
                        View Reports <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- News & Announcements Section -->
    {% if active_announcements %}
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-8">
        <div class="border-b border-gray-200 px-6 py-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-bullhorn mr-2 text-purple-500"></i> News & Announcements
            </h3>
        </div>
        <div class="divide-y divide-gray-100">
            {% for announcement in active_announcements %}
            <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                <div class="flex">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-12 h-12 rounded-full {{ announcement.bg_color }} flex items-center justify-center text-white">
                            <i class="fas {{ announcement.icon }} text-xl"></i>
                        </div>
                    </div>
                    <div class="flex-grow">
                        <div class="flex justify-between items-start mb-1">
                            <h4 class="text-lg font-semibold text-gray-900">{{ announcement.title }}</h4>
                            <span class="text-sm text-gray-500">{{ announcement.created_at|timesince }} ago</span>
                        </div>
                        <p class="text-gray-700 mb-2">
                            {{ announcement.content }}
                        </p>
                        <div class="flex justify-between items-center">
                            <span class="{% if announcement.priority == 'high' %}bg-red-100 text-red-800{% elif announcement.priority == 'medium' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %} text-xs font-medium px-3 py-1 rounded-full">
                                {{ announcement.get_priority_display }}
                            </span>
                            <span class="text-sm text-gray-500">
                                Posted by {{ announcement.created_by.username }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if user.profile.role == 'support' %}
    <!-- Support Dashboard Content -->
    <!-- Open Tickets and Reports Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Open Tickets -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
            <div class="border-b border-gray-200 px-6 py-4 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-ticket-alt mr-2 text-blue-500"></i> Open Tickets <span class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">{{ open_tickets.count }}</span>
                </h3>
                <a href="{% url 'ticket_list' %}?status=open" class="text-sm font-medium text-blue-600 hover:text-blue-800">
                    View All
                </a>
            </div>
            <div>
                {% if open_tickets %}
                    <div class="divide-y divide-gray-200">
                        {% for ticket in open_tickets|slice:":5" %}
                            <a href="{% url 'ticket_detail' ticket.id %}" class="block hover:bg-gray-50 transition-colors">
                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h4 class="text-sm font-medium text-gray-900">{{ ticket.subject }}</h4>
                                        <span class="text-xs text-gray-500">{{ ticket.created_at|date:"M d, Y" }}</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2 truncate">{{ ticket.description|truncatechars:100 }}</p>
                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                {{ ticket.get_category_display }}
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-500">From: {{ ticket.user.username }}</span>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="mb-4 text-green-500">
                            <i class="fas fa-check-circle text-5xl"></i>
                        </div>
                        <h5 class="text-xl font-semibold text-gray-900 mb-2">No open tickets</h5>
                        <p class="text-gray-500">All tickets have been addressed</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Open Reports -->
        <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
            <div class="border-b border-gray-200 px-6 py-4">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2 text-yellow-500"></i> Open Reports <span class="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">{{ open_reports.count }}</span>
                </h3>
                <a href="{% url 'user_reports' %}?status=open" class="text-sm font-medium text-yellow-600 hover:text-yellow-800">
                    View All
                </a>
            </div>
            <div>
                {% if open_reports %}
                    <div class="divide-y divide-gray-200">
                        {% for report in open_reports|slice:":5" %}
                            <a href="{% url 'order_report' report.order_item.id %}" class="block hover:bg-gray-50 transition-colors">
                                <div class="p-4">
                                    <div class="flex justify-between items-start mb-1">
                                        <h4 class="text-sm font-medium text-gray-900">Report #{{ report.id }} - {{ report.order_item.product.title }}</h4>
                                        <span class="text-xs text-gray-500">{{ report.created_at|date:"M d, Y" }}</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2 truncate">{{ report.issue|truncatechars:100 }}</p>
                                    <div class="flex justify-between items-center">
                                        <div class="flex space-x-2">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">
                                                Unresolved
                                            </span>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                                Order #{{ report.order_item.order.id }}
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-500">
                                            Buyer: {{ report.order_item.order.buyer.username }} |
                                            Seller: {{ report.order_item.product.seller.username }}
                                        </span>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="mb-4 text-green-500">
                            <i class="fas fa-check-circle text-5xl"></i>
                        </div>
                        <h5 class="text-xl font-semibold text-gray-900 mb-2">No open reports</h5>
                        <p class="text-gray-500">All reports have been resolved</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}

    {% if user.profile.role == 'buyer' or user.profile.role == 'seller' %}
    <!-- Recent Activity Feed -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-8">
        <div class="border-b border-gray-200 px-6 py-4">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-history mr-2 text-blue-500"></i> Recent Activity
            </h3>
        </div>
        <div class="divide-y divide-gray-100">
            <!-- Last Order -->
            {% if recent_orders %}
                {% with last_order=recent_orders.0 %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white">
                                <i class="fas fa-shopping-cart text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between items-start mb-1">
                                <h4 class="text-lg font-semibold text-gray-900">Last Order</h4>
                                <span class="text-sm text-gray-500">
                                    {% if user.profile.role == 'buyer' %}
                                        {{ last_order.created_at|timesince }} ago
                                    {% else %}
                                        {{ last_order.order.created_at|timesince }} ago
                                    {% endif %}
                                </span>
                            </div>
                            <p class="text-gray-700 mb-2">
                                {% if user.profile.role == 'buyer' %}
                                    Order #{{ last_order.id }} - ${{ last_order.total_amount }}
                                {% else %}
                                    Product: {{ last_order.product.title }} - ${{ last_order.price }}
                                {% endif %}
                            </p>
                            <div class="flex justify-between items-center">
                                <span class="{% if user.profile.role == 'buyer' %}
                                    {% if last_order.order_status == 'paid' %}bg-green-100 text-green-800
                                    {% elif last_order.order_status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif last_order.order_status == 'delivered' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}
                                {% else %}
                                    {% if last_order.status == 'paid' %}bg-green-100 text-green-800
                                    {% elif last_order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif last_order.status == 'delivered' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}
                                {% endif %} text-xs font-medium px-3 py-1 rounded-full">
                                    {% if user.profile.role == 'buyer' %}
                                        {{ last_order.order_status }}
                                    {% else %}
                                        {{ last_order.status }}
                                    {% endif %}
                                </span>
                                <a href="{% if user.profile.role == 'buyer' %}{% url 'buyer_dashboard' %}{% else %}{% url 'seller_dashboard' %}{% endif %}"
                                   class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-200 rounded-lg hover:bg-blue-50">
                                    <i class="fas fa-eye mr-1"></i> View Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endwith %}
            {% else %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500">
                                <i class="fas fa-shopping-cart text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">
                                {% if user.profile.role == 'buyer' %}No Orders Yet{% else %}No Sales Yet{% endif %}
                            </h4>
                            <p class="text-gray-700 mb-3">
                                {% if user.profile.role == 'buyer' %}
                                    You haven't placed any orders yet.
                                {% else %}
                                    You haven't sold any items yet.
                                {% endif %}
                            </p>
                            {% if user.profile.role == 'buyer' %}
                                <a href="{% url 'vps_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg hover:bg-blue-600">
                                    <i class="fas fa-shopping-cart mr-2"></i> Browse Products
                                </a>
                            {% else %}
                                <a href="{% url 'add_vps_product' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-500 rounded-lg hover:bg-green-600">
                                    <i class="fas fa-plus-circle mr-2"></i> Add New Item
                                </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Last Balance Top-up - Only for buyers -->
            {% if user.profile.role == 'buyer' %}
                {% if last_topup %}
                    <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <div class="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center text-white">
                                    <i class="fas fa-wallet text-xl"></i>
                                </div>
                            </div>
                            <div class="flex-grow">
                                <div class="flex justify-between items-start mb-1">
                                    <h4 class="text-lg font-semibold text-gray-900">Last Balance Top-up</h4>
                                    <span class="text-sm text-gray-500">{{ last_topup.created_at|timesince }} ago</span>
                                </div>
                                <p class="text-gray-700 mb-2">
                                    ${{ last_topup.amount }} -
                                    {% if last_topup.verified %}
                                        Verified
                                    {% else %}
                                        Pending Verification
                                    {% endif %}
                                </p>
                                <div class="flex justify-end">
                                    <a href="{% url 'topup_balance' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 bg-white border border-green-200 rounded-lg hover:bg-green-50">
                                        <i class="fas fa-plus-circle mr-1"></i> Top Up Again
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-4">
                                <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500">
                                    <i class="fas fa-wallet text-xl"></i>
                                </div>
                            </div>
                            <div class="flex-grow">
                                <h4 class="text-lg font-semibold text-gray-900 mb-1">No Balance Top-ups</h4>
                                <p class="text-gray-700 mb-3">You haven't topped up your balance yet.</p>
                                <a href="{% url 'topup_balance' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-500 rounded-lg hover:bg-green-600">
                                    <i class="fas fa-plus-circle mr-2"></i> Top Up Balance
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            {% endif %}

            <!-- Last Report -->
            {% if recent_reports %}
                {% with last_report=recent_reports.0 %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-yellow-500 flex items-center justify-center text-white">
                                <i class="fas fa-flag text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between items-start mb-1">
                                <h4 class="text-lg font-semibold text-gray-900">Last Report</h4>
                                <span class="text-sm text-gray-500">{{ last_report.created_at|timesince }} ago</span>
                            </div>
                            <p class="text-gray-700 mb-2">
                                Report #{{ last_report.id }} -
                                {% if last_report.order_item.product.login_url %}
                                    {{ last_report.order_item.product.login_url }}
                                {% else %}
                                    {{ last_report.order_item.product.title }}
                                {% endif %}
                            </p>
                            <div class="flex justify-between items-center">
                                <span class="{% if last_report.resolved %}
                                    {% if last_report.decision == 'refunded' %}bg-green-100 text-green-800
                                    {% elif last_report.decision == 'rejected' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}
                                {% else %}
                                    bg-yellow-100 text-yellow-800
                                {% endif %} text-xs font-medium px-3 py-1 rounded-full">
                                    {% if last_report.resolved %}
                                        {% if last_report.decision == 'refunded' %}Refunded
                                        {% elif last_report.decision == 'rejected' %}Rejected
                                        {% else %}Resolved{% endif %}
                                    {% else %}
                                        Pending
                                    {% endif %}
                                </span>
                                <a href="{% url 'order_report' item_id=last_report.order_item.id %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-yellow-600 bg-white border border-yellow-200 rounded-lg hover:bg-yellow-50">
                                    <i class="fas fa-eye mr-1"></i> View Report
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endwith %}
            {% else %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500">
                                <i class="fas fa-flag text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">No Reports</h4>
                            <p class="text-gray-700 mb-3">
                                {% if user.profile.role == 'buyer' %}
                                    You haven't reported any issues yet.
                                {% else %}
                                    No reports have been filed against your products.
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Last Support Ticket -->
            {% if recent_tickets %}
                {% with last_ticket=recent_tickets.0 %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gray-500 flex items-center justify-center text-white">
                                <i class="fas fa-ticket-alt text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between items-start mb-1">
                                <h4 class="text-lg font-semibold text-gray-900">Last Support Ticket</h4>
                                <span class="text-sm text-gray-500">{{ last_ticket.created_at|timesince }} ago</span>
                            </div>
                            <p class="text-gray-700 mb-2">
                                {{ last_ticket.subject }}
                            </p>
                            <div class="flex justify-between items-center">
                                <span class="{% if last_ticket.status == 'open' %}bg-blue-100 text-blue-800
                                    {% elif last_ticket.status == 'in_progress' %}bg-purple-100 text-purple-800
                                    {% elif last_ticket.status == 'waiting' %}bg-yellow-100 text-yellow-800
                                    {% elif last_ticket.status == 'resolved' %}bg-green-100 text-green-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %} text-xs font-medium px-3 py-1 rounded-full">
                                    {{ last_ticket.get_status_display }}
                                </span>
                                <a href="{% url 'ticket_detail' ticket_id=last_ticket.id %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-200 rounded-lg hover:bg-gray-50">
                                    <i class="fas fa-eye mr-1"></i> View Ticket
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endwith %}
            {% else %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500">
                                <i class="fas fa-ticket-alt text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">No Support Tickets</h4>
                            <p class="text-gray-700 mb-3">You haven't created any support tickets yet.</p>
                            <a href="{% url 'create_ticket' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-gray-500 rounded-lg hover:bg-gray-600">
                                <i class="fas fa-plus-circle mr-2"></i> Create Ticket
                            </a>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Last Notification -->
            {% if recent_notifications %}
                {% with last_notification=recent_notifications.0 %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-red-500 flex items-center justify-center text-white">
                                <i class="fas fa-bell text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between items-start mb-1">
                                <h4 class="text-lg font-semibold text-gray-900">Last Notification</h4>
                                <span class="text-sm text-gray-500">{{ last_notification.created_at|timesince }} ago</span>
                            </div>
                            <p class="text-gray-700 mb-2">
                                {{ last_notification.title }}
                            </p>
                            <p class="text-gray-600 text-sm mb-3">
                                {{ last_notification.message|truncatechars:100 }}
                            </p>
                            <div class="flex justify-end">
                                {% if last_notification.link %}
                                <a href="{{ last_notification.link }}" class="inline-flex items-center px-4 py-2 text-sm font-medium text-red-600 bg-white border border-red-200 rounded-lg hover:bg-red-50">
                                    <i class="fas fa-eye mr-1"></i> View
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endwith %}
            {% else %}
                <div class="p-5 hover:bg-gray-50 transition-colors duration-150">
                    <div class="flex">
                        <div class="flex-shrink-0 mr-4">
                            <div class="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-gray-500">
                                <i class="fas fa-bell text-xl"></i>
                            </div>
                        </div>
                        <div class="flex-grow">
                            <h4 class="text-lg font-semibold text-gray-900 mb-1">No Notifications</h4>
                            <p class="text-gray-700 mb-3">You don't have any notifications yet.</p>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- View All Activity Button -->
            <div class="p-5 text-center">
                <a href="{% if user.profile.role == 'buyer' %}{% url 'buyer_dashboard' %}{% else %}{% url 'seller_dashboard' %}{% endif %}"
                   class="inline-flex items-center px-5 py-2.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 shadow-sm">
                    <i class="fas fa-history mr-2"></i> View All Activity
                </a>
            </div>
        </div>
    </div>
    {% else %}
    <!-- Quick Access Cards for admin role only -->
    <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Access</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-5 mb-8">
        {% if user.profile.role == 'admin' %}
            <!-- Admin Quick Access Cards -->
            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-purple-400 to-indigo-600 group-hover:from-purple-500 group-hover:to-indigo-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-shield-alt text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-purple-100">Admin Dashboard</p>
                            <p class="text-xl font-bold text-white">Manage Marketplace</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-500 to-indigo-600 rounded-lg hover:from-purple-600 hover:to-indigo-700 shadow-sm transition-all duration-300 group">
                        Go to Dashboard <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-emerald-400 to-teal-600 group-hover:from-emerald-500 group-hover:to-teal-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-users-cog text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-emerald-100">User Management</p>
                            <p class="text-xl font-bold text-white">Manage Accounts</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'manage_user_roles' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg hover:from-emerald-600 hover:to-teal-700 shadow-sm transition-all duration-300 group">
                        Manage Users <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 hover:shadow-lg transition-all duration-300 group">
                <div class="p-6 bg-gradient-to-br from-amber-400 to-orange-600 group-hover:from-amber-500 group-hover:to-orange-700 transition-all duration-300">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-white/20 backdrop-blur-sm mr-4 shadow-inner">
                            <i class="fas fa-money-bill-wave text-2xl text-white"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-amber-100">Withdrawal Requests</p>
                            <p class="text-xl font-bold text-white">Manage Payments</p>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 group-hover:bg-gray-100 transition-colors duration-300">
                    <a href="{% url 'withdrawal_list' %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg hover:from-amber-600 hover:to-orange-700 shadow-sm transition-all duration-300 group">
                        View Withdrawals <i class="fas fa-arrow-right ml-1 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}
