{% extends 'marketplace/base.html' %}

{% block title %}Verify Payment - Oleer Market{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'index' %}" class="text-gray-700 hover:text-blue-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <a href="{% url 'admin_dashboard' %}" class="text-gray-700 hover:text-blue-600">Admin Dashboard</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2 text-xs"></i>
                    <span class="text-gray-500">Verify Payment</span>
                </div>
            </li>
        </ol>
    </nav>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <!-- Main Card -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-check-circle mr-2 text-blue-500"></i>
                        Payment Verification
                    </h2>
                </div>
                <div class="p-6">
                    <!-- Info Alert -->
                    <div class="mb-6 p-4 rounded-md bg-blue-50 text-blue-800">
                        <div class="flex">
                            <div class="flex-shrink-0 mr-3">
                                <i class="fas fa-info-circle text-2xl text-blue-500"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-medium mb-1">Payment Verification Request</h3>
                                <p>Please verify that the USDT TRC-20 transaction is valid and matches the order amount.</p>
                                <p class="mt-1">Once verified, the order will be marked as paid and the seller can proceed with delivery.</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order & Payment Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-800 mb-3">Order Information</h3>
                            <p class="mb-2"><span class="font-semibold">Order #:</span> {{ payment.order.id }}</p>
                            <p class="mb-2"><span class="font-semibold">Date:</span> {{ payment.order.created_at|date:"M d, Y" }}</p>
                            <p class="mb-2"><span class="font-semibold">Buyer:</span> {{ payment.order.buyer.username }}</p>
                            <div class="mb-2">
                                <span class="font-semibold">Items:</span> 
                                {% for item in payment.order.items.all %}
                                    <div class="ml-4 mt-1">{{ item.product.title }}</div>
                                {% endfor %}
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-800 mb-3">Payment Information</h3>
                            <p class="mb-2"><span class="font-semibold">Amount:</span> ${{ payment.amount }} USDT</p>
                            <p class="mb-2"><span class="font-semibold">Submitted:</span> {{ payment.created_at|date:"M d, Y" }}</p>
                            <div class="mb-3">
                                <span class="font-semibold block mb-1">TXID:</span>
                                <div class="flex items-center">
                                    <code id="txid" class="bg-gray-100 px-3 py-1 rounded text-sm font-mono break-all select-all">{{ payment.txid }}</code>
                                    <button id="copyTxidBtn" class="ml-2 px-2 py-1 bg-gray-200 hover:bg-gray-300 text-gray-700 rounded text-sm flex items-center transition-colors">
                                        <i class="far fa-copy mr-1"></i>Copy
                                    </button>
                                </div>
                            </div>
                            <a href="https://tronscan.org/#/transaction/{{ payment.txid }}" target="_blank" class="inline-flex items-center px-3 py-2 border border-blue-500 text-blue-500 rounded-md hover:bg-blue-50 transition-colors text-sm">
                                <i class="fas fa-external-link-alt mr-2"></i>View on TronScan
                            </a>
                        </div>
                    </div>
                    
                    <!-- Verification Form -->
                    <form method="post" id="verifyPaymentForm">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                            <div class="mb-6 p-4 rounded-md bg-red-50 text-red-800">
                                {% for error in form.non_field_errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <div class="flex items-start mb-3">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="verify" id="id_verify" 
                                            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                            {% if form.initial.verify %}checked{% endif %}>
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="id_verify" class="font-medium text-green-600 flex items-center">
                                            <i class="fas fa-check-circle mr-1"></i>Verify Payment
                                        </label>
                                        <p class="text-gray-500 mt-1">
                                            Check if the transaction is valid and matches the expected amount
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="flex items-start mb-3">
                                    <div class="flex items-center h-5">
                                        <input type="checkbox" name="reject" id="id_reject" 
                                            class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                                    </div>
                                    <div class="ml-3 text-sm">
                                        <label for="id_reject" class="font-medium text-red-600 flex items-center">
                                            <i class="fas fa-times-circle mr-1"></i>Reject Payment
                                        </label>
                                        <p class="text-gray-500 mt-1">
                                            Check if the transaction is invalid or doesn't match the expected amount
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="id_notes" class="block text-sm font-medium text-gray-700 mb-1">Notes (Optional)</label>
                            {{ form.notes }}
                            <p class="mt-1 text-sm text-gray-500">Add any notes about the verification process</p>
                        </div>
                        
                        <div class="flex justify-between">
                            <a href="{% url 'admin_dashboard' %}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                                <i class="fas fa-check-double mr-2"></i>Submit Verification
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Transaction Verification Guide -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden mt-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800">Transaction Verification Guide</h3>
                </div>
                <div class="p-6">
                    <ol class="list-decimal pl-5 space-y-2">
                        <li>
                            <strong>Check the transaction on TronScan</strong> using the "View on TronScan" button above
                        </li>
                        <li>
                            <strong>Verify the amount</strong> - It should match the order amount of ${{ payment.amount }} USDT
                        </li>
                        <li>
                            <strong>Confirm the recipient address</strong> - It should match the seller's wallet address
                        </li>
                        <li>
                            <strong>Check the transaction status</strong> - It should be "Confirmed" on the blockchain
                        </li>
                        <li>
                            <strong>Verify the token type</strong> - It should be USDT on the TRC-20 network
                        </li>
                    </ol>
                    
                    <div class="mt-4 p-4 rounded-md bg-yellow-50 text-yellow-800 flex">
                        <i class="fas fa-exclamation-triangle mr-3 text-yellow-500 mt-1"></i>
                        <div>
                            Always double-check transaction details carefully. Incorrect verification can lead to financial loss.
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar Cards -->
        <div class="space-y-6">
            <!-- Verification Status Card -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800">Verification Status</h3>
                </div>
                <div class="p-6">
                    <div class="text-center mb-4">
                        <img src="https://pixabay.com/get/gfb3657208945ef219759b140d8deebd4b7fc6a06cdbba905a97cbcd0f9d1461a9489a47b61d789db05f3efa308af372782490ef4eea5f11940ad6171f5569d78_1280.jpg" alt="Payment Verification" class="rounded-md mx-auto mb-3" style="max-height: 150px;">
                    </div>
                    
                    <div class="flex items-center mb-4">
                        <div class="mr-3 text-yellow-500">
                            <i class="fas fa-spinner text-2xl"></i>
                        </div>
                        <div>
                            <h4 class="font-medium">Awaiting Verification</h4>
                            <p class="text-sm text-gray-500">Transaction submitted by buyer</p>
                        </div>
                    </div>
                    
                    <div class="p-4 bg-gray-50 border border-gray-200 rounded-md">
                        <div class="flex">
                            <div class="mr-3 text-blue-500">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div>
                                <h4 class="font-medium">Automatic Verification</h4>
                                <p class="text-sm text-gray-600 mt-1">Our system has attempted to verify this transaction automatically. Please review the results and confirm manually.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Seller Information Card -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800">Seller Information</h3>
                </div>
                <div class="p-6">
                    <p class="mb-3"><span class="font-semibold">Seller:</span> {{ payment.order.items.first.product.seller.username }}</p>
                    <div>
                        <span class="font-semibold block mb-1">Wallet Address:</span>
                        <code class="bg-gray-100 px-3 py-2 rounded text-sm font-mono break-all block select-all">{{ payment.order.items.first.product.seller.profile.wallet_address }}</code>
                    </div>
                </div>
            </div>
            
            <!-- After Verification Card -->
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-800">After Verification</h3>
                </div>
                <div class="p-6">
                    <p class="font-medium mb-2">Once verified:</p>
                    <ul class="list-disc pl-5 mb-4 space-y-1 text-sm">
                        <li>Order status will change to "Paid"</li>
                        <li>Seller will be notified to deliver the VPS</li>
                        <li>Buyer will be able to communicate with the seller</li>
                    </ul>
                    <p class="font-medium mb-2">If rejected:</p>
                    <ul class="list-disc pl-5 space-y-1 text-sm">
                        <li>Order status will change to "Rejected"</li>
                        <li>Buyer will need to submit a new payment</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make checkboxes mutually exclusive
        const verifyCheckbox = document.getElementById('id_verify');
        const rejectCheckbox = document.getElementById('id_reject');
        
        verifyCheckbox.addEventListener('change', function() {
            if (this.checked) {
                rejectCheckbox.checked = false;
            }
        });
        
        rejectCheckbox.addEventListener('change', function() {
            if (this.checked) {
                verifyCheckbox.checked = false;
            }
        });
        
        // Copy TXID functionality
        const copyTxidBtn = document.getElementById('copyTxidBtn');
        const txid = document.getElementById('txid');
        
        copyTxidBtn.addEventListener('click', function() {
            navigator.clipboard.writeText(txid.textContent)
                .then(() => {
                    // Change button text temporarily
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                    this.classList.remove('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                    this.classList.add('bg-green-500', 'hover:bg-green-600', 'text-white');
                    
                    // Reset after 2 seconds
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.classList.remove('bg-green-500', 'hover:bg-green-600', 'text-white');
                        this.classList.add('bg-gray-200', 'hover:bg-gray-300', 'text-gray-700');
                    }, 2000);
                })
                .catch(err => {
                    console.error('Failed to copy: ', err);
                });
        });
        
        // Form validation
        document.getElementById('verifyPaymentForm').addEventListener('submit', function(e) {
            if (!verifyCheckbox.checked && !rejectCheckbox.checked) {
                e.preventDefault();
                alert('Please choose to either verify or reject the payment.');
            }
        });
    });
</script>
{% endblock %}
