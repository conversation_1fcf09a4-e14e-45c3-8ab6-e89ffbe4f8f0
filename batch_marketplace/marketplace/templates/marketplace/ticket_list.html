{% extends 'marketplace/base.html' %}

{% block title %}Support Tickets - Batch VPS Marketplace{% endblock %}

{% block content %}
<!-- Main Content -->
<div class="p-6 space-y-6">
    <!-- Header Section -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                    <i class="fas fa-ticket-alt mr-3 text-blue-500"></i>
                    Support Tickets
                </h1>
                <p class="mt-2 text-lg text-gray-600">
                    View and manage your support tickets
                </p>
            </div>
            <div>
                <a href="{% url 'create_ticket' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-md transition-all duration-300">
                    <i class="fas fa-plus mr-2"></i> Create New Ticket
                </a>
            </div>
        </div>
    </div>

    <!-- Tickets Table -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
        <div class="border-b border-gray-200 px-6 py-4">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div class="flex items-center">
                    <h3 class="text-lg font-semibold text-gray-900 mr-4">Tickets</h3>
                    <div class="flex space-x-2">
                        <button id="filterOpen" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-blue-500 text-white hover:bg-blue-600 transition-colors">
                            Open
                        </button>
                        <button id="filterResolved" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors">
                            Resolved
                        </button>
                        <button id="filterAll" class="px-3 py-1.5 text-xs font-medium rounded-lg bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors">
                            All
                        </button>
                    </div>
                </div>
                <div class="relative">
                    <div class="flex items-center">
                        <input type="text" id="ticketSearch" placeholder="Search tickets..." class="rounded-l-lg border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        <button type="button" class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-r-lg border border-l-0 border-gray-300 hover:bg-gray-200">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        {% if tickets %}
            <div class="overflow-x-auto">
                <table class="w-full" id="ticketsTable">
                    <thead class="bg-gray-50 border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        {% for ticket in tickets %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#{{ ticket.id }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ ticket.subject }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">{{ ticket.get_category_display }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {% if ticket.status == 'open' %}bg-blue-100 text-blue-800
                                        {% elif ticket.status == 'in_progress' %}bg-cyan-100 text-cyan-800
                                        {% elif ticket.status == 'waiting' %}bg-yellow-100 text-yellow-800
                                        {% elif ticket.status == 'resolved' %}bg-green-100 text-green-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ ticket.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ticket.created_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ ticket.updated_at|date:"M d, Y" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <a href="{% url 'ticket_detail' ticket.id %}" class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors">
                                        <i class="fas fa-eye mr-1"></i> View
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="mb-4 text-gray-400">
                    <i class="fas fa-ticket-alt text-5xl"></i>
                </div>
                <h5 class="text-xl font-semibold text-gray-900 mb-2">No tickets found</h5>
                <p class="text-gray-500 mb-6">You haven't created any support tickets yet</p>
                <a href="{% url 'create_ticket' %}" class="inline-flex items-center px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg hover:from-blue-600 hover:to-indigo-700 shadow-md transition-all duration-300">
                    <i class="fas fa-plus mr-2"></i> Create New Ticket
                </a>
            </div>
        {% endif %}
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('ticketSearch');
        const table = document.getElementById('ticketsTable');
        const filterOpenBtn = document.getElementById('filterOpen');
        const filterResolvedBtn = document.getElementById('filterResolved');
        const filterAllBtn = document.getElementById('filterAll');

        let currentFilter = 'open'; // Default filter

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const statusParam = urlParams.get('status');

        // Set initial filter based on URL parameter
        if (statusParam) {
            currentFilter = statusParam;
            updateFilterButtons(currentFilter);
        }

        // Apply initial filter
        applyFilters();

        // Search functionality
        if (searchInput && table) {
            searchInput.addEventListener('keyup', function() {
                applyFilters();
            });
        }

        // Filter buttons click handlers
        if (filterOpenBtn) {
            filterOpenBtn.addEventListener('click', function() {
                currentFilter = 'open';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('open');
            });
        }

        if (filterResolvedBtn) {
            filterResolvedBtn.addEventListener('click', function() {
                currentFilter = 'resolved';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('resolved');
            });
        }

        if (filterAllBtn) {
            filterAllBtn.addEventListener('click', function() {
                currentFilter = 'all';
                updateFilterButtons(currentFilter);
                applyFilters();
                updateURL('all');
            });
        }

        // Update filter button styles
        function updateFilterButtons(filter) {
            // Reset all buttons
            [filterOpenBtn, filterResolvedBtn, filterAllBtn].forEach(btn => {
                if (btn) {
                    btn.classList.remove('bg-blue-500', 'text-white');
                    btn.classList.add('bg-gray-200', 'text-gray-700');
                }
            });

            // Highlight active button
            if (filter === 'open' && filterOpenBtn) {
                filterOpenBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterOpenBtn.classList.add('bg-blue-500', 'text-white');
            } else if (filter === 'resolved' && filterResolvedBtn) {
                filterResolvedBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterResolvedBtn.classList.add('bg-blue-500', 'text-white');
            } else if (filter === 'all' && filterAllBtn) {
                filterAllBtn.classList.remove('bg-gray-200', 'text-gray-700');
                filterAllBtn.classList.add('bg-blue-500', 'text-white');
            }
        }

        // Apply both search and status filters
        function applyFilters() {
            if (!table) return;

            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const rows = table.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const statusCell = row.querySelector('td:nth-child(4)');
                const statusText = statusCell ? statusCell.textContent.toLowerCase().trim() : '';

                let showByStatus = true;

                // Apply status filter
                if (currentFilter === 'open') {
                    showByStatus = statusText.includes('open') || statusText.includes('in progress') || statusText.includes('waiting');
                } else if (currentFilter === 'resolved') {
                    showByStatus = statusText.includes('resolved') || statusText.includes('closed');
                }

                // Apply search filter
                const showBySearch = text.includes(searchTerm);

                // Show row only if it passes both filters
                row.style.display = (showByStatus && showBySearch) ? '' : 'none';
            });
        }

        // Update URL with filter parameter
        function updateURL(filter) {
            const url = new URL(window.location);
            url.searchParams.set('status', filter);
            window.history.pushState({}, '', url);
        }
    });
</script>
{% endblock %}
