from django.shortcuts import redirect
from django.urls import reverse
from django.utils.deprecation import MiddlewareMixin
from django.contrib import messages

class RoleMiddleware(MiddlewareMixin):
    """
    Middleware to handle role-based access to certain views
    """
    
    def process_request(self, request):
        """
        Check if user has the required role for accessing certain pages
        """
        if not request.user.is_authenticated:
            return None
        
        path = request.path
        
        # Skip middleware for non-role specific paths
        excluded_paths = [
            reverse('index'),
            reverse('login'),
            reverse('register'),
            reverse('logout'),
            reverse('vps_list'),
        ]
        for excluded in excluded_paths:
            if path.startswith(excluded):
                return None
        
        # Check if path starts with these role-specific URLs
        role_paths = {
            'buyer': ['/buyer/', '/order/'],
            'seller': ['/seller/'],
            'admin': ['/admin-dashboard/', '/verify-payment/', '/report/']
        }
        
        try:
            profile = request.user.profile
            user_role = profile.role
            
            # Check if user is accessing a path restricted to another role
            for role, paths in role_paths.items():
                if role != user_role:
                    for path_prefix in paths:
                        if path.startswith(path_prefix):
                            messages.error(request, f"Access denied. You need {role} privileges.")
                            
                            # Redirect to appropriate dashboard
                            if user_role == 'buyer':
                                return redirect('buyer_dashboard')
                            elif user_role == 'seller':
                                return redirect('seller_dashboard')
                            elif user_role == 'admin':
                                return redirect('admin_dashboard')
                            else:
                                return redirect('index')
        except Exception:
            # If profile doesn't exist or any other error, continue normally
            pass
        
        return None
