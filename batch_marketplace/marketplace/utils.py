import requests
import json
import hmac
import hashlib
from decimal import Decimal
import os
import time
from django.conf import settings
from django.urls import reverse
from django.utils import timezone

def verify_tron_transaction(txid, expected_amount):
    """
    Verify a USDT TRC-20 transaction on the Tron blockchain

    Args:
        txid (str): The transaction ID to verify
        expected_amount (Decimal): The expected payment amount

    Returns:
        dict: Verification result containing 'verified' boolean and 'message' string
    """
    api_key = settings.TRONSCAN_API_KEY

    if not api_key:
        return {
            'verified': False,
            'message': 'No API key configured for TronScan verification'
        }

    # Base URL for TronScan API
    base_url = "https://apilist.tronscan.org/api/transaction-info"

    try:
        # Make request to TronScan API
        response = requests.get(
            f"{base_url}?hash={txid}",
            headers={
                'TRON-PRO-API-KEY': api_key,
                'Content-Type': 'application/json'
            }
        )

        if response.status_code != 200:
            return {
                'verified': False,
                'message': f'API error: {response.status_code} - {response.text}'
            }

        data = response.json()

        # Check if transaction exists
        if not data or 'contractData' not in data:
            return {
                'verified': False,
                'message': 'Transaction not found or not valid'
            }

        # For TRC-20 tokens (USDT), we need to check if this is a token transfer
        is_trc20 = False
        contract_type = data.get('contractType', 0)

        if contract_type == 31:  # TriggerSmartContract
            # This is a smart contract interaction, could be TRC20 transfer
            is_trc20 = True

        if not is_trc20:
            return {
                'verified': False,
                'message': 'Not a TRC-20 token transfer'
            }

        # Check if this is confirmed
        confirmed = data.get('confirmed', False)
        if not confirmed:
            return {
                'verified': False,
                'message': 'Transaction not confirmed yet'
            }

        # Parse token amount (need to divide by 10^6 for USDT)
        amount_str = data.get('amount', '0')
        try:
            amount = Decimal(amount_str) / Decimal(1_000_000)  # USDT has 6 decimal places
        except Exception:
            amount = Decimal('0')

        # Compare amounts with some tolerance (0.01 USDT) to account for potential fee differences
        if abs(amount - expected_amount) > Decimal('0.01'):
            return {
                'verified': False,
                'message': f'Amount mismatch: expected {expected_amount}, got {amount}'
            }

        return {
            'verified': True,
            'message': 'Transaction verified successfully',
            'details': {
                'amount': str(amount),
                'timestamp': data.get('timestamp', 0),
                'block': data.get('block', 0)
            }
        }

    except Exception as e:
        return {
            'verified': False,
            'message': f'Verification error: {str(e)}'
        }


def nowpayments_api_request(endpoint, method='GET', data=None):
    """
    Make a request to the NOWPayments API

    Args:
        endpoint (str): API endpoint (without leading slash)
        method (str): HTTP method (GET, POST, etc.)
        data (dict): Data to send in the request body (for POST, PUT, etc.)

    Returns:
        dict: Response data or error information
    """
    api_key = settings.NOWPAYMENTS_API_KEY
    api_url = settings.NOWPAYMENTS_API_URL

    if not api_key:
        return {
            'success': False,
            'message': 'No API key configured for NOWPayments'
        }

    headers = {
        'x-api-key': api_key,
        'Content-Type': 'application/json'
    }

    url = f"{api_url}/{endpoint}"

    try:
        if method == 'GET':
            response = requests.get(url, headers=headers)
        elif method == 'POST':
            response = requests.post(url, headers=headers, json=data)
        elif method == 'PUT':
            response = requests.put(url, headers=headers, json=data)
        else:
            return {
                'success': False,
                'message': f'Unsupported HTTP method: {method}'
            }

        # Check if the request was successful
        if response.status_code in [200, 201]:
            return {
                'success': True,
                'details': response.json()
            }
        else:
            return {
                'success': False,
                'message': f'API error: {response.status_code} - {response.text}'
            }

    except Exception as e:
        return {
            'success': False,
            'message': f'API request error: {str(e)}'
        }


def create_nowpayments_payment(amount, currency='USD', order_id=None, order_description=None, ipn_callback_url=None):
    """
    Create a payment using NOWPayments API

    Args:
        amount (Decimal): Payment amount
        currency (str): Currency code (USD, EUR, etc.)
        order_id (str): Optional order ID for reference
        order_description (str): Optional order description
        ipn_callback_url (str): Optional callback URL for IPN

    Returns:
        dict: Payment information or error details
    """
    # Prepare payment data
    payment_data = {
        'price_amount': float(amount),
        'price_currency': currency,
        'pay_currency': settings.NOWPAYMENTS_PAYOUT_CURRENCY,
        'ipn_callback_url': ipn_callback_url,
        'order_id': order_id,
        'order_description': order_description or f'Balance top-up of {amount} {currency}'
    }

    # Debug print
    print(f"Creating NOWPayments payment with data: {payment_data}")
    print(f"API URL: {settings.NOWPAYMENTS_API_URL}/payment")
    print(f"API Key: {settings.NOWPAYMENTS_API_KEY[:5]}...{settings.NOWPAYMENTS_API_KEY[-5:]}")

    # Add test mode parameters when test mode is enabled
    if hasattr(settings, 'NOWPAYMENTS_TEST_MODE') and settings.NOWPAYMENTS_TEST_MODE:
        payment_data['is_fee_paid_by_user'] = False
        payment_data['case'] = 'success'  # Options: success, failed, partially_paid
        print(f"Test mode enabled with case: {payment_data['case']}")

    # Remove None values
    payment_data = {k: v for k, v in payment_data.items() if v is not None}

    # Make API request
    result = nowpayments_api_request('payment', method='POST', data=payment_data)

    # Debug print the result
    if result.get('success'):
        print(f"Payment created successfully with ID: {result.get('details', {}).get('payment_id')}")
        print(f"Payment address: {result.get('details', {}).get('pay_address')}")
    else:
        print(f"Payment creation failed: {result.get('message')}")

    return result


def verify_nowpayments_ipn_request(request_data, ipn_signature):
    """
    Verify the authenticity of an IPN request from NOWPayments

    Args:
        request_data (str): Raw request body as string
        ipn_signature (str): Signature from the X-NOWPayments-Sig header

    Returns:
        bool: True if the signature is valid, False otherwise
    """
    if not settings.NOWPAYMENTS_IPN_SECRET_KEY:
        return False

    # Create HMAC signature using the IPN secret key
    secret_key = settings.NOWPAYMENTS_IPN_SECRET_KEY.encode('utf-8')
    computed_signature = hmac.new(
        secret_key,
        request_data.encode('utf-8'),
        hashlib.sha512
    ).hexdigest()

    # Compare signatures
    return computed_signature == ipn_signature


def get_payment_status(payment_id):
    """
    Get the status of a payment from NOWPayments API

    Args:
        payment_id (str): Payment ID

    Returns:
        dict: Payment status information or error details
    """
    return nowpayments_api_request(f'payment/{payment_id}')


def get_available_currencies():
    """
    Get the list of available currencies from NOWPayments API

    Returns:
        dict: Available currencies or error details
    """
    return nowpayments_api_request('currencies')
