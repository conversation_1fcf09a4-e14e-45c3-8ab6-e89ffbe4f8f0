from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter(name='add_class')
def add_class(field, css_class):
    """Add a CSS class to a form field."""
    return field.as_widget(attrs={"class": css_class})

@register.filter(name='index')
def index(sequence, position):
    """Get an item from a sequence by position."""
    try:
        return sequence[position]
    except (IndexError, TypeError):
        return None

@register.filter(name='get')
def get(dictionary, key):
    """Get a value from a dictionary by key."""
    try:
        return dictionary.get(key, '')
    except (Attribute<PERSON><PERSON><PERSON>, TypeError):
        return ''
