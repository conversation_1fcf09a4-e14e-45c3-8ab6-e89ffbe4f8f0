from django import template
import re

register = template.Library()

@register.filter
def contains(value, arg):
    """
    Returns True if the value contains the argument.
    """
    if not value:
        return False
    return arg in value

@register.filter
def clean_url(value):
    """
    Extracts and cleans a URL from a string.
    Useful for extracting URLs from specifications.
    """
    if not value:
        return ""

    # Remove leading/trailing whitespace
    value = value.strip()

    # Extract URLs if they exist
    url_pattern = r'https?://\S+'
    urls = re.findall(url_pattern, value)

    if urls:
        return urls[0].strip()

    return value.strip()

@register.filter
def trim(value):
    """
    Trims whitespace from the beginning and end of a string.
    """
    if not value:
        return ""
    return value.strip()

@register.filter
def extract_url(value):
    """
    Extracts a URL from a string, regardless of format.
    Handles various formats like 'Proof Link: http://...' or just a URL.
    """
    if not value:
        return ""

    # Remove leading/trailing whitespace
    value = value.strip()

    # Check for "Proof Link:" format
    if 'Proof Link:' in value:
        # Extract the part after "Proof Link:"
        proof_link_part = value.split('Proof Link:')[1].strip()

        # Extract URL from this part
        url_pattern = r'https?://\S+'
        urls = re.findall(url_pattern, proof_link_part)

        if urls:
            return urls[0].strip()

        # If no URL found in the proof link part, return the proof link part itself
        return proof_link_part

    # If no "Proof Link:" format, try to extract any URL
    url_pattern = r'https?://\S+'
    urls = re.findall(url_pattern, value)

    if urls:
        return urls[0].strip()

    # If no URL found, return the original value
    return value.strip()
