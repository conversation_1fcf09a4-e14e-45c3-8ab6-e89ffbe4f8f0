from django import forms
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm
from django.contrib.auth.models import User
from .models import (
    UserProfile, Item, Order,
    OrderItem, Payment, ChatMessage,
    Report, ROLE_CHOICES,
    Ticket, TicketMessage, Notification,
    Withdrawal, Announcement
)

class UserRegistrationForm(UserCreationForm):
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    role = forms.ChoiceField(
        choices=[('buyer', 'Buyer'), ('seller', 'Seller')],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'})
    )
    wallet_address = forms.CharField(
        required=False,
        help_text="Required for sellers",
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )

    class Meta:
        model = User
        fields = ('email', 'password1', 'password2', 'role', 'wallet_address')

    def generate_unique_username(self, role):
        """Generate a unique username based on role with incremental number"""
        base_name = role.lower()
        from django.contrib.auth.models import User

        # Find the highest number for this role
        import re
        pattern = f"^{base_name}(\\d+)$"

        # Get all usernames that match the pattern (e.g., buyer1, buyer2, etc.)
        matching_users = User.objects.filter(username__regex=pattern)

        # Start with 1 or the highest existing number + 1
        new_number = 1

        if matching_users.exists():
            # Extract numbers from usernames and find the highest
            numbers = []
            for user in matching_users:
                match = re.match(pattern, user.username)
                if match:
                    try:
                        numbers.append(int(match.group(1)))
                    except (ValueError, IndexError):
                        pass

            if numbers:
                new_number = max(numbers) + 1

        # Create the new username
        username = f"{base_name}{new_number}"

        # Double-check it's unique (just in case)
        while User.objects.filter(username=username).exists():
            new_number += 1
            username = f"{base_name}{new_number}"

        return username

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes and placeholders to default fields
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '••••••••••••'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '••••••••••••'
        })

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email and User.objects.filter(email__iexact=email).exists():
            raise forms.ValidationError("This email is already registered. Please use a different email or login with your existing account.")
        return email

    def clean(self):
        cleaned_data = super().clean()
        role = cleaned_data.get('role')
        wallet_address = cleaned_data.get('wallet_address')

        if role == 'seller' and not wallet_address:
            raise forms.ValidationError("Wallet address is required for sellers")

        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        # Set email from form data
        user.email = self.cleaned_data['email']
        # Generate unique username based on role
        role = self.cleaned_data['role']
        user.username = self.generate_unique_username(role)

        if commit:
            try:
                user.save()
                # Create the user profile
                UserProfile.objects.create(
                    user=user,
                    role=role,
                    wallet_address=self.cleaned_data.get('wallet_address', '')
                )
            except Exception as e:
                # Log the error for debugging
                print(f"Error creating user or profile: {str(e)}")
                # Re-raise the exception to be handled by the view
                raise

        return user



class PaymentForm(forms.ModelForm):
    class Meta:
        model = Payment
        fields = ('txid',)
        widgets = {
            'txid': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your USDT TRC-20 transaction ID'
            })
        }

class TopUpBalanceForm(forms.Form):
    AMOUNT_CHOICES = [
        (10, '$10.00'),
        (25, '$25.00'),
        (50, '$50.00'),
        (100, '$100.00'),
        (250, '$250.00'),
        (500, '$500.00'),
        (1000, '$1000.00'),
    ]

    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=1.00,
        widget=forms.NumberInput(attrs={
            'class': 'w-full px-4 py-2 border border-gray-300 rounded-r-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter amount to add',
            'step': '0.01',
            'min': '1.00'
        })
    )

    amount_preset = forms.ChoiceField(
        choices=AMOUNT_CHOICES,
        required=False,
        widget=forms.RadioSelect(attrs={
            'class': 'sr-only amount-preset'
        })
    )

class TicketForm(forms.ModelForm):
    class Meta:
        model = Ticket
        fields = ('subject', 'description', 'category')
        widgets = {
            'subject': forms.TextInput(attrs={
                'class': 'w-full h-10 px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200',
                'placeholder': 'Enter a brief subject for your ticket'
            }),
            'description': forms.Textarea(attrs={
                'class': 'w-full min-h-[150px] px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200',
                'placeholder': 'Describe your issue in detail...'
            }),
            'category': forms.Select(attrs={
                'class': 'w-full h-10 px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 appearance-none bg-white'
            })
        }

class SupportTicketForm(TicketForm):
    user = forms.ModelChoiceField(
        queryset=User.objects.filter(profile__role__in=['buyer', 'seller']),
        widget=forms.Select(attrs={
            'class': 'w-full h-10 px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 appearance-none bg-white'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Update the queryset to show username in the dropdown
        self.fields['user'].label_from_instance = lambda obj: f"{obj.username} ({obj.profile.role})"

class TicketMessageForm(forms.ModelForm):
    class Meta:
        model = TicketMessage
        fields = ('message',)
        widgets = {
            'message': forms.Textarea(attrs={
                'class': 'w-full min-h-[100px] px-3 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200',
                'placeholder': 'Type your message here...'
            })
        }

class ChatMessageForm(forms.ModelForm):
    class Meta:
        model = ChatMessage
        fields = ('message',)
        widgets = {
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Type your message here...'
            })
        }

class ReportForm(forms.ModelForm):
    class Meta:
        model = Report
        fields = ('issue',)
        widgets = {
            'issue': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Describe your issue in detail...'
            })
        }

# Predefined messages for report resolutions
REPORT_REJECTION_MESSAGE = """Your report has been reviewed and rejected. We have determined that the product meets our quality standards and functions as described. The credentials provided are valid and have been verified by our support team. No refund will be issued for this purchase."""

REPORT_REFUND_MESSAGE = """Your report has been reviewed and accepted. We apologize for the inconvenience you experienced with this product. A full refund has been issued to your account balance. The refund should be reflected in your balance immediately."""

class ReportResolutionForm(forms.Form):
    resolution_notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter resolution details...',
            'id': 'resolution_notes'
        })
    )
    decision = forms.ChoiceField(
        choices=[
            ('rejected', 'Reject Report - No Refund'),
            ('refunded', 'Accept Report - Issue Refund')
        ],
        widget=forms.Select(attrs={
            'class': 'form-select',
            'id': 'decision_select'
        })
    )

class PaymentVerificationForm(forms.Form):
    verify = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'})
    )
    reject = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'rows': 3,
            'placeholder': 'Optional notes about verification'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        verify = cleaned_data.get('verify')
        reject = cleaned_data.get('reject')

        if verify and reject:
            raise forms.ValidationError("Cannot both verify and reject at the same time")

        if not verify and not reject:
            raise forms.ValidationError("Must choose to either verify or reject")

        return cleaned_data



class WithdrawalForm(forms.ModelForm):
    class Meta:
        model = Withdrawal
        fields = ('amount', 'wallet_address')
        widgets = {
            'amount': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter amount to withdraw',
                'min': '10.00',
                'step': '0.01'
            }),
            'wallet_address': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your USDT TRC-20 wallet address'
            })
        }

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount < 10:
            raise forms.ValidationError("Minimum withdrawal amount is $10.00")
        return amount

class WithdrawalProcessForm(forms.Form):
    action = forms.ChoiceField(
        choices=[
            ('approve', 'Approve Withdrawal'),
            ('reject', 'Reject Withdrawal')
        ],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'})
    )
    txid = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Transaction ID (required for approval)'
        })
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional notes'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        action = cleaned_data.get('action')
        txid = cleaned_data.get('txid')

        if action == 'approve' and not txid:
            raise forms.ValidationError("Transaction ID is required for approval")

        return cleaned_data

class UserRoleForm(forms.Form):
    role = forms.ChoiceField(
        choices=ROLE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional notes about role change'
        })
    )




class ItemForm(forms.ModelForm):
    """
    Form for adding or editing marketplace items.
    Includes fields for both VPS/RDP and Account items with dynamic display based on item type.
    """
    # Add a company field that will be stored in the specifications
    company = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company name (optional)'})
    )

    # Add a proof_link field that will be stored in the specifications for account items
    proof_link = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'Link to screenshot proof'})
    )

    class Meta:
        model = Item
        fields = [
            'title', 'description', 'specifications', 'price', 'image_url',
            'category', 'item_type', 'username', 'password',
            # VPS/RDP fields
            'login_url', 'ram',
            # Account fields
            'url', 'proof'
        ]
        # proof_link is a custom field that will be stored in specifications
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Enter item title'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Enter item description'}),
            'specifications': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'Enter specifications'}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'placeholder': '0.00', 'step': '0.01', 'min': '0.01'}),
            'image_url': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'https://example.com/image.jpg (optional)'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'item_type': forms.Select(attrs={'class': 'form-select', 'id': 'id_item_type'}),

            # Common fields
            'username': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Username'}),
            'password': forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Password'}),

            # VPS/RDP fields
            'login_url': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'IP address for VPS/RDP', 'id': 'id_login_url'}),
            'ram': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'RAM amount (e.g., 8GB)', 'id': 'id_ram'}),

            # Account fields
            'url': forms.URLInput(attrs={'class': 'form-control', 'placeholder': 'Login URL for account', 'id': 'id_url'}),
            'proof': forms.Textarea(attrs={'class': 'form-control', 'rows': 3, 'placeholder': 'Description of the account', 'id': 'id_proof'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default values
        self.initial['category'] = 'vps'
        self.initial['item_type'] = 'vps'

        # Set default values for hidden fields
        if not kwargs.get('instance'):
            self.initial['title'] = 'Item'
            self.initial['description'] = 'Item with secure access.'
            self.initial['specifications'] = 'Details will be shown here'
            self.initial['image_url'] = 'https://pixabay.com/get/gd12040adc427d19fbfa328eef8d5039f0da72fab034ff5df590c055037f0e3a4044e01fd2c159feeb8e71dda2193b71f0eeba9957dde638c9b7b76903387fe65_1280.jpg'

        # If we're editing an existing item, extract company and proof link from specifications
        if kwargs.get('instance'):
            instance = kwargs['instance']
            specs = instance.specifications

            # Extract company for VPS/RDP items
            if 'Company:' in specs:
                company_line = [line for line in specs.split('\n') if 'Company:' in line]
                if company_line:
                    self.initial['company'] = company_line[0].replace('Company:', '').strip()

            # Extract proof link for Account items
            if 'Proof Link:' in specs:
                proof_link_line = [line for line in specs.split('\n') if 'Proof Link:' in line]
                if proof_link_line:
                    self.initial['proof_link'] = proof_link_line[0].replace('Proof Link:', '').strip()

    def clean(self):
        cleaned_data = super().clean()
        item_type = cleaned_data.get('item_type')
        username = cleaned_data.get('username')
        password = cleaned_data.get('password')

        # Common validation for all item types
        if not username:
            raise forms.ValidationError("Username is required.")
        if not password:
            raise forms.ValidationError("Password is required.")

        # Type-specific validation
        if item_type == 'vps':
            login_url = cleaned_data.get('login_url')
            ram = cleaned_data.get('ram')

            if not login_url:
                raise forms.ValidationError("IP address is required for VPS/RDP items.")
            if not ram:
                raise forms.ValidationError("RAM amount is required for VPS/RDP items.")

        elif item_type == 'account':
            url = cleaned_data.get('url')
            proof = cleaned_data.get('proof')
            proof_link = cleaned_data.get('proof_link')

            if not url:
                raise forms.ValidationError("Login URL is required for Account items.")
            if not proof:
                raise forms.ValidationError("Description is required for Account items.")
            if not proof_link:
                raise forms.ValidationError("Screenshot proof link is required for Account items.")

            # Basic URL validation
            if not proof_link.startswith(('http://', 'https://')) and '.' not in proof_link:
                raise forms.ValidationError("Please enter a valid URL for the screenshot proof (e.g., https://imgur.com/abcd123)")

        return cleaned_data

    def save(self, commit=True):
        """Override save to add company to specifications and handle item type"""
        instance = super().save(commit=False)

        # Get form data
        item_type = self.cleaned_data.get('item_type')
        category = self.cleaned_data.get('category')
        company = self.cleaned_data.get('company')

        # Update title based on item type if it's the default title
        if instance.title == 'Item':
            if item_type == 'vps':
                login_url = self.cleaned_data.get('login_url')
                if login_url:
                    instance.title = f"{category.upper()} - {login_url}"
            else:  # account
                url = self.cleaned_data.get('url')
                if url:
                    # Extract domain from URL for account title
                    from urllib.parse import urlparse
                    try:
                        domain = urlparse(url).netloc
                        if domain:
                            instance.title = f"ACCOUNT - {domain}"
                        else:
                            instance.title = f"ACCOUNT - {url}"
                    except:
                        instance.title = f"ACCOUNT - {url}"

        # Update specifications
        specs = instance.specifications
        specs_lines = [line for line in specs.split('\n') if not line.startswith('Company:')]

        # Add item type to specifications
        specs_lines.append(f"Type: {dict(Item.ITEM_TYPE_CHOICES).get(item_type)}")

        # Add RAM for VPS/RDP items
        if item_type == 'vps' and self.cleaned_data.get('ram'):
            specs_lines.append(f"RAM: {self.cleaned_data.get('ram')}")

        # Add company only for VPS/RDP items
        if item_type == 'vps' and company:
            specs_lines.append(f"Company: {company}")

            # Also update the description if it's the default
            if instance.description == 'Item with secure access.':
                instance.description = f"VPS/RDP from {company} with secure access."
        elif item_type == 'account':
            # For accounts, add proof link to specifications
            proof_link = self.cleaned_data.get('proof_link')
            if proof_link:
                # Ensure the URL starts with http:// or https://
                if not proof_link.startswith(('http://', 'https://')):
                    proof_link = 'https://' + proof_link
                specs_lines.append(f"Proof Link: {proof_link}")

            # Use a different default description without company
            if instance.description == 'Item with secure access.':
                instance.description = f"Account with secure access."

        # Update specifications
        instance.specifications = '\n'.join(specs_lines)

        if commit:
            instance.save()

        return instance


class ProfileForm(forms.ModelForm):
    """Form for editing user profile information"""
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    wallet_address = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'USDT TRC-20 Wallet Address'
        })
    )

    class Meta:
        model = User
        fields = ('email',)

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        if self.user and hasattr(self.user, 'profile'):
            self.fields['wallet_address'].initial = self.user.profile.wallet_address

    def clean_email(self):
        email = self.cleaned_data.get('email')
        # Check if email exists but exclude the current user
        if email and User.objects.filter(email__iexact=email).exclude(pk=self.user.pk).exists():
            raise forms.ValidationError("This email is already registered by another user. Please use a different email.")
        return email

    def save(self, commit=True):
        user = super().save(commit=False)

        if commit:
            user.save()

            # Update profile wallet address
            if hasattr(user, 'profile'):
                user.profile.wallet_address = self.cleaned_data.get('wallet_address', '')
                user.profile.save()

        return user


class CustomPasswordChangeForm(PasswordChangeForm):
    """Custom password change form with Tailwind CSS styling"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Tailwind CSS classes to all fields
        self.fields['old_password'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Current Password'
        })
        self.fields['new_password1'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'New Password'
        })
        self.fields['new_password2'].widget.attrs.update({
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Confirm New Password'
        })


class AnnouncementForm(forms.ModelForm):
    """Form for creating and editing announcements"""

    # Define choices for icons with visual representation
    ICON_CHOICES = [
        ('fa-bullhorn', 'Bullhorn (Announcement)'),
        ('fa-gift', 'Gift (Promotion)'),
        ('fa-server', 'Server (Maintenance)'),
        ('fa-info-circle', 'Info Circle (Information)'),
        ('fa-exclamation-circle', 'Exclamation Circle (Warning)'),
        ('fa-bell', 'Bell (Notification)'),
        ('fa-calendar', 'Calendar (Event)'),
        ('fa-star', 'Star (Featured)'),
        ('fa-tag', 'Tag (Sale)'),
        ('fa-trophy', 'Trophy (Achievement)'),
    ]

    # Define choices for background colors with visual representation
    BG_COLOR_CHOICES = [
        ('bg-gradient-to-br from-purple-400 to-indigo-600', 'Purple to Indigo (Default)'),
        ('bg-gradient-to-br from-blue-400 to-indigo-600', 'Blue to Indigo'),
        ('bg-gradient-to-br from-green-400 to-emerald-600', 'Green to Emerald'),
        ('bg-gradient-to-br from-red-400 to-rose-600', 'Red to Rose'),
        ('bg-gradient-to-br from-amber-400 to-orange-600', 'Amber to Orange'),
        ('bg-gradient-to-br from-cyan-400 to-blue-600', 'Cyan to Blue'),
        ('bg-gradient-to-br from-pink-400 to-purple-600', 'Pink to Purple'),
        ('bg-gradient-to-br from-yellow-400 to-amber-600', 'Yellow to Amber'),
        ('bg-gradient-to-br from-teal-400 to-cyan-600', 'Teal to Cyan'),
        ('bg-gradient-to-br from-gray-400 to-gray-600', 'Gray (Neutral)'),
    ]

    # Override the default fields with our custom choices
    icon = forms.ChoiceField(
        choices=ICON_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
        })
    )

    bg_color = forms.ChoiceField(
        choices=BG_COLOR_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
        })
    )

    class Meta:
        model = Announcement
        fields = ['title', 'content', 'priority', 'is_active', 'icon', 'bg_color']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'Announcement Title'
            }),
            'content': forms.Textarea(attrs={
                'class': 'w-full min-h-[150px] px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                'placeholder': 'Announcement Content'
            }),
            'priority': forms.Select(attrs={
                'class': 'w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500'
            }),
        }