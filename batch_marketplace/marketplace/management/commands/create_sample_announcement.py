from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from marketplace.models import Announcement

class Command(BaseCommand):
    help = 'Creates a sample announcement for testing'

    def handle(self, *args, **options):
        # Find an admin or support user
        admin_user = User.objects.filter(profile__role__in=['admin', 'support']).first()
        
        if not admin_user:
            self.stdout.write(self.style.ERROR('No admin or support user found. Cannot create announcement.'))
            return
            
        # Create the announcement
        announcement = Announcement.objects.create(
            title='Welcome to Oleer Market!',
            content='Get $2 bonus for every $5 you deposit! Limited time offer. The server will be undergoing maintenance tomorrow from 2-4 AM UTC.',
            created_by=admin_user,
            priority='high',
            icon='fa-gift',
            bg_color='bg-gradient-to-br from-green-400 to-emerald-600'
        )
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created announcement: "{announcement.title}"'))
        
        # Create a second announcement
        announcement2 = Announcement.objects.create(
            title='Server Maintenance',
            content='The server will be undergoing maintenance tomorrow from 2-4 AM UTC. Please plan accordingly.',
            created_by=admin_user,
            priority='medium',
            icon='fa-server',
            bg_color='bg-gradient-to-br from-blue-400 to-indigo-600'
        )
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created announcement: "{announcement2.title}"'))
