"""
WSGI config for batch project.
"""

import os
import sys
from pathlib import Path

# Add the project root directory to Python's sys.path
CURRENT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = CURRENT_DIR.parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# Load environment variables from .env file if it exists
from dotenv import load_dotenv
env_path = os.path.join(PROJECT_ROOT, '.env')
if os.path.exists(env_path):
    print(f"Loading environment variables from {env_path}")
    load_dotenv(env_path)
else:
    print("No .env file found, using environment variables from the system")

# Print DATABASE_URL for debugging (will be visible in logs)
if 'DATABASE_URL' in os.environ:
    db_url = os.environ['DATABASE_URL']
    print(f"DATABASE_URL is set: {db_url[:20]}...")
else:
    print("WARNING: DATABASE_URL environment variable is not set")

# Print NOWPayments API key for debugging (will be visible in logs)
if 'NOWPAYMENTS_API_KEY' in os.environ:
    api_key = os.environ['NOWPAYMENTS_API_KEY']
    print(f"NOWPAYMENTS_API_KEY is set: {api_key[:5]}...{api_key[-5:] if len(api_key) > 10 else ''}")
else:
    print("WARNING: NOWPAYMENTS_API_KEY environment variable is not set, will use default sandbox key")

# Print NOWPayments test mode for debugging
if 'NOWPAYMENTS_TEST_MODE' in os.environ:
    test_mode = os.environ['NOWPAYMENTS_TEST_MODE']
    print(f"NOWPAYMENTS_TEST_MODE is set to: {test_mode}")
else:
    print("NOWPAYMENTS_TEST_MODE is not set, will default to True")

from django.core.wsgi import get_wsgi_application

# Use Railway settings when running on Railway
if os.environ.get('RAILWAY_ENVIRONMENT') == 'True':
    print("Running on Railway, using settings_railway.py")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'batch.settings_railway')
else:
    print("Not running on Railway, using settings.py")
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'batch.settings')

application = get_wsgi_application()
