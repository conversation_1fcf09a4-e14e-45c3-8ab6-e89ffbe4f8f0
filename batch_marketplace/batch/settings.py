"""
Django settings for batch project.
"""

import os
import dj_database_url
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('DJANGO_SECRET_KEY', 'django-insecure-1234567890abcdefghijklmnopqrstuvwxyz')

# SECURITY WARNING: don't run with debug turned on in production!
# Temporarily force DEBUG to True to get more detailed error information
DEBUG = True  # os.getenv('DEBUG', 'True') == 'True'

ALLOWED_HOSTS = ['0.0.0.0', 'localhost', '127.0.0.1', '.onrender.com', '*']

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'marketplace.apps.MarketplaceConfig',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'marketplace.middlewares.RoleMiddleware',
]

# Whitenoise settings
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

ROOT_URLCONF = 'batch.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'marketplace.context_processors.settings_context',
            ],
        },
    },
]

WSGI_APPLICATION = 'batch.wsgi.application'

# Database
DATABASE_URL = os.getenv('DATABASE_URL', None)

if DATABASE_URL:
    print(f"Using DATABASE_URL from environment: {DATABASE_URL[:20]}...")
    try:
        db_config = dj_database_url.parse(DATABASE_URL)
        print(f"Parsed database config: ENGINE={db_config.get('ENGINE')}, HOST={db_config.get('HOST')}, NAME={db_config.get('NAME')}")
        DATABASES = {
            'default': db_config
        }
    except Exception as e:
        print(f"Error parsing DATABASE_URL: {e}")
        # Fallback to SQLite if there's an error parsing the DATABASE_URL
        DATABASES = {
            'default': {
                'ENGINE': 'django.db.backends.sqlite3',
                'NAME': BASE_DIR / 'db.sqlite3',
            }
        }
else:
    print("WARNING: No DATABASE_URL provided, using SQLite")
    # Fallback to SQLite for development if no DATABASE_URL is provided
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_L10N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'marketplace/static'),
]

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Login redirect
LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/dashboard/'
LOGOUT_REDIRECT_URL = '/'

# Authentication backends
AUTHENTICATION_BACKENDS = [
    'marketplace.backends.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',
]

# TronScan API Key
TRONSCAN_API_KEY = os.getenv('TRONSCAN_API_KEY', '')

# NOWPayments API Settings
NOWPAYMENTS_API_KEY = os.getenv('NOWPAYMENTS_API_KEY', '3HSVSAY-PNB428X-QFZ7H5Q-BX3QDBW')  # Default to Sandbox API key
NOWPAYMENTS_TEST_MODE = os.getenv('NOWPAYMENTS_TEST_MODE', 'True').lower() == 'true'  # Get from env or default to True

# Set API URL based on test mode
if NOWPAYMENTS_TEST_MODE:
    NOWPAYMENTS_API_URL = 'https://api-sandbox.nowpayments.io/v1'  # Sandbox API URL
else:
    NOWPAYMENTS_API_URL = 'https://api.nowpayments.io/v1'  # Production API URL

NOWPAYMENTS_IPN_SECRET_KEY = os.getenv('NOWPAYMENTS_IPN_SECRET_KEY', 'eAbTkwDM0ou+TsseqNALdbHJegIP6ySd')  # Default to Sandbox IPN secret key
NOWPAYMENTS_PAYOUT_CURRENCY = 'USDTTRC20'  # Currency to receive payments (no underscore)
NOWPAYMENTS_WEBHOOK_URL = os.getenv('NOWPAYMENTS_WEBHOOK_URL', 'https://batch-marketplace-production.up.railway.app/nowpayments/webhook/')  # Public webhook URL

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# CSRF Trusted Origins
CSRF_TRUSTED_ORIGINS = ['https://*.serveo.net', 'https://*.onrender.com', 'https://*.railway.app']

# Email settings for SendGrid
EMAIL_BACKEND = 'marketplace.utils.sendgrid_backend.SendGridEmailBackend'
EMAIL_HOST_PASSWORD = os.getenv('SENDGRID_API_KEY', '')  # SendGrid API key
DEFAULT_FROM_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')  # Use the verified email address
SERVER_EMAIL = os.getenv('DEFAULT_FROM_EMAIL', '<EMAIL>')  # Used for error emails
DEFAULT_FROM_NAME = 'Online Marketplace'  # Used for the display name in emails

# Keep these settings for fallback to SMTP if needed
EMAIL_HOST = 'smtp.sendgrid.net'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = 'apikey'  # This is exactly the string 'apikey'
