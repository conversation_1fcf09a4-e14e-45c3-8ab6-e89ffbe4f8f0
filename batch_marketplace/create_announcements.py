import os
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'batch.settings')
django.setup()

# Import models after Django setup
from django.contrib.auth.models import User
from marketplace.models import Announcement

def create_sample_announcements():
    # Find an admin or support user
    admin_user = User.objects.filter(profile__role__in=['admin', 'support']).first()
    
    if not admin_user:
        print('No admin or support user found. Cannot create announcement.')
        return
        
    # Create the announcement
    announcement = Announcement.objects.create(
        title='Welcome to Oleer Market!',
        content='Get $2 bonus for every $5 you deposit! Limited time offer. The server will be undergoing maintenance tomorrow from 2-4 AM UTC.',
        created_by=admin_user,
        priority='high',
        icon='fa-gift',
        bg_color='bg-gradient-to-br from-green-400 to-emerald-600'
    )
    
    print(f'Successfully created announcement: "{announcement.title}"')
    
    # Create a second announcement
    announcement2 = Announcement.objects.create(
        title='Server Maintenance',
        content='The server will be undergoing maintenance tomorrow from 2-4 AM UTC. Please plan accordingly.',
        created_by=admin_user,
        priority='medium',
        icon='fa-server',
        bg_color='bg-gradient-to-br from-blue-400 to-indigo-600'
    )
    
    print(f'Successfully created announcement: "{announcement2.title}"')

if __name__ == '__main__':
    create_sample_announcements()
